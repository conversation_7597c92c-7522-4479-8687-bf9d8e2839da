#!/bin/bash

# Script to produce test data to IP classification service
# Make sure <PERSON><PERSON><PERSON> is running on localhost:19092

KAFKA_BROKER="localhost:19092"
INPUT_TOPIC="decorated-flow-v1"
OUTPUT_TOPIC="ip-classification-v1"

echo "Starting test data ingestion for IP Classification service..."

# Test data - each JSON object on a separate line
cat << 'EOF' | kafka-console-producer.sh --bootstrap-server $KAFKA_BROKER --topic $INPUT_TOPIC
{"SrcIP": "*************", "SrcId": "test-src-id-001", "CSSrcId": "test-css-src-id-001", "DestIP": "*******", "DestId": "test-dest-id-001", "CSDestId": "test-css-dest-id-001", "Port": 443, "Proto": "tcp", "SentBytes": 1024, "ReceivedBytes": 2048, "IllumioTenantId": "test-tenant-id-001", "SrcTenantId": "test-src-tenant-001", "SrcSubId": "test-src-sub-001", "SrcRegion": "us-east-1", "SrcResId": "test-src-resource-001", "SrcVnetId": "test-src-vnet-001", "SrcUserName": "/org/test-org/user/testuser1", "DestTenantId": "test-dest-tenant-001", "DestSubId": "test-dest-sub-001", "DestRegion": "us-west-2", "DestResId": "test-dest-resource-001", "DestVnetId": "test-dest-vnet-001", "DestUserName": "/org/test-org/user/testuser2"}
{"SrcIP": "*********", "SrcId": "test-src-id-002", "CSSrcId": "test-css-src-id-002", "DestIP": "*******", "DestId": "test-dest-id-002", "CSDestId": "test-css-dest-id-002", "Port": 80, "Proto": "tcp", "SentBytes": 512, "ReceivedBytes": 1024, "IllumioTenantId": "test-tenant-id-002", "SrcTenantId": "test-src-tenant-002", "SrcSubId": "test-src-sub-002", "SrcRegion": "eu-west-1", "SrcResId": "test-src-resource-002", "SrcVnetId": "test-src-vnet-002", "SrcUserName": "/org/test-org/user/testuser3", "DestTenantId": "test-dest-tenant-002", "DestSubId": "test-dest-sub-002", "DestRegion": "eu-central-1", "DestResId": "test-dest-resource-002", "DestVnetId": "test-dest-vnet-002", "DestUserName": "/org/test-org/user/testuser4"}
{"SrcIP": "***********", "SrcId": "test-src-id-003", "CSSrcId": "test-css-src-id-003", "DestIP": "**************", "DestId": "test-dest-id-003", "CSDestId": "test-css-dest-id-003", "Port": 53, "Proto": "udp", "SentBytes": 64, "ReceivedBytes": 128, "IllumioTenantId": "test-tenant-id-003", "SrcTenantId": "test-src-tenant-003", "SrcSubId": "test-src-sub-003", "SrcRegion": "ap-southeast-1", "SrcResId": "test-src-resource-003", "SrcVnetId": "test-src-vnet-003", "SrcUserName": "/org/test-org/user/testuser5", "DestTenantId": "test-dest-tenant-003", "DestSubId": "test-dest-sub-003", "DestRegion": "ap-northeast-1", "DestResId": "test-dest-resource-003", "DestVnetId": "test-dest-vnet-003", "DestUserName": "/org/test-org/user/testuser6"}
EOF

echo "Test data sent to topic: $INPUT_TOPIC"
echo "Monitor the output topic: $OUTPUT_TOPIC"
echo ""
echo "To consume output data, run:"
echo "kafka-console-consumer.sh --bootstrap-server $KAFKA_BROKER --topic $OUTPUT_TOPIC --from-beginning"
