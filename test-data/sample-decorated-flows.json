[{"SrcIP": "*************", "SrcId": "vm-web-server-001", "CSSrcId": "css-vm-web-001", "DestIP": "*******", "DestId": "google-dns-primary", "CSDestId": "css-google-dns", "Port": 443, "Proto": "tcp", "SentBytes": 2048, "ReceivedBytes": 4096, "IllumioTenantId": "test-tenant-12345", "SrcTenantId": "test-tenant-12345", "SrcSubId": "subscription-web-tier", "SrcRegion": "us-east-1", "SrcResId": "resource-web-vm-001", "SrcVnetId": "vnet-web-tier", "SrcUserName": "/org/test-org/user/webadmin", "DestTenantId": "external-google", "DestSubId": "external-dns", "DestRegion": "us-central1", "DestResId": "google-dns-8888", "DestVnetId": "external-google-network", "DestUserName": "/org/google/service/dns"}, {"SrcIP": "*********", "SrcId": "vm-app-server-002", "CSSrcId": "css-vm-app-002", "DestIP": "*******", "DestId": "cloudflare-dns", "CSDestId": "css-cloudflare-dns", "Port": 80, "Proto": "tcp", "SentBytes": 1024, "ReceivedBytes": 2048, "IllumioTenantId": "test-tenant-12345", "SrcTenantId": "test-tenant-12345", "SrcSubId": "subscription-app-tier", "SrcRegion": "us-west-2", "SrcResId": "resource-app-vm-002", "SrcVnetId": "vnet-app-tier", "SrcUserName": "/org/test-org/user/appadmin", "DestTenantId": "external-cloudflare", "DestSubId": "external-dns", "DestRegion": "us-west1", "DestResId": "cloudflare-dns-1111", "DestVnetId": "external-cloudflare-network", "DestUserName": "/org/cloudflare/service/dns"}, {"SrcIP": "***********", "SrcId": "vm-db-server-003", "CSSrcId": "css-vm-db-003", "DestIP": "**************", "DestId": "opendns-primary", "CSDestId": "css-opendns", "Port": 53, "Proto": "udp", "SentBytes": 128, "ReceivedBytes": 256, "IllumioTenantId": "test-tenant-12345", "SrcTenantId": "test-tenant-12345", "SrcSubId": "subscription-db-tier", "SrcRegion": "eu-west-1", "SrcResId": "resource-db-vm-003", "SrcVnetId": "vnet-db-tier", "SrcUserName": "/org/test-org/user/dbadmin", "DestTenantId": "external-opendns", "DestSubId": "external-dns", "DestRegion": "us-east1", "DestResId": "opendns-primary-222", "DestVnetId": "external-opendns-network", "DestUserName": "/org/opendns/service/dns"}, {"SrcIP": "************", "SrcId": "vm-api-server-004", "CSSrcId": "css-vm-api-004", "DestIP": "************", "DestId": "microsoft-office365", "CSDestId": "css-microsoft-o365", "Port": 8080, "Proto": "tcp", "SentBytes": 5120, "ReceivedBytes": 10240, "IllumioTenantId": "test-tenant-12345", "SrcTenantId": "test-tenant-12345", "SrcSubId": "subscription-api-tier", "SrcRegion": "ap-southeast-1", "SrcResId": "resource-api-vm-004", "SrcVnetId": "vnet-api-tier", "SrcUserName": "/org/test-org/user/apiuser", "DestTenantId": "external-microsoft", "DestSubId": "external-office365", "DestRegion": "us-south-central", "DestResId": "microsoft-o365-endpoint", "DestVnetId": "external-microsoft-network", "DestUserName": "/org/microsoft/service/office365"}, {"SrcIP": "**********", "SrcId": "vm-monitoring-005", "CSSrcId": "css-vm-mon-005", "DestIP": "************", "DestId": "aws-cloudfront", "CSDestId": "css-aws-cf", "Port": 22, "Proto": "tcp", "SentBytes": 512, "ReceivedBytes": 1024, "IllumioTenantId": "test-tenant-12345", "SrcTenantId": "test-tenant-12345", "SrcSubId": "subscription-monitoring", "SrcRegion": "ap-northeast-1", "SrcResId": "resource-mon-vm-005", "SrcVnetId": "vnet-monitoring", "SrcUserName": "/org/test-org/user/monitoradmin", "DestTenantId": "external-aws", "DestSubId": "external-cloudfront", "DestRegion": "us-east-1", "DestResId": "aws-cloudfront-edge", "DestVnetId": "external-aws-network", "DestUserName": "/org/aws/service/cloudfront"}]