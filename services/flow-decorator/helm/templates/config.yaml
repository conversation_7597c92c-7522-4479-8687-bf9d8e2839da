apiVersion: v1
kind: ConfigMap
metadata:
  name: {{ include "FlowDecorator.fullname" . }}-env-configmap
  labels:
    {{- include "FlowDecorator.labels" . | nindent 4 }}
data:
  application.yml: |
    logging:
      level:
        ROOT: {{.Values.logging.level.root}}
        org:
          apache:
            kafka: INFO
    
    spring:
      application:
        name: flow-decorator
      output:
        ansi:
          enabled: ALWAYS
    
    flowDecorator:
      kafkaCommonConfig:
        bootstrapServers: "{{.Values.flowDecorator.kafkaCommonConfig.bootstrapServers}}"
        isSasl: true
      kafkaReceiverConfig:
        topic: "{{.Values.flowDecorator.kafkaReceiverConfig.topic}}"
        groupId: "{{.Values.flowDecorator.kafkaReceiverConfig.groupId}}"
        autoOffsetReset: "{{.Values.flowDecorator.kafkaReceiverConfig.autoOffsetReset}}"
        requestTimeoutMs: "{{.Values.flowDecorator.kafkaReceiverConfig.requestTimeoutMs}}"
        maxPollRecords: "{{.Values.flowDecorator.kafkaReceiverConfig.maxPollRecords}}"
        maxPartitionFetchBytes: "{{.Values.flowDecorator.kafkaReceiverConfig.maxPartitionFetchBytes}}"
      kafkaSenderConfig:
        sinkTopic: "{{.Values.flowDecorator.kafkaSenderConfig.sinkTopic}}"
      inventoryConfig:
        host: "{{.Values.flowDecorator.inventoryConfig.host}}"
        port: "{{.Values.flowDecorator.inventoryConfig.port}}"
      resilienceConfig:
        failureRateThreshold: "{{.Values.flowDecorator.resilienceConfig.failureRateThreshold}}"
        waitDurationInOpenState: "{{.Values.flowDecorator.resilienceConfig.waitDurationInOpenState}}"
        slidingWindowSize: "{{.Values.flowDecorator.resilienceConfig.slidingWindowSize}}"
        maxAttempts: "{{.Values.flowDecorator.resilienceConfig.maxAttempts}}"
        waitDuration: "{{.Values.flowDecorator.resilienceConfig.waitDuration}}"
        timeoutDuration: "{{.Values.flowDecorator.resilienceConfig.timeoutDuration}}"