name: IP Classification Integration Tests

on:
  push:
    branches: [ main, develop ]
    paths:
      - 'services/ip-classification-mmdb/**'
  pull_request:
    branches: [ main ]
    paths:
      - 'services/ip-classification-mmdb/**'
  schedule:
    # Run integration tests daily at 2 AM UTC
    - cron: '0 2 * * *'
  workflow_dispatch:
    inputs:
      test_type:
        description: 'Type of integration test to run'
        required: true
        default: 'comprehensive'
        type: choice
        options:
          - 'comprehensive'
          - 'real-time-only'
          - 'performance-only'

env:
  KAFKA_BOOTSTRAP_SERVERS: localhost:19092
  BLUEMMDB_HOST: localhost
  BLUEMMDB_PORT: 7900
  TEST_TENANT_ID: ci-integration-test
  JAVA_VERSION: '17'

jobs:
  integration-tests:
    runs-on: ubuntu-latest
    timeout-minutes: 30
    
    services:
      # Kafka service for integration testing
      kafka:
        image: confluentinc/cp-kafka:latest
        ports:
          - 19092:19092
        env:
          KAFKA_BROKER_ID: 1
          KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
          KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://localhost:19092
          KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
          KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
          KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
        options: >-
          --health-cmd "kafka-broker-api-versions --bootstrap-server localhost:19092"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5

      zookeeper:
        image: confluentinc/cp-zookeeper:latest
        ports:
          - 2181:2181
        env:
          ZOOKEEPER_CLIENT_PORT: 2181
          ZOOKEEPER_TICK_TIME: 2000

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Set up JDK ${{ env.JAVA_VERSION }}
      uses: actions/setup-java@v4
      with:
        java-version: ${{ env.JAVA_VERSION }}
        distribution: 'temurin'

    - name: Cache Gradle packages
      uses: actions/cache@v3
      with:
        path: |
          ~/.gradle/caches
          ~/.gradle/wrapper
        key: ${{ runner.os }}-gradle-${{ hashFiles('**/*.gradle*', '**/gradle-wrapper.properties') }}
        restore-keys: |
          ${{ runner.os }}-gradle-

    - name: Wait for Kafka to be ready
      run: |
        echo "Waiting for Kafka to be ready..."
        timeout 60 bash -c 'until nc -z localhost 19092; do sleep 1; done'
        echo "Kafka is ready!"

    - name: Create Kafka topics
      run: |
        # Install Kafka CLI tools
        wget -q https://downloads.apache.org/kafka/2.8.2/kafka_2.13-2.8.2.tgz
        tar -xzf kafka_2.13-2.8.2.tgz
        export PATH=$PATH:$(pwd)/kafka_2.13-2.8.2/bin
        
        # Create required topics
        kafka-topics.sh --create --topic decorated-flow-v1 --bootstrap-server localhost:19092 --partitions 3 --replication-factor 1 --if-not-exists
        kafka-topics.sh --create --topic ip-classification-v1 --bootstrap-server localhost:19092 --partitions 3 --replication-factor 1 --if-not-exists
        
        # Verify topics
        kafka-topics.sh --list --bootstrap-server localhost:19092

    - name: Start BlueMmdb Mock Service (Optional)
      run: |
        # Start a simple mock BlueMmdb service for testing
        # This is optional - tests will work without it but won't have IP enrichment
        echo "Starting mock BlueMmdb service..."
        nohup python3 -c "
        import http.server
        import socketserver
        import json
        
        class MockBlueMmdbHandler(http.server.BaseHTTPRequestHandler):
            def do_POST(self):
                self.send_response(200)
                self.send_header('Content-type', 'application/json')
                self.end_headers()
                mock_response = {
                    'threat_level': 'low',
                    'country': 'US',
                    'city': 'San Francisco'
                }
                self.wfile.write(json.dumps(mock_response).encode())
        
        with socketserver.TCPServer(('', 7900), MockBlueMmdbHandler) as httpd:
            httpd.serve_forever()
        " > bluemmdb_mock.log 2>&1 &
        
        # Wait for mock service to start
        sleep 5
        
        # Check if mock service is running
        if nc -z localhost 7900; then
          echo "Mock BlueMmdb service started successfully"
        else
          echo "Mock BlueMmdb service failed to start - continuing without it"
        fi

    - name: Build IP Classification Service
      working-directory: services/ip-classification-mmdb
      run: |
        chmod +x gradlew
        ./gradlew build -x test

    - name: Start IP Classification Service
      working-directory: services/ip-classification-mmdb
      run: |
        echo "Starting IP Classification service..."
        nohup java -jar build/libs/ip-classification-mmdb-*.jar \
          --spring.profiles.active=integration \
          --ipClassificationMmdb.kafkaCommonConfig.bootstrapServers=localhost:19092 \
          --blueMmdbClient.target=localhost \
          --blueMmdbClient.port=7900 \
          > service.log 2>&1 &
        
        # Wait for service to start
        echo "Waiting for IP Classification service to start..."
        timeout 60 bash -c 'until grep -q "Started IpClassificationMmdbApplication" service.log; do sleep 2; done'
        echo "IP Classification service started!"

    - name: Run Integration Tests
      working-directory: services/ip-classification-mmdb
      env:
        CI_BUILD_ID: ${{ github.run_id }}-${{ github.run_attempt }}
      run: |
        chmod +x run-real-integration-tests.sh
        
        # Determine test type
        TEST_TYPE="${{ github.event.inputs.test_type || 'comprehensive' }}"
        
        case $TEST_TYPE in
          "real-time-only")
            ./gradlew test --tests "RealTimeIpClassificationIntegrationTest" -Dspring.profiles.active=integration --info
            ;;
          "performance-only")
            ./gradlew test --tests "CiCdIntegrationTestRunner.testPerformanceUnderLoad" -Dspring.profiles.active=integration --info
            ;;
          *)
            ./gradlew test --tests "*Integration*" -Dspring.profiles.active=integration --info
            ;;
        esac

    - name: Collect Service Logs
      if: always()
      working-directory: services/ip-classification-mmdb
      run: |
        echo "=== IP Classification Service Logs ==="
        cat service.log || echo "No service logs found"
        echo ""
        echo "=== BlueMmdb Mock Service Logs ==="
        cat bluemmdb_mock.log || echo "No BlueMmdb mock logs found"

    - name: Collect Test Results
      if: always()
      uses: actions/upload-artifact@v3
      with:
        name: integration-test-results
        path: |
          services/ip-classification-mmdb/build/reports/tests/
          services/ip-classification-mmdb/build/test-results/
          services/ip-classification-mmdb/service.log
          services/ip-classification-mmdb/bluemmdb_mock.log

    - name: Publish Test Results
      if: always()
      uses: dorny/test-reporter@v1
      with:
        name: Integration Test Results
        path: services/ip-classification-mmdb/build/test-results/test/*.xml
        reporter: java-junit

    - name: Comment PR with Test Results
      if: github.event_name == 'pull_request' && always()
      uses: actions/github-script@v6
      with:
        script: |
          const fs = require('fs');
          const path = 'services/ip-classification-mmdb/build/test-results/test/';
          
          let testSummary = '## 🧪 Integration Test Results\n\n';
          
          if (fs.existsSync(path)) {
            testSummary += '✅ Integration tests completed successfully!\n\n';
            testSummary += '**Test Details:**\n';
            testSummary += `- Build ID: ${{ github.run_id }}-${{ github.run_attempt }}\n`;
            testSummary += `- Test Tenant: ${process.env.TEST_TENANT_ID}\n`;
            testSummary += `- Kafka: ${process.env.KAFKA_BOOTSTRAP_SERVERS}\n`;
          } else {
            testSummary += '❌ Integration tests failed or did not complete.\n\n';
            testSummary += 'Please check the workflow logs for details.\n';
          }
          
          github.rest.issues.createComment({
            issue_number: context.issue.number,
            owner: context.repo.owner,
            repo: context.repo.repo,
            body: testSummary
          });

  notify-on-failure:
    needs: integration-tests
    if: failure()
    runs-on: ubuntu-latest
    steps:
    - name: Notify on Failure
      run: |
        echo "Integration tests failed for IP Classification service"
        echo "Build ID: ${{ github.run_id }}-${{ github.run_attempt }}"
        echo "Please check the logs and fix the issues before merging."
