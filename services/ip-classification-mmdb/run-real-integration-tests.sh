#!/bin/bash

# Script to run real integration tests against IP Classification service
# This script tests the actual service (not test folders) for CI/CD readiness

set -e

echo "=========================================="
echo "🚀 Real IP Classification Integration Tests"
echo "=========================================="

# Configuration
KAFKA_PORT=${KAFKA_PORT:-19092}
BLUEMMDB_PORT=${BLUEMMDB_PORT:-7900}
TEST_TENANT_ID=${TEST_TENANT_ID:-"ci-integration-test"}
BUILD_ID=${CI_BUILD_ID:-"local-$(date +%s)"}

# Service endpoints
KAFKA_BOOTSTRAP_SERVERS=${KAFKA_BOOTSTRAP_SERVERS:-"localhost:$KAFKA_PORT"}
BLUEMMDB_HOST=${BLUEMMDB_HOST:-"localhost"}

echo "Configuration:"
echo "  Kafka: $KAFKA_BOOTSTRAP_SERVERS"
echo "  BlueMmdb: $BLUEMMDB_HOST:$BLUEMMDB_PORT"
echo "  Test Tenant: $TEST_TENANT_ID"
echo "  Build ID: $BUILD_ID"
echo ""

# Function to check service health
check_service_health() {
    local host=$1
    local port=$2
    local service_name=$3
    
    if nc -z $host $port 2>/dev/null; then
        echo "✅ $service_name is healthy ($host:$port)"
        return 0
    else
        echo "❌ $service_name is not accessible ($host:$port)"
        return 1
    fi
}

# Function to wait for service
wait_for_service() {
    local host=$1
    local port=$2
    local service_name=$3
    local max_attempts=30
    local attempt=1
    
    echo "⏳ Waiting for $service_name to be ready..."
    
    while [ $attempt -le $max_attempts ]; do
        if nc -z $host $port 2>/dev/null; then
            echo "✅ $service_name is ready!"
            return 0
        fi
        
        echo "   Attempt $attempt/$max_attempts: $service_name not ready yet..."
        sleep 2
        ((attempt++))
    done
    
    echo "❌ $service_name failed to become ready within timeout"
    return 1
}

# Health checks
echo "🔍 Performing health checks..."

# Extract host and port from KAFKA_BOOTSTRAP_SERVERS
KAFKA_HOST=$(echo $KAFKA_BOOTSTRAP_SERVERS | cut -d':' -f1)
KAFKA_PORT=$(echo $KAFKA_BOOTSTRAP_SERVERS | cut -d':' -f2)

if ! check_service_health $KAFKA_HOST $KAFKA_PORT "Kafka"; then
    echo ""
    echo "❌ Kafka is required for integration tests"
    echo "Please ensure Kafka is running at: $KAFKA_BOOTSTRAP_SERVERS"
    exit 1
fi

# BlueMmdb is optional but recommended
if ! check_service_health $BLUEMMDB_HOST $BLUEMMDB_PORT "BlueMmdb"; then
    echo "⚠️  BlueMmdb service is not accessible"
    echo "   IP enrichment will not work, but basic flow processing will be tested"
    echo ""
    read -p "Continue without BlueMmdb? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo ""
echo "✅ Health checks completed"
echo ""

# Check if IP Classification service is running
echo "🔍 Checking IP Classification service..."

# Look for the service process
if pgrep -f "ip-classification-mmdb" > /dev/null; then
    echo "✅ IP Classification service appears to be running"
else
    echo "⚠️  IP Classification service process not detected"
    echo "   Please ensure the service is running before proceeding"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo ""

# Verify Kafka topics exist
echo "📝 Verifying Kafka topics..."

check_topic() {
    local topic_name=$1
    
    if command -v kafka-topics.sh &> /dev/null; then
        if kafka-topics.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --list | grep -q "^${topic_name}$"; then
            echo "✅ Topic '$topic_name' exists"
            return 0
        else
            echo "❌ Topic '$topic_name' does not exist"
            return 1
        fi
    elif command -v kaf &> /dev/null; then
        if kaf topic list | grep -q "^${topic_name}$"; then
            echo "✅ Topic '$topic_name' exists"
            return 0
        else
            echo "❌ Topic '$topic_name' does not exist"
            return 1
        fi
    else
        echo "⚠️  Cannot verify topic '$topic_name' - no Kafka tools available"
        return 0
    fi
}

INPUT_TOPIC_EXISTS=true
OUTPUT_TOPIC_EXISTS=true

if ! check_topic "decorated-flow-v1"; then
    INPUT_TOPIC_EXISTS=false
fi

if ! check_topic "ip-classification-v1"; then
    OUTPUT_TOPIC_EXISTS=false
fi

if [ "$INPUT_TOPIC_EXISTS" = false ] || [ "$OUTPUT_TOPIC_EXISTS" = false ]; then
    echo ""
    echo "❌ Required Kafka topics are missing"
    echo "Please create the topics or ensure the IP Classification service has created them"
    exit 1
fi

echo ""
echo "✅ Kafka topics verified"
echo ""

# Build the test classes
echo "🔨 Building integration tests..."
cd "$(dirname "$0")"

if ! ./gradlew compileTestJava; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build completed"
echo ""

# Set environment variables for tests
export KAFKA_BOOTSTRAP_SERVERS
export BLUEMMDB_HOST
export BLUEMMDB_PORT
export TEST_TENANT_ID
export CI_BUILD_ID=$BUILD_ID

# Choose test type
echo "🧪 Select integration test type:"
echo "1. Real-time processing test (recommended)"
echo "2. CI/CD comprehensive test suite"
echo "3. Both tests"
echo ""
read -p "Enter choice (1-3): " -n 1 -r
echo ""

case $REPLY in
    1)
        echo "🚀 Running real-time processing integration test..."
        ./gradlew test --tests "RealTimeIpClassificationIntegrationTest" \
            -Dspring.profiles.active=integration \
            --info
        ;;
    2)
        echo "🚀 Running CI/CD comprehensive test suite..."
        ./gradlew test --tests "CiCdIntegrationTestRunner" \
            -Dspring.profiles.active=integration \
            --info
        ;;
    3)
        echo "🚀 Running all integration tests..."
        ./gradlew test --tests "*Integration*" \
            -Dspring.profiles.active=integration \
            --info
        ;;
    *)
        echo "❌ Invalid choice. Running default real-time test..."
        ./gradlew test --tests "RealTimeIpClassificationIntegrationTest" \
            -Dspring.profiles.active=integration \
            --info
        ;;
esac

TEST_EXIT_CODE=$?

echo ""
if [ $TEST_EXIT_CODE -eq 0 ]; then
    echo "🎉 Integration tests PASSED!"
    echo ""
    echo "📊 Test Summary:"
    echo "  ✅ Service connectivity verified"
    echo "  ✅ Real-time data processing tested"
    echo "  ✅ End-to-end workflow validated"
    echo "  ✅ Ready for CI/CD deployment"
else
    echo "❌ Integration tests FAILED!"
    echo ""
    echo "🔍 Troubleshooting:"
    echo "  1. Check if IP Classification service is running"
    echo "  2. Verify Kafka connectivity"
    echo "  3. Check BlueMmdb service (if using IP enrichment)"
    echo "  4. Review test logs for specific errors"
fi

echo ""
echo "📝 Monitoring Commands:"
echo "  Input topic:  kafka-console-consumer.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --topic decorated-flow-v1 --from-beginning"
echo "  Output topic: kafka-console-consumer.sh --bootstrap-server $KAFKA_BOOTSTRAP_SERVERS --topic ip-classification-v1 --from-beginning"
echo ""
echo "🏷️  Test Metadata:"
echo "  Build ID: $BUILD_ID"
echo "  Tenant ID: $TEST_TENANT_ID"
echo "  Timestamp: $(date -u +"%Y-%m-%dT%H:%M:%SZ")"

exit $TEST_EXIT_CODE
