#!/bin/bash

# Script to feed JSON data to IP classification service via Kafka
# This script sends decorated flow JSON data to the input topic

set -e

# Configuration
KAFKA_BROKER="localhost:19092"
INPUT_TOPIC="decorated-flow-v1"
TEST_TENANT_ID="test-tenant-12345"

echo "=========================================="
echo "Feeding JSON Data to IP Classification Service"
echo "=========================================="
echo "Kafka Broker: $KAFKA_BROKER"
echo "Input Topic: $INPUT_TOPIC"
echo "Test Tenant: $TEST_TENANT_ID"
echo ""

# Function to send a single JSON record
send_json_record() {
    local json_data="$1"
    local record_id="$2"
    
    echo "Sending record $record_id:"
    echo "$json_data" | kafka-console-producer.sh \
        --bootstrap-server $KAFKA_BROKER \
        --topic $INPUT_TOPIC \
        --property "key.separator=:" \
        --property "parse.key=true" \
        --property "key.serializer=org.apache.kafka.common.serialization.StringSerializer" \
        --property "value.serializer=org.apache.kafka.common.serialization.StringSerializer"
    
    echo "✅ Record $record_id sent successfully"
    echo ""
}

# Function to create sample JSON data
create_sample_json() {
    local index=$1
    local src_ip="192.168.1.$((100 + index))"
    local dest_ip="8.8.8.$((8 + index % 3))"
    local port=$((443 + index))
    
    cat << EOF
${TEST_TENANT_ID}-record-${index}:{"SrcIP":"${src_ip}","SrcId":"src-vm-${index}","CSSrcId":"css-src-${index}","DestIP":"${dest_ip}","DestId":"dest-${index}","CSDestId":"css-dest-${index}","Port":${port},"Proto":"tcp","SentBytes":$((1024 * (index + 1))),"ReceivedBytes":$((2048 * (index + 1))),"IllumioTenantId":"${TEST_TENANT_ID}","SrcTenantId":"${TEST_TENANT_ID}","SrcSubId":"subscription-$((index % 3))","SrcRegion":"us-east-1","SrcResId":"resource-vm-${index}","SrcVnetId":"vnet-$((index % 2))","SrcUserName":"/org/test-org/user/user${index}","DestTenantId":"external-tenant","DestSubId":"external-sub","DestRegion":"us-west-2","DestResId":"external-resource-${index}","DestVnetId":"external-vnet","DestUserName":"/org/external/user/external${index}"}
EOF
}

# Function to send multiple records at once
send_batch_records() {
    local count=$1
    echo "Sending $count records in batch..."
    
    for i in $(seq 0 $((count - 1))); do
        create_sample_json $i
    done | kafka-console-producer.sh \
        --bootstrap-server $KAFKA_BROKER \
        --topic $INPUT_TOPIC \
        --property "key.separator=:" \
        --property "parse.key=true"
    
    echo "✅ Batch of $count records sent successfully"
}

# Function to send hourly data
send_hourly_data() {
    local hours=$1
    echo "Sending hourly data for $hours hours..."
    
    for hour in $(seq 0 $((hours - 1))); do
        echo "Sending data for hour $((hour + 1))..."
        
        # Generate 10-15 records per hour
        local records_per_hour=$((10 + RANDOM % 5))
        
        for record in $(seq 0 $((records_per_hour - 1))); do
            local src_ip="192.168.$((hour + 1)).$((100 + record))"
            local dest_ip="8.8.$((8 + record % 3)).$((8 + record % 10))"
            local port_options=(80 443 53 22 3389 8080)
            local port=${port_options[$((record % ${#port_options[@]}))]}
            local proto_options=("tcp" "udp")
            local proto=${proto_options[$((record % ${#proto_options[@]}))]}
            
            cat << EOF
${TEST_TENANT_ID}-hour-${hour}-${record}:{"SrcIP":"${src_ip}","SrcId":"src-h${hour}-${record}","CSSrcId":"css-src-h${hour}-${record}","DestIP":"${dest_ip}","DestId":"dest-h${hour}-${record}","CSDestId":"css-dest-h${hour}-${record}","Port":${port},"Proto":"${proto}","SentBytes":$((RANDOM % 5000 + 100)),"ReceivedBytes":$((RANDOM % 10000 + 200)),"IllumioTenantId":"${TEST_TENANT_ID}","SrcTenantId":"${TEST_TENANT_ID}","SrcSubId":"subscription-$((record % 5))","SrcRegion":"us-east-1","SrcResId":"resource-h${hour}-${record}","SrcVnetId":"vnet-$((record % 3))","SrcUserName":"/org/test-org/user/user${record}","DestTenantId":"external-tenant","DestSubId":"external-sub","DestRegion":"us-west-2","DestResId":"external-resource-${record}","DestVnetId":"external-vnet","DestUserName":"/org/external/user/external${record}","Hour":${hour},"RecordIndex":${record}}
EOF
        done | kafka-console-producer.sh \
            --bootstrap-server $KAFKA_BROKER \
            --topic $INPUT_TOPIC \
            --property "key.separator=:" \
            --property "parse.key=true"
        
        echo "✅ Sent $records_per_hour records for hour $((hour + 1))"
        
        # Small delay between hours
        sleep 2
    done
}

# Function to send data from file
send_from_file() {
    local file_path="$1"
    
    if [[ ! -f "$file_path" ]]; then
        echo "❌ File not found: $file_path"
        exit 1
    fi
    
    echo "Sending data from file: $file_path"
    
    # Check if file contains JSON array or single object
    if jq -e 'type == "array"' "$file_path" > /dev/null 2>&1; then
        # JSON array - send each object
        local count=$(jq 'length' "$file_path")
        echo "Found JSON array with $count objects"
        
        for i in $(seq 0 $((count - 1))); do
            local json_obj=$(jq -c ".[$i]" "$file_path")
            echo "${TEST_TENANT_ID}-file-${i}:${json_obj}" | kafka-console-producer.sh \
                --bootstrap-server $KAFKA_BROKER \
                --topic $INPUT_TOPIC \
                --property "key.separator=:" \
                --property "parse.key=true"
        done
        
        echo "✅ Sent $count records from file"
    else
        # Single JSON object
        echo "Found single JSON object"
        local json_obj=$(jq -c '.' "$file_path")
        echo "${TEST_TENANT_ID}-file-single:${json_obj}" | kafka-console-producer.sh \
            --bootstrap-server $KAFKA_BROKER \
            --topic $INPUT_TOPIC \
            --property "key.separator=:" \
            --property "parse.key=true"
        
        echo "✅ Sent single record from file"
    fi
}

# Main menu
show_menu() {
    echo "Select an option:"
    echo "1. Send sample records (default: 5)"
    echo "2. Send batch records (specify count)"
    echo "3. Send hourly data (specify hours)"
    echo "4. Send data from JSON file"
    echo "5. Send single custom record"
    echo "6. Exit"
    echo ""
}

# Check if Kafka tools are available
if ! command -v kafka-console-producer.sh &> /dev/null; then
    echo "❌ kafka-console-producer.sh not found in PATH"
    echo "Please ensure Kafka is installed and tools are in PATH"
    exit 1
fi

# Check if jq is available (for file processing)
if ! command -v jq &> /dev/null; then
    echo "⚠️  jq not found - file processing will be limited"
fi

# Interactive mode if no arguments
if [[ $# -eq 0 ]]; then
    while true; do
        show_menu
        read -p "Enter choice (1-6): " choice
        
        case $choice in
            1)
                read -p "Number of sample records (default: 5): " count
                count=${count:-5}
                send_batch_records $count
                ;;
            2)
                read -p "Number of records to send: " count
                if [[ $count =~ ^[0-9]+$ ]]; then
                    send_batch_records $count
                else
                    echo "❌ Invalid number"
                fi
                ;;
            3)
                read -p "Number of hours: " hours
                if [[ $hours =~ ^[0-9]+$ ]]; then
                    send_hourly_data $hours
                else
                    echo "❌ Invalid number"
                fi
                ;;
            4)
                read -p "JSON file path: " file_path
                send_from_file "$file_path"
                ;;
            5)
                echo "Enter JSON data (press Ctrl+D when done):"
                json_data=$(cat)
                echo "${TEST_TENANT_ID}-custom:${json_data}" | kafka-console-producer.sh \
                    --bootstrap-server $KAFKA_BROKER \
                    --topic $INPUT_TOPIC \
                    --property "key.separator=:" \
                    --property "parse.key=true"
                echo "✅ Custom record sent"
                ;;
            6)
                echo "Goodbye!"
                exit 0
                ;;
            *)
                echo "❌ Invalid choice"
                ;;
        esac
        echo ""
    done
else
    # Command line mode
    case $1 in
        sample)
            count=${2:-5}
            send_batch_records $count
            ;;
        hourly)
            hours=${2:-3}
            send_hourly_data $hours
            ;;
        file)
            if [[ -z "$2" ]]; then
                echo "Usage: $0 file <json-file-path>"
                exit 1
            fi
            send_from_file "$2"
            ;;
        *)
            echo "Usage:"
            echo "  $0                    - Interactive mode"
            echo "  $0 sample [count]     - Send sample records"
            echo "  $0 hourly [hours]     - Send hourly data"
            echo "  $0 file <path>        - Send data from file"
            exit 1
            ;;
    esac
fi

echo ""
echo "🎉 Data feeding completed!"
echo ""
echo "To monitor the processing:"
echo "Input topic:  kafka-console-consumer.sh --bootstrap-server $KAFKA_BROKER --topic $INPUT_TOPIC --from-beginning"
echo "Output topic: kafka-console-consumer.sh --bootstrap-server $KAFKA_BROKER --topic ip-classification-v1 --from-beginning"
