#!/bin/bash

# Script to start continuous hourly data ingestion for IP classification service
# This will run indefinitely, feeding JSON data every hour

set -e

echo "=========================================="
echo "🚀 Starting Continuous Hourly Data Ingestion"
echo "=========================================="

# Configuration
KAFKA_PORT=19092
BLUEMMDB_PORT=7900
TEST_TENANT_ID="test-tenant-12345"
APP_PROFILE="hourly-ingestion"

# Function to check if a service is running
check_service() {
    local port=$1
    local service_name=$2
    
    if nc -z localhost $port 2>/dev/null; then
        echo "✅ $service_name is running on port $port"
        return 0
    else
        echo "❌ $service_name is not running on port $port"
        return 1
    fi
}

# Check prerequisites
echo "🔍 Checking prerequisites..."

if ! command -v nc &> /dev/null; then
    echo "❌ netcat (nc) is required but not installed"
    exit 1
fi

if ! command -v java &> /dev/null; then
    echo "❌ Java is required but not installed"
    exit 1
fi

# Check if Kafka is running
if ! check_service $KAFKA_PORT "Kafka"; then
    echo ""
    echo "❌ Kafka is required for data ingestion"
    echo "Please start Kafka on port $KAFKA_PORT"
    echo "You can use: kaf kafka"
    exit 1
fi

# Check if bluemmdb is running (optional)
if ! check_service $BLUEMMDB_PORT "BlueMmdb"; then
    echo "⚠️  BlueMmdb service is not running on port $BLUEMMDB_PORT"
    echo "   Data will pass through without IP enrichment"
    echo ""
    read -p "Continue anyway? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        exit 1
    fi
fi

echo ""
echo "✅ Prerequisites check completed"
echo ""

# Create Kafka topics if they don't exist
echo "📝 Creating Kafka topics..."

create_topic() {
    local topic_name=$1
    echo "Creating topic: $topic_name"
    
    if command -v kafka-topics.sh &> /dev/null; then
        kafka-topics.sh --create --topic $topic_name --bootstrap-server localhost:$KAFKA_PORT --partitions 3 --replication-factor 1 --if-not-exists 2>/dev/null || true
    elif command -v kaf &> /dev/null; then
        kaf topic create $topic_name --partitions 3 --if-not-exists 2>/dev/null || true
    else
        echo "⚠️  Could not create topic $topic_name - no Kafka tools found"
    fi
}

create_topic "decorated-flow-v1"
create_topic "ip-classification-v1"

echo ""

# Choose scheduling mode
echo "⏰ Select ingestion schedule:"
echo "1. Every hour (production mode)"
echo "2. Every minute (fast testing)"
echo "3. Every 5 minutes (moderate testing)"
echo ""
read -p "Enter choice (1-3): " -n 1 -r
echo ""

case $REPLY in
    1)
        SCHEDULE_MODE="hourly"
        echo "📅 Selected: Hourly ingestion (every hour at minute 0)"
        ;;
    2)
        SCHEDULE_MODE="minute"
        echo "📅 Selected: Minute ingestion (every minute)"
        ;;
    3)
        SCHEDULE_MODE="5minute"
        echo "📅 Selected: 5-minute ingestion (every 5 minutes)"
        ;;
    *)
        echo "📅 Default: Hourly ingestion"
        SCHEDULE_MODE="hourly"
        ;;
esac

echo ""

# Build the application
echo "🔨 Building application..."
cd "$(dirname "$0")"

if ! ./gradlew compileTestJava; then
    echo "❌ Build failed"
    exit 1
fi

echo "✅ Build completed"
echo ""

# Prepare application properties
echo "⚙️  Configuring application..."

# Create temporary application properties for the selected schedule
TEMP_CONFIG="/tmp/application-temp-$$.yml"

cat > "$TEMP_CONFIG" << EOF
logging:
  level:
    ROOT: INFO
    com.illumio.data: DEBUG

spring:
  application:
    name: continuous-hourly-ingestion
  task:
    scheduling:
      pool:
        size: 2

integration:
  test:
    hourly-ingestion:
      enabled: true
    tenant-id: "$TEST_TENANT_ID"
    records-per-hour: 25

ipClassificationMmdb:
  kafkaCommonConfig:
    bootstrapServers: localhost:$KAFKA_PORT
    isSasl: false
  kafkaReceiverConfig:
    topic: decorated-flow-v1
    groupId: ip-classification-mmdb-continuous-group
  kafkaSenderConfig:
    sinkTopic: ip-classification-v1

blueMmdbClient:
  target: "localhost"
  port: $BLUEMMDB_PORT
EOF

echo "✅ Configuration prepared"
echo ""

# Start the application
echo "🚀 Starting continuous hourly data ingestion..."
echo ""
echo "📊 Monitoring Information:"
echo "   • Test Tenant ID: $TEST_TENANT_ID"
echo "   • Schedule Mode: $SCHEDULE_MODE"
echo "   • Input Topic: decorated-flow-v1"
echo "   • Output Topic: ip-classification-v1"
echo ""
echo "📝 To monitor data flow:"
echo "   Input:  kafka-console-consumer.sh --bootstrap-server localhost:$KAFKA_PORT --topic decorated-flow-v1 --from-beginning"
echo "   Output: kafka-console-consumer.sh --bootstrap-server localhost:$KAFKA_PORT --topic ip-classification-v1 --from-beginning"
echo ""
echo "🛑 Press Ctrl+C to stop the ingestion"
echo ""
echo "=" .repeat(60)

# Run the application
java -cp "build/classes/java/test:build/classes/java/main:$(./gradlew -q printClasspath)" \
    -Dspring.config.location="file:$TEMP_CONFIG" \
    -Dspring.profiles.active="$APP_PROFILE" \
    com.illumio.data.integration.ContinuousHourlyIngestionApp

# Cleanup
rm -f "$TEMP_CONFIG"

echo ""
echo "🏁 Continuous hourly data ingestion stopped"
