logging:
  level:
    ROOT: INFO
    com.illumio.data: DEBUG
    org.apache.kafka: WARN

spring:
  application:
    name: ip-classification-integration-test
  output:
    ansi:
      enabled: ALWAYS

# Real service configuration (not test configuration)
# This connects to the actual IP classification service
integration:
  test:
    # Real Kafka topics (not test topics)
    input-topic: decorated-flow-v1
    output-topic: ip-classification-v1
    tenant-id: integration-test-tenant
    kafka:
      bootstrap-servers: localhost:19092
      # For CI/CD, you might want to use environment variables:
      # bootstrap-servers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:19092}
    timeout:
      processing: 60s
      connection: 30s
    
# IP Classification service configuration
# This should match your actual service configuration
ipClassificationMmdb:
  kafkaCommonConfig:
    bootstrapServers: ${KAFKA_BOOTSTRAP_SERVERS:localhost:19092}
    isSasl: false
    saslJaasConfig: _DO_NOT_COMMIT_
    metadataMaxAgeMs: 180000
    connectionMaxIdleMs: 180000
  kafkaReceiverConfig:
    topic: decorated-flow-v1
    groupId: ip-classification-mmdb-integration-group
    isGroupInstanceIdEnabled: false
    autoOffsetReset: latest
    requestTimeoutMs: 60000
    maxPollRecords: 1000
    maxPartitionFetchBytes: 2097152
    maxRetries: 3
    maxBackoff: 1s
    assignmentStrategy: "org.apache.kafka.clients.consumer.CooperativeStickyAssignor"
    maxPollIntervalMs: 300000
    sessionTimeoutMs: 300000
    heartbeatIntervalMs: 3000
  kafkaSenderConfig:
    sinkTopic: ip-classification-v1
    maxRequestSize: 1000000
    requestTimeoutMs: 60000
    metadataMaxIdleMs: 180000
    deliveryTimeoutMs: 300000
    maxRetries: 3
    maxBackoff: 1s
    lingerMs: 10
    bufferMemoryMb: 32
    batchSizeKb: 32
    numProducers: 1
  grpcConfig:
    timeout: 1s
    maxRetries: 3
    initialBackoff: 100ms
    maxBackoff: 1s
  backpressureConfig:
    enabled: false
    maxSize: 1000
    maxTime: 1000ms
    lowTide: 100
    highTide: 1000

# BlueMmdb client configuration for real IP enrichment
blueMmdbClient:
  target: ${BLUEMMDB_HOST:localhost}
  port: ${BLUEMMDB_PORT:7900}

circuitBreaker:
  enabled: true

scheduler:
  maxThreadPoolQueueSize: 50
  keepAliveTimeSeconds: 30
  name: "ip-classification-integration-scheduler"
