logging:
  level:
    ROOT: INFO
    com.illumio.data: DEBUG

spring:
  application:
    name: ip-classification-mmdb-hourly-ingestion
  output:
    ansi:
      enabled: ALWAYS
  task:
    scheduling:
      pool:
        size: 2

# Enable automatic hourly data ingestion
integration:
  test:
    hourly-ingestion:
      enabled: true
      # Choose one of these scheduling options:
      # For testing every minute: fixedRate: 60000
      # For testing every 5 minutes: fixedRate: 300000  
      # For production hourly: use cron expression in the service
      tenant-id: "test-tenant-12345"
      records-per-hour: 20  # Number of records to generate each hour

ipClassificationMmdb:
  kafkaCommonConfig:
    bootstrapServers: localhost:19092
    isSasl: false
    saslJaasConfig: _DO_NOT_COMMIT_
    metadataMaxAgeMs: 180000
    connectionMaxIdleMs: 180000
  kafkaReceiverConfig:
    topic: decorated-flow-v1
    groupId: ip-classification-mmdb-hourly-group
    isGroupInstanceIdEnabled: false
    autoOffsetReset: earliest
    requestTimeoutMs: 60000
    maxPollRecords: 2000
    maxPartitionFetchBytes: 2097152
    maxRetries: 5
    maxBackoff: 3s
    assignmentStrategy: "org.apache.kafka.clients.consumer.CooperativeStickyAssignor"
    maxPollIntervalMs: 300000
    sessionTimeoutMs: 300000
    heartbeatIntervalMs: 3000
  kafkaSenderConfig:
    sinkTopic: ip-classification-v1
    maxRequestSize: 1000000
    requestTimeoutMs: 60000
    metadataMaxIdleMs: 180000
    deliveryTimeoutMs: 300000
    maxRetries: 5
    maxBackoff: 3s
    lingerMs: 10
    bufferMemoryMb: 48
    batchSizeKb: 64
    numProducers: 1
  grpcConfig:
    timeout: 1s
    maxRetries: 3
    initialBackoff: 100ms
    maxBackoff: 1s
  backpressureConfig:
    enabled: false
    maxSize: 4000
    maxTime: 5000ms
    lowTide: 600
    highTide: 6000

blueMmdbClient:
  target: "localhost"
  port: 7900

circuitBreaker:
  enabled: false

scheduler:
  maxThreadPoolQueueSize: 100
  keepAliveTimeSeconds: 60
  name: "ip-classification-hourly-scheduler"
