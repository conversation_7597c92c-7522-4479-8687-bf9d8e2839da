package com.illumio.data.integration;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.consumer.ConsumerConfig;
import org.apache.kafka.clients.consumer.ConsumerRecord;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.StringDeserializer;
import org.apache.kafka.common.serialization.StringSerializer;
import org.junit.jupiter.api.*;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import reactor.core.publisher.Flux;
import reactor.kafka.receiver.KafkaReceiver;
import reactor.kafka.receiver.ReceiverOptions;
import reactor.test.StepVerifier;

import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Real-time integration test for IP Classification service.
 * 
 * This test:
 * 1. Sends real JSON data to the actual IP classification service
 * 2. Monitors the output to verify processing
 * 3. Validates that IP enrichment is working
 * 4. Can be used in CI/CD pipelines
 * 
 * Prerequisites:
 * - Kafka running on configured broker
 * - IP Classification service running
 * - BlueMmdb service running (for IP enrichment)
 */
@Slf4j
@SpringBootTest
@TestPropertySource(locations = "classpath:application-integration.yml")
@TestMethodOrder(MethodOrderer.OrderAnnotation.class)
public class RealTimeIpClassificationIntegrationTest {

    // Real service topics (not test topics)
    private static final String INPUT_TOPIC = "decorated-flow-v1";
    private static final String OUTPUT_TOPIC = "ip-classification-v1";
    private static final String TEST_TENANT_ID = "integration-test-tenant";
    private static final String KAFKA_BOOTSTRAP_SERVERS = "localhost:19092";

    private KafkaProducer<String, String> kafkaProducer;
    private KafkaReceiver<String, String> kafkaConsumer;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        setupKafkaProducer();
        setupKafkaConsumer();
        log.info("Integration test setup completed for real IP classification service");
    }

    @AfterEach
    void tearDown() {
        if (kafkaProducer != null) {
            kafkaProducer.close();
        }
        if (kafkaConsumer != null) {
            kafkaConsumer.close();
        }
    }

    @Test
    @Order(1)
    @DisplayName("Test real-time data ingestion and processing")
    void testRealTimeDataIngestionAndProcessing() throws Exception {
        log.info("=".repeat(60));
        log.info("REAL-TIME IP CLASSIFICATION INTEGRATION TEST");
        log.info("=".repeat(60));

        // Generate test data
        List<String> testJsonData = generateRealTestData();
        int expectedRecords = testJsonData.size();
        
        CountDownLatch processedLatch = new CountDownLatch(expectedRecords);
        List<JsonNode> processedRecords = Collections.synchronizedList(new ArrayList<>());

        // Start consuming processed data
        Flux<ConsumerRecord<String, String>> consumerFlux = kafkaConsumer
                .receiveAutoAck()
                .flatMap(records -> records)
                .filter(record -> record.key() != null && record.key().contains(TEST_TENANT_ID))
                .doOnNext(record -> {
                    try {
                        JsonNode processedData = objectMapper.readTree(record.value());
                        processedRecords.add(processedData);
                        
                        log.info("Received processed record: key={}, hasIpEnrichment={}", 
                                record.key(), 
                                hasIpEnrichmentData(processedData));
                        
                        processedLatch.countDown();
                    } catch (Exception e) {
                        log.error("Error processing received record", e);
                    }
                })
                .take(expectedRecords);

        // Subscribe to consumer
        consumerFlux.subscribe();

        // Send test data to real service
        log.info("Sending {} test records to real IP classification service", expectedRecords);
        sendTestDataToRealService(testJsonData);

        // Wait for processing
        boolean allProcessed = processedLatch.await(60, TimeUnit.SECONDS);
        
        // Assertions
        Assertions.assertTrue(allProcessed, 
                "Not all records were processed within timeout. Expected: " + expectedRecords + 
                ", Processed: " + (expectedRecords - processedLatch.getCount()));

        Assertions.assertEquals(expectedRecords, processedRecords.size(), 
                "Number of processed records doesn't match input");

        // Verify IP enrichment
        long enrichedCount = processedRecords.stream()
                .mapToLong(record -> hasIpEnrichmentData(record) ? 1 : 0)
                .sum();

        log.info("IP Enrichment Results: {}/{} records enriched", enrichedCount, processedRecords.size());
        
        // At least some records should be enriched (depends on BlueMmdb service)
        if (enrichedCount == 0) {
            log.warn("No records were IP enriched - BlueMmdb service may not be running");
        }

        log.info("✅ Real-time integration test completed successfully");
    }

    @Test
    @Order(2)
    @DisplayName("Test hourly batch processing")
    void testHourlyBatchProcessing() throws Exception {
        log.info("Testing hourly batch processing simulation");

        // Simulate 3 hours of data
        for (int hour = 0; hour < 3; hour++) {
            log.info("Processing hour {} data", hour + 1);
            
            List<String> hourlyData = generateHourlyBatchData(hour);
            sendTestDataToRealService(hourlyData);
            
            // Small delay between hours
            Thread.sleep(2000);
        }

        log.info("✅ Hourly batch processing test completed");
    }

    @Test
    @Order(3)
    @DisplayName("Test service resilience with malformed data")
    void testServiceResilienceWithMalformedData() throws Exception {
        log.info("Testing service resilience with malformed data");

        List<String> testData = new ArrayList<>();
        
        // Add valid record
        testData.add(createValidDecoratedFlowJson(0));
        
        // Add malformed records
        testData.add("{\"invalid\": \"json\", \"missing\": \"required_fields\"}");
        testData.add("not-json-at-all");
        testData.add("{}"); // Empty JSON
        
        // Add another valid record
        testData.add(createValidDecoratedFlowJson(1));

        // Send data and verify service doesn't crash
        sendTestDataToRealService(testData);
        
        // Give time for processing
        Thread.sleep(5000);
        
        log.info("✅ Service resilience test completed");
    }

    private void setupKafkaProducer() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, KAFKA_BOOTSTRAP_SERVERS);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 10);

        kafkaProducer = new KafkaProducer<>(props);
    }

    private void setupKafkaConsumer() {
        Map<String, Object> consumerProps = new HashMap<>();
        consumerProps.put(ConsumerConfig.BOOTSTRAP_SERVERS_CONFIG, KAFKA_BOOTSTRAP_SERVERS);
        consumerProps.put(ConsumerConfig.GROUP_ID_CONFIG, "integration-test-consumer-" + UUID.randomUUID());
        consumerProps.put(ConsumerConfig.KEY_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.VALUE_DESERIALIZER_CLASS_CONFIG, StringDeserializer.class);
        consumerProps.put(ConsumerConfig.AUTO_OFFSET_RESET_CONFIG, "latest");
        consumerProps.put(ConsumerConfig.SESSION_TIMEOUT_MS_CONFIG, 30000);
        consumerProps.put(ConsumerConfig.HEARTBEAT_INTERVAL_MS_CONFIG, 10000);

        ReceiverOptions<String, String> receiverOptions = ReceiverOptions.<String, String>create(consumerProps)
                .subscription(Collections.singleton(OUTPUT_TOPIC));
        kafkaConsumer = KafkaReceiver.create(receiverOptions);
    }

    private List<String> generateRealTestData() throws Exception {
        List<String> testData = new ArrayList<>();
        
        // Generate diverse test scenarios
        testData.add(createValidDecoratedFlowJson(0)); // HTTPS traffic
        testData.add(createDnsTrafficJson(1)); // DNS traffic
        testData.add(createHttpTrafficJson(2)); // HTTP traffic
        testData.add(createSshTrafficJson(3)); // SSH traffic
        testData.add(createDatabaseTrafficJson(4)); // Database traffic
        
        return testData;
    }

    private List<String> generateHourlyBatchData(int hour) throws Exception {
        List<String> batchData = new ArrayList<>();
        Random random = new Random();
        
        // Generate 10-15 records per hour
        int recordCount = 10 + random.nextInt(5);
        
        for (int i = 0; i < recordCount; i++) {
            batchData.add(createValidDecoratedFlowJson(hour * 100 + i));
        }
        
        return batchData;
    }

    private void sendTestDataToRealService(List<String> testData) throws Exception {
        for (int i = 0; i < testData.size(); i++) {
            String jsonData = testData.get(i);
            String key = TEST_TENANT_ID + "-" + System.currentTimeMillis() + "-" + i;
            
            ProducerRecord<String, String> record = new ProducerRecord<>(
                INPUT_TOPIC, 
                key, 
                jsonData
            );
            
            RecordMetadata metadata = kafkaProducer.send(record).get();
            log.debug("Sent record to real service: partition={}, offset={}", 
                    metadata.partition(), metadata.offset());
        }
        
        log.info("Successfully sent {} records to real IP classification service", testData.size());
    }

    private String createValidDecoratedFlowJson(int index) throws Exception {
        Map<String, Object> flow = new HashMap<>();
        
        flow.put("SrcIP", "192.168.1." + (100 + index));
        flow.put("DestIP", "*******");
        flow.put("SrcId", "integration-src-" + index);
        flow.put("CSSrcId", "css-integration-src-" + index);
        flow.put("DestId", "integration-dest-" + index);
        flow.put("CSDestId", "css-integration-dest-" + index);
        flow.put("Port", 443);
        flow.put("Proto", "tcp");
        flow.put("SentBytes", 1024 * (index + 1));
        flow.put("ReceivedBytes", 2048 * (index + 1));
        flow.put("IllumioTenantId", TEST_TENANT_ID);
        flow.put("SrcTenantId", TEST_TENANT_ID);
        flow.put("SrcSubId", "integration-subscription-" + (index % 3));
        flow.put("SrcRegion", "us-east-1");
        flow.put("SrcResId", "integration-resource-" + index);
        flow.put("SrcVnetId", "integration-vnet-" + (index % 2));
        flow.put("SrcUserName", "/org/integration/user/testuser" + index);
        flow.put("DestTenantId", "external-tenant");
        flow.put("DestSubId", "external-sub");
        flow.put("DestRegion", "us-west-2");
        flow.put("DestResId", "external-resource-" + index);
        flow.put("DestVnetId", "external-vnet");
        flow.put("DestUserName", "/org/external/user/external" + index);
        flow.put("Timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        flow.put("TestType", "integration");
        
        return objectMapper.writeValueAsString(flow);
    }

    private String createDnsTrafficJson(int index) throws Exception {
        Map<String, Object> flow = new HashMap<>();
        flow.put("SrcIP", "10.0.0." + (50 + index));
        flow.put("DestIP", "*******");
        flow.put("Port", 53);
        flow.put("Proto", "udp");
        flow.put("SentBytes", 64);
        flow.put("ReceivedBytes", 128);
        flow.put("IllumioTenantId", TEST_TENANT_ID);
        flow.put("SrcTenantId", TEST_TENANT_ID);
        flow.put("TestType", "dns");
        // Add other required fields...
        return objectMapper.writeValueAsString(flow);
    }

    private String createHttpTrafficJson(int index) throws Exception {
        Map<String, Object> flow = new HashMap<>();
        flow.put("SrcIP", "172.16.0." + (25 + index));
        flow.put("DestIP", "**************");
        flow.put("Port", 80);
        flow.put("Proto", "tcp");
        flow.put("SentBytes", 512);
        flow.put("ReceivedBytes", 1024);
        flow.put("IllumioTenantId", TEST_TENANT_ID);
        flow.put("SrcTenantId", TEST_TENANT_ID);
        flow.put("TestType", "http");
        // Add other required fields...
        return objectMapper.writeValueAsString(flow);
    }

    private String createSshTrafficJson(int index) throws Exception {
        Map<String, Object> flow = new HashMap<>();
        flow.put("SrcIP", "192.168.2." + (75 + index));
        flow.put("DestIP", "************");
        flow.put("Port", 22);
        flow.put("Proto", "tcp");
        flow.put("SentBytes", 256);
        flow.put("ReceivedBytes", 512);
        flow.put("IllumioTenantId", TEST_TENANT_ID);
        flow.put("SrcTenantId", TEST_TENANT_ID);
        flow.put("TestType", "ssh");
        // Add other required fields...
        return objectMapper.writeValueAsString(flow);
    }

    private String createDatabaseTrafficJson(int index) throws Exception {
        Map<String, Object> flow = new HashMap<>();
        flow.put("SrcIP", "10.1.1." + (100 + index));
        flow.put("DestIP", "************");
        flow.put("Port", 5432);
        flow.put("Proto", "tcp");
        flow.put("SentBytes", 2048);
        flow.put("ReceivedBytes", 4096);
        flow.put("IllumioTenantId", TEST_TENANT_ID);
        flow.put("SrcTenantId", TEST_TENANT_ID);
        flow.put("TestType", "database");
        // Add other required fields...
        return objectMapper.writeValueAsString(flow);
    }

    private boolean hasIpEnrichmentData(JsonNode record) {
        // Check if the record has IP enrichment fields added by the service
        return record.has("SrcThreatLevel") || 
               record.has("DestThreatLevel") ||
               record.has("SrcCountry") ||
               record.has("DestCountry") ||
               record.has("SrcCity") ||
               record.has("DestCity");
    }
}
