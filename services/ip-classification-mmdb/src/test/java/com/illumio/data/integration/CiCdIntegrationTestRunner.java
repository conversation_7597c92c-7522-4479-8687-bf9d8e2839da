package com.illumio.data.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.admin.AdminClient;
import org.apache.kafka.clients.admin.AdminClientConfig;
import org.apache.kafka.clients.admin.ListTopicsResult;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.common.serialization.StringSerializer;
import org.junit.jupiter.api.BeforeAll;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.TestInstance;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;

/**
 * CI/CD Integration Test Runner for IP Classification Service
 * 
 * This test is designed to run in CI/CD pipelines and validates:
 * 1. Service connectivity and health
 * 2. Real-time data processing
 * 3. End-to-end workflow validation
 * 4. Performance under load
 * 
 * Environment Variables for CI/CD:
 * - KAFKA_BOOTSTRAP_SERVERS: Kafka broker address
 * - BLUEMMDB_HOST: BlueMmdb service host
 * - BLUEMMDB_PORT: BlueMmdb service port
 * - TEST_TENANT_ID: Tenant ID for testing
 * - CI_BUILD_ID: Build identifier for tracking
 */
@Slf4j
@SpringBootTest
@TestPropertySource(locations = "classpath:application-integration.yml")
@TestInstance(TestInstance.Lifecycle.PER_CLASS)
public class CiCdIntegrationTestRunner {

    private static final String INPUT_TOPIC = "decorated-flow-v1";
    private static final String OUTPUT_TOPIC = "ip-classification-v1";
    
    // Use environment variables for CI/CD
    private final String kafkaBootstrapServers = System.getenv().getOrDefault("KAFKA_BOOTSTRAP_SERVERS", "localhost:19092");
    private final String testTenantId = System.getenv().getOrDefault("TEST_TENANT_ID", "ci-integration-test");
    private final String buildId = System.getenv().getOrDefault("CI_BUILD_ID", "local-" + System.currentTimeMillis());
    
    private KafkaProducer<String, String> kafkaProducer;
    private ObjectMapper objectMapper;

    @BeforeAll
    void setupCiCdTest() {
        objectMapper = new ObjectMapper();
        setupKafkaProducer();
        
        log.info("=".repeat(80));
        log.info("CI/CD INTEGRATION TEST RUNNER");
        log.info("=".repeat(80));
        log.info("Build ID: {}", buildId);
        log.info("Test Tenant: {}", testTenantId);
        log.info("Kafka Brokers: {}", kafkaBootstrapServers);
        log.info("Input Topic: {}", INPUT_TOPIC);
        log.info("Output Topic: {}", OUTPUT_TOPIC);
        log.info("=".repeat(80));
    }

    @Test
    void testServiceConnectivity() throws Exception {
        log.info("🔍 Testing service connectivity...");
        
        // Test Kafka connectivity
        try (AdminClient adminClient = createAdminClient()) {
            ListTopicsResult topics = adminClient.listTopics();
            Set<String> topicNames = topics.names().get(30, TimeUnit.SECONDS);
            
            boolean inputTopicExists = topicNames.contains(INPUT_TOPIC);
            boolean outputTopicExists = topicNames.contains(OUTPUT_TOPIC);
            
            log.info("Kafka Topics Found: {}", topicNames.size());
            log.info("Input Topic '{}' exists: {}", INPUT_TOPIC, inputTopicExists);
            log.info("Output Topic '{}' exists: {}", OUTPUT_TOPIC, outputTopicExists);
            
            if (!inputTopicExists || !outputTopicExists) {
                log.warn("Required topics are missing - this may cause test failures");
            }
        }
        
        log.info("✅ Service connectivity test completed");
    }

    @Test
    void testRealTimeDataProcessing() throws Exception {
        log.info("🚀 Testing real-time data processing...");
        
        // Generate test data with CI/CD identifiers
        List<String> testData = generateCiCdTestData();
        
        // Send data to real service
        for (int i = 0; i < testData.size(); i++) {
            String jsonData = testData.get(i);
            String key = String.format("%s-%s-record-%d", testTenantId, buildId, i);
            
            ProducerRecord<String, String> record = new ProducerRecord<>(
                INPUT_TOPIC, 
                key, 
                jsonData
            );
            
            kafkaProducer.send(record).get(10, TimeUnit.SECONDS);
            log.debug("Sent CI/CD test record {}/{}", i + 1, testData.size());
        }
        
        log.info("✅ Successfully sent {} test records to IP classification service", testData.size());
        
        // Give time for processing
        Thread.sleep(10000);
        
        log.info("✅ Real-time data processing test completed");
    }

    @Test
    void testHourlyDataIngestionSimulation() throws Exception {
        log.info("⏰ Testing hourly data ingestion simulation...");
        
        // Simulate 2 hours of data for CI/CD
        for (int hour = 0; hour < 2; hour++) {
            log.info("Processing hour {} data for CI/CD test", hour + 1);
            
            List<String> hourlyData = generateHourlyTestData(hour);
            
            for (int i = 0; i < hourlyData.size(); i++) {
                String jsonData = hourlyData.get(i);
                String key = String.format("%s-%s-hour-%d-record-%d", testTenantId, buildId, hour, i);
                
                ProducerRecord<String, String> record = new ProducerRecord<>(
                    INPUT_TOPIC, 
                    key, 
                    jsonData
                );
                
                kafkaProducer.send(record);
            }
            
            log.info("Sent {} records for hour {}", hourlyData.size(), hour + 1);
            
            // Small delay between hours
            Thread.sleep(2000);
        }
        
        // Flush all records
        kafkaProducer.flush();
        
        log.info("✅ Hourly data ingestion simulation completed");
    }

    @Test
    void testPerformanceUnderLoad() throws Exception {
        log.info("📊 Testing performance under load...");
        
        int totalRecords = 100;
        long startTime = System.currentTimeMillis();
        
        // Generate load test data
        List<String> loadTestData = new ArrayList<>();
        for (int i = 0; i < totalRecords; i++) {
            loadTestData.add(createLoadTestRecord(i));
        }
        
        // Send all records as fast as possible
        for (int i = 0; i < loadTestData.size(); i++) {
            String jsonData = loadTestData.get(i);
            String key = String.format("%s-%s-load-test-%d", testTenantId, buildId, i);
            
            ProducerRecord<String, String> record = new ProducerRecord<>(
                INPUT_TOPIC, 
                key, 
                jsonData
            );
            
            kafkaProducer.send(record);
        }
        
        kafkaProducer.flush();
        long endTime = System.currentTimeMillis();
        
        long duration = endTime - startTime;
        double recordsPerSecond = (double) totalRecords / (duration / 1000.0);
        
        log.info("Performance Results:");
        log.info("  Total Records: {}", totalRecords);
        log.info("  Duration: {} ms", duration);
        log.info("  Records/Second: {:.2f}", recordsPerSecond);
        
        log.info("✅ Performance test completed");
    }

    private void setupKafkaProducer() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBootstrapServers);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 5);
        props.put(ProducerConfig.REQUEST_TIMEOUT_MS_CONFIG, 30000);
        props.put(ProducerConfig.DELIVERY_TIMEOUT_MS_CONFIG, 60000);

        kafkaProducer = new KafkaProducer<>(props);
    }

    private AdminClient createAdminClient() {
        Properties props = new Properties();
        props.put(AdminClientConfig.BOOTSTRAP_SERVERS_CONFIG, kafkaBootstrapServers);
        props.put(AdminClientConfig.REQUEST_TIMEOUT_MS_CONFIG, 30000);
        return AdminClient.create(props);
    }

    private List<String> generateCiCdTestData() throws Exception {
        List<String> testData = new ArrayList<>();
        
        // Generate diverse test scenarios for CI/CD
        testData.add(createCiCdTestRecord(0, "https-traffic"));
        testData.add(createCiCdTestRecord(1, "dns-traffic"));
        testData.add(createCiCdTestRecord(2, "http-traffic"));
        testData.add(createCiCdTestRecord(3, "ssh-traffic"));
        testData.add(createCiCdTestRecord(4, "database-traffic"));
        
        return testData;
    }

    private List<String> generateHourlyTestData(int hour) throws Exception {
        List<String> hourlyData = new ArrayList<>();
        Random random = new Random();
        
        // Generate 15-20 records per hour for CI/CD
        int recordCount = 15 + random.nextInt(5);
        
        for (int i = 0; i < recordCount; i++) {
            hourlyData.add(createCiCdTestRecord(hour * 100 + i, "hourly-batch"));
        }
        
        return hourlyData;
    }

    private String createCiCdTestRecord(int index, String testType) throws Exception {
        Map<String, Object> flow = new HashMap<>();
        
        flow.put("SrcIP", "192.168.1." + (100 + index % 50));
        flow.put("DestIP", "8.8.8." + (8 + index % 3));
        flow.put("SrcId", "cicd-src-" + buildId + "-" + index);
        flow.put("CSSrcId", "css-cicd-src-" + buildId + "-" + index);
        flow.put("DestId", "cicd-dest-" + buildId + "-" + index);
        flow.put("CSDestId", "css-cicd-dest-" + buildId + "-" + index);
        flow.put("Port", 443 + index % 100);
        flow.put("Proto", index % 2 == 0 ? "tcp" : "udp");
        flow.put("SentBytes", 1024 * (index + 1));
        flow.put("ReceivedBytes", 2048 * (index + 1));
        flow.put("IllumioTenantId", testTenantId);
        flow.put("SrcTenantId", testTenantId);
        flow.put("SrcSubId", "cicd-subscription-" + (index % 3));
        flow.put("SrcRegion", "us-east-1");
        flow.put("SrcResId", "cicd-resource-" + buildId + "-" + index);
        flow.put("SrcVnetId", "cicd-vnet-" + (index % 2));
        flow.put("SrcUserName", "/org/cicd/user/testuser" + index);
        flow.put("DestTenantId", "external-tenant");
        flow.put("DestSubId", "external-sub");
        flow.put("DestRegion", "us-west-2");
        flow.put("DestResId", "external-resource-" + index);
        flow.put("DestVnetId", "external-vnet");
        flow.put("DestUserName", "/org/external/user/external" + index);
        
        // CI/CD specific metadata
        flow.put("BuildId", buildId);
        flow.put("TestType", testType);
        flow.put("Timestamp", LocalDateTime.now().format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
        flow.put("CiCdTest", true);
        
        return objectMapper.writeValueAsString(flow);
    }

    private String createLoadTestRecord(int index) throws Exception {
        Map<String, Object> flow = new HashMap<>();
        Random random = new Random();
        
        flow.put("SrcIP", "10.0." + (index / 256) + "." + (index % 256));
        flow.put("DestIP", "*******");
        flow.put("Port", 80 + random.nextInt(1000));
        flow.put("Proto", "tcp");
        flow.put("SentBytes", random.nextInt(5000) + 100);
        flow.put("ReceivedBytes", random.nextInt(10000) + 200);
        flow.put("IllumioTenantId", testTenantId);
        flow.put("SrcTenantId", testTenantId);
        flow.put("BuildId", buildId);
        flow.put("TestType", "load-test");
        flow.put("LoadTestIndex", index);
        
        return objectMapper.writeValueAsString(flow);
    }
}
