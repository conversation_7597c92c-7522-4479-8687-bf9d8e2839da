package com.illumio.data.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.StringSerializer;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;

import java.util.*;
import java.util.concurrent.Future;

/**
 * Integration test that feeds JSON data directly to the IP classification service via Kafka.
 * This test demonstrates how to send initial decorated flow data in JSON format.
 */
@Slf4j
@SpringBootTest
@TestPropertySource(locations = "classpath:application-integration-test.yml")
public class JsonDataFeederIntegrationTest {

    private static final String INPUT_TOPIC = "decorated-flow-v1-test";
    private static final String TEST_TENANT_ID = "test-tenant-12345";
    private static final String KAFKA_BOOTSTRAP_SERVERS = "localhost:19092";

    private KafkaProducer<String, String> kafkaProducer;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        setupKafkaProducer();
    }

    @AfterEach
    void tearDown() {
        if (kafkaProducer != null) {
            kafkaProducer.close();
        }
    }

    @Test
    void testFeedInitialJsonDataToIpClassificationService() throws Exception {
        log.info("Starting to feed initial JSON data to IP classification service");

        // Create sample decorated flow JSON data
        List<String> jsonDataList = createSampleDecoratedFlowJsonData();

        // Send each JSON record to Kafka
        List<Future<RecordMetadata>> futures = new ArrayList<>();
        
        for (int i = 0; i < jsonDataList.size(); i++) {
            String jsonData = jsonDataList.get(i);
            String key = TEST_TENANT_ID + "-record-" + i;
            
            ProducerRecord<String, String> record = new ProducerRecord<>(
                INPUT_TOPIC, 
                key, 
                jsonData
            );

            log.info("Sending JSON record {}: {}", i + 1, jsonData);
            Future<RecordMetadata> future = kafkaProducer.send(record);
            futures.add(future);
        }

        // Wait for all records to be sent
        for (Future<RecordMetadata> future : futures) {
            RecordMetadata metadata = future.get();
            log.info("Record sent successfully to topic: {}, partition: {}, offset: {}", 
                    metadata.topic(), metadata.partition(), metadata.offset());
        }

        log.info("Successfully fed {} JSON records to IP classification service", jsonDataList.size());
        
        // Give some time for processing
        Thread.sleep(5000);
    }

    @Test
    void testFeedHourlyBatchData() throws Exception {
        log.info("Feeding hourly batch of JSON data");

        // Simulate hourly data ingestion
        for (int hour = 0; hour < 3; hour++) {
            log.info("Feeding data for hour: {}", hour + 1);
            
            List<String> hourlyData = createHourlyBatchJsonData(hour);
            
            for (String jsonData : hourlyData) {
                String key = TEST_TENANT_ID + "-hour-" + hour + "-" + UUID.randomUUID();
                ProducerRecord<String, String> record = new ProducerRecord<>(
                    INPUT_TOPIC, 
                    key, 
                    jsonData
                );
                
                kafkaProducer.send(record).get(); // Wait for each record
            }
            
            log.info("Completed feeding {} records for hour {}", hourlyData.size(), hour + 1);
            Thread.sleep(1000); // Small delay between hours
        }
    }

    private void setupKafkaProducer() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, KAFKA_BOOTSTRAP_SERVERS);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 10);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);

        kafkaProducer = new KafkaProducer<>(props);
    }

    private List<String> createSampleDecoratedFlowJsonData() throws Exception {
        List<String> jsonDataList = new ArrayList<>();

        // Sample 1: HTTPS traffic
        Map<String, Object> flow1 = new HashMap<>();
        flow1.put("SrcIP", "*************");
        flow1.put("SrcId", "src-vm-001");
        flow1.put("CSSrcId", "css-src-001");
        flow1.put("DestIP", "*******");
        flow1.put("DestId", "dest-dns-001");
        flow1.put("CSDestId", "css-dest-001");
        flow1.put("Port", 443);
        flow1.put("Proto", "tcp");
        flow1.put("SentBytes", 1024);
        flow1.put("ReceivedBytes", 2048);
        flow1.put("IllumioTenantId", TEST_TENANT_ID);
        flow1.put("SrcTenantId", TEST_TENANT_ID);
        flow1.put("SrcSubId", "subscription-001");
        flow1.put("SrcRegion", "us-east-1");
        flow1.put("SrcResId", "resource-vm-001");
        flow1.put("SrcVnetId", "vnet-001");
        flow1.put("SrcUserName", "/org/test-org/user/alice");
        flow1.put("DestTenantId", "external-tenant");
        flow1.put("DestSubId", "external-sub");
        flow1.put("DestRegion", "us-west-1");
        flow1.put("DestResId", "external-resource");
        flow1.put("DestVnetId", "external-vnet");
        flow1.put("DestUserName", "/org/external/user/dns");
        jsonDataList.add(objectMapper.writeValueAsString(flow1));

        // Sample 2: HTTP traffic
        Map<String, Object> flow2 = new HashMap<>();
        flow2.put("SrcIP", "*********");
        flow2.put("SrcId", "src-vm-002");
        flow2.put("CSSrcId", "css-src-002");
        flow2.put("DestIP", "*******");
        flow2.put("DestId", "dest-dns-002");
        flow2.put("CSDestId", "css-dest-002");
        flow2.put("Port", 80);
        flow2.put("Proto", "tcp");
        flow2.put("SentBytes", 512);
        flow2.put("ReceivedBytes", 1024);
        flow2.put("IllumioTenantId", TEST_TENANT_ID);
        flow2.put("SrcTenantId", TEST_TENANT_ID);
        flow2.put("SrcSubId", "subscription-002");
        flow2.put("SrcRegion", "eu-west-1");
        flow2.put("SrcResId", "resource-vm-002");
        flow2.put("SrcVnetId", "vnet-002");
        flow2.put("SrcUserName", "/org/test-org/user/bob");
        flow2.put("DestTenantId", "external-tenant");
        flow2.put("DestSubId", "external-sub");
        flow2.put("DestRegion", "us-west-2");
        flow2.put("DestResId", "external-resource-2");
        flow2.put("DestVnetId", "external-vnet-2");
        flow2.put("DestUserName", "/org/external/user/cloudflare");
        jsonDataList.add(objectMapper.writeValueAsString(flow2));

        // Sample 3: DNS traffic
        Map<String, Object> flow3 = new HashMap<>();
        flow3.put("SrcIP", "***********");
        flow3.put("SrcId", "src-vm-003");
        flow3.put("CSSrcId", "css-src-003");
        flow3.put("DestIP", "**************");
        flow3.put("DestId", "dest-dns-003");
        flow3.put("CSDestId", "css-dest-003");
        flow3.put("Port", 53);
        flow3.put("Proto", "udp");
        flow3.put("SentBytes", 64);
        flow3.put("ReceivedBytes", 128);
        flow3.put("IllumioTenantId", TEST_TENANT_ID);
        flow3.put("SrcTenantId", TEST_TENANT_ID);
        flow3.put("SrcSubId", "subscription-003");
        flow3.put("SrcRegion", "ap-southeast-1");
        flow3.put("SrcResId", "resource-vm-003");
        flow3.put("SrcVnetId", "vnet-003");
        flow3.put("SrcUserName", "/org/test-org/user/charlie");
        flow3.put("DestTenantId", "external-tenant");
        flow3.put("DestSubId", "external-sub");
        flow3.put("DestRegion", "us-east-2");
        flow3.put("DestResId", "external-resource-3");
        flow3.put("DestVnetId", "external-vnet-3");
        flow3.put("DestUserName", "/org/external/user/opendns");
        jsonDataList.add(objectMapper.writeValueAsString(flow3));

        return jsonDataList;
    }

    private List<String> createHourlyBatchJsonData(int hourOffset) throws Exception {
        List<String> jsonDataList = new ArrayList<>();
        Random random = new Random();

        // Generate 10-20 records per hour
        int recordCount = 10 + random.nextInt(10);
        
        for (int i = 0; i < recordCount; i++) {
            Map<String, Object> flow = new HashMap<>();
            
            // Generate varied IP addresses
            flow.put("SrcIP", "192.168." + (hourOffset + 1) + "." + (100 + i));
            flow.put("DestIP", "8.8." + (8 + i % 3) + "." + (8 + i % 10));
            flow.put("SrcId", "src-vm-" + hourOffset + "-" + i);
            flow.put("CSSrcId", "css-src-" + hourOffset + "-" + i);
            flow.put("DestId", "dest-" + hourOffset + "-" + i);
            flow.put("CSDestId", "css-dest-" + hourOffset + "-" + i);
            
            // Vary ports and protocols
            int[] ports = {80, 443, 53, 22, 3389, 8080};
            String[] protocols = {"tcp", "udp"};
            flow.put("Port", ports[random.nextInt(ports.length)]);
            flow.put("Proto", protocols[random.nextInt(protocols.length)]);
            
            flow.put("SentBytes", random.nextInt(5000) + 100);
            flow.put("ReceivedBytes", random.nextInt(10000) + 200);
            
            // Consistent tenant info
            flow.put("IllumioTenantId", TEST_TENANT_ID);
            flow.put("SrcTenantId", TEST_TENANT_ID);
            flow.put("SrcSubId", "subscription-" + (i % 3));
            flow.put("SrcRegion", "us-east-1");
            flow.put("SrcResId", "resource-vm-" + i);
            flow.put("SrcVnetId", "vnet-" + (i % 2));
            flow.put("SrcUserName", "/org/test-org/user/user" + i);
            flow.put("DestTenantId", "external-tenant");
            flow.put("DestSubId", "external-sub");
            flow.put("DestRegion", "us-west-2");
            flow.put("DestResId", "external-resource-" + i);
            flow.put("DestVnetId", "external-vnet");
            flow.put("DestUserName", "/org/external/user/external" + i);
            
            // Add timestamp for this hour
            flow.put("HourOffset", hourOffset);
            flow.put("RecordIndex", i);
            
            jsonDataList.add(objectMapper.writeValueAsString(flow));
        }

        return jsonDataList;
    }
}
