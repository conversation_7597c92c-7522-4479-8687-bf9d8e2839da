package com.illumio.data.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.StringSerializer;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.Future;

/**
 * Service that automatically ingests JSON data to IP classification service every hour.
 * This service runs continuously and feeds data at scheduled intervals.
 * 
 * To enable: Set integration.test.hourly-ingestion.enabled=true in application properties
 * 
 * Scheduling options:
 * - Every hour: @Scheduled(cron = "0 0 * * * *")
 * - Every minute (testing): @Scheduled(fixedRate = 60000)
 * - Every 5 minutes (testing): @Scheduled(fixedRate = 300000)
 */
@Slf4j
@Service
@RequiredArgsConstructor
@ConditionalOnProperty(name = "integration.test.hourly-ingestion.enabled", havingValue = "true")
public class AutomaticHourlyDataIngestionService {

    private static final String INPUT_TOPIC = "decorated-flow-v1";
    private static final String KAFKA_BOOTSTRAP_SERVERS = "localhost:19092";

    @Value("${integration.test.tenant-id:test-tenant-12345}")
    private String testTenantId;

    @Value("${integration.test.records-per-hour:20}")
    private int recordsPerHour;

    private final ObjectMapper objectMapper;
    private KafkaProducer<String, String> kafkaProducer;
    private int ingestionCounter = 0;

    @PostConstruct
    public void init() {
        setupKafkaProducer();
        log.info("AutomaticHourlyDataIngestionService initialized");
        log.info("Tenant ID: {}", testTenantId);
        log.info("Records per hour: {}", recordsPerHour);
        log.info("Target topic: {}", INPUT_TOPIC);
        log.info("Kafka broker: {}", KAFKA_BOOTSTRAP_SERVERS);
    }

    @PreDestroy
    public void cleanup() {
        if (kafkaProducer != null) {
            kafkaProducer.close();
            log.info("Kafka producer closed");
        }
    }

    /**
     * PRODUCTION: Runs every hour at minute 0
     * For testing, you can change this to:
     * @Scheduled(fixedRate = 60000) // Every minute
     * @Scheduled(fixedRate = 300000) // Every 5 minutes
     */
    @Scheduled(cron = "0 0 * * * *") // Every hour at minute 0
    public void ingestHourlyData() {
        try {
            ingestionCounter++;
            LocalDateTime now = LocalDateTime.now();
            String timestamp = now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);
            
            log.info("=".repeat(60));
            log.info("HOURLY DATA INGESTION #{} - {}", ingestionCounter, timestamp);
            log.info("=".repeat(60));
            
            List<String> jsonDataList = generateHourlyJsonData(now);
            
            int successCount = 0;
            int failureCount = 0;
            
            for (int i = 0; i < jsonDataList.size(); i++) {
                try {
                    String jsonData = jsonDataList.get(i);
                    String key = testTenantId + "-auto-" + ingestionCounter + "-" + i;
                    
                    ProducerRecord<String, String> record = new ProducerRecord<>(
                        INPUT_TOPIC, 
                        key, 
                        jsonData
                    );
                    
                    Future<RecordMetadata> future = kafkaProducer.send(record);
                    RecordMetadata metadata = future.get(); // Wait for completion
                    
                    log.debug("Record {} sent to partition {} at offset {}", 
                            i + 1, metadata.partition(), metadata.offset());
                    successCount++;
                    
                } catch (Exception e) {
                    log.error("Failed to send record {}", i + 1, e);
                    failureCount++;
                }
            }
            
            log.info("Hourly ingestion #{} completed:", ingestionCounter);
            log.info("  ✅ Success: {} records", successCount);
            if (failureCount > 0) {
                log.warn("  ❌ Failed: {} records", failureCount);
            }
            log.info("  📊 Total records sent: {}", successCount);
            log.info("  🕐 Next ingestion: {}", now.plusHours(1).format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
        } catch (Exception e) {
            log.error("Error during hourly data ingestion #{}", ingestionCounter, e);
        }
    }

    /**
     * For testing purposes - can be called manually to trigger immediate ingestion
     */
    public void triggerImmediateIngestion() {
        log.info("Triggering immediate data ingestion (manual)");
        ingestHourlyData();
    }

    /**
     * Alternative scheduling for testing - every minute
     * Uncomment this and comment the hourly method for faster testing
     */
    // @Scheduled(fixedRate = 60000) // Every minute
    public void ingestDataEveryMinute() {
        try {
            ingestionCounter++;
            LocalDateTime now = LocalDateTime.now();
            
            log.info("MINUTE DATA INGESTION #{} - {}", 
                    ingestionCounter, now.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME));
            
            // Generate fewer records for minute-based testing
            List<String> jsonDataList = generateTestJsonData(now, 5);
            
            for (int i = 0; i < jsonDataList.size(); i++) {
                String jsonData = jsonDataList.get(i);
                String key = testTenantId + "-minute-" + ingestionCounter + "-" + i;
                
                ProducerRecord<String, String> record = new ProducerRecord<>(
                    INPUT_TOPIC, 
                    key, 
                    jsonData
                );
                
                kafkaProducer.send(record).get();
            }
            
            log.info("Sent {} records for minute ingestion #{}", jsonDataList.size(), ingestionCounter);
            
        } catch (Exception e) {
            log.error("Error during minute data ingestion #{}", ingestionCounter, e);
        }
    }

    private void setupKafkaProducer() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, KAFKA_BOOTSTRAP_SERVERS);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 10);
        props.put(ProducerConfig.BUFFER_MEMORY_CONFIG, 33554432);

        kafkaProducer = new KafkaProducer<>(props);
        log.info("Kafka producer initialized for broker: {}", KAFKA_BOOTSTRAP_SERVERS);
    }

    private List<String> generateHourlyJsonData(LocalDateTime timestamp) throws Exception {
        return generateTestJsonData(timestamp, recordsPerHour);
    }

    private List<String> generateTestJsonData(LocalDateTime timestamp, int recordCount) throws Exception {
        List<String> jsonDataList = new ArrayList<>();
        Random random = new Random();
        String timestampStr = timestamp.format(DateTimeFormatter.ISO_LOCAL_DATE_TIME);

        for (int i = 0; i < recordCount; i++) {
            Map<String, Object> flow = new HashMap<>();
            
            // Generate realistic IP patterns
            String[] srcIpPrefixes = {"192.168.1.", "10.0.0.", "172.16.0.", "192.168.2."};
            String[] destIpPrefixes = {"8.8.8.", "1.1.1.", "208.67.222.", "13.107.42."};
            
            String srcIpPrefix = srcIpPrefixes[random.nextInt(srcIpPrefixes.length)];
            String destIpPrefix = destIpPrefixes[random.nextInt(destIpPrefixes.length)];
            
            flow.put("SrcIP", srcIpPrefix + (100 + i % 50));
            flow.put("DestIP", destIpPrefix + (1 + i % 20));
            flow.put("SrcId", "auto-src-" + ingestionCounter + "-" + i);
            flow.put("CSSrcId", "css-auto-src-" + ingestionCounter + "-" + i);
            flow.put("DestId", "auto-dest-" + ingestionCounter + "-" + i);
            flow.put("CSDestId", "css-auto-dest-" + ingestionCounter + "-" + i);
            
            // Realistic ports and protocols
            int[] commonPorts = {80, 443, 53, 22, 3389, 8080, 8443, 9090, 5432, 3306};
            String[] protocols = {"tcp", "udp"};
            
            flow.put("Port", commonPorts[random.nextInt(commonPorts.length)]);
            flow.put("Proto", protocols[random.nextInt(protocols.length)]);
            flow.put("SentBytes", random.nextInt(10000) + 100);
            flow.put("ReceivedBytes", random.nextInt(20000) + 200);
            
            // Tenant and subscription info
            flow.put("IllumioTenantId", testTenantId);
            flow.put("SrcTenantId", testTenantId);
            flow.put("SrcSubId", "auto-subscription-" + (i % 5));
            flow.put("SrcRegion", "us-east-1");
            flow.put("SrcResId", "auto-resource-" + ingestionCounter + "-" + i);
            flow.put("SrcVnetId", "auto-vnet-" + (i % 3));
            flow.put("SrcUserName", "/org/test-org/user/autouser" + (i % 10));
            flow.put("DestTenantId", "external-tenant");
            flow.put("DestSubId", "external-sub");
            flow.put("DestRegion", "us-west-2");
            flow.put("DestResId", "external-resource-" + i);
            flow.put("DestVnetId", "external-vnet");
            flow.put("DestUserName", "/org/external/user/external" + i);
            
            // Metadata for tracking
            flow.put("Timestamp", timestampStr);
            flow.put("IngestionCounter", ingestionCounter);
            flow.put("RecordIndex", i);
            flow.put("AutoGenerated", true);
            
            String jsonData = objectMapper.writeValueAsString(flow);
            jsonDataList.add(jsonData);
        }

        return jsonDataList;
    }
}
