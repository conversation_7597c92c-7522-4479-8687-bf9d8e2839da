package com.illumio.data.integration;

import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.apache.kafka.clients.producer.KafkaProducer;
import org.apache.kafka.clients.producer.ProducerConfig;
import org.apache.kafka.clients.producer.ProducerRecord;
import org.apache.kafka.clients.producer.RecordMetadata;
import org.apache.kafka.common.serialization.StringSerializer;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.*;
import java.util.concurrent.Future;

/**
 * Standalone utility to feed JSON data to the IP classification service.
 * This can be run independently to send test data to Kafka.
 * 
 * Usage:
 * 1. Ensure Kafka is running on localhost:19092
 * 2. Run this class with appropriate arguments
 * 3. Monitor the IP classification service processing
 */
@Slf4j
public class JsonDataFeeder {

    private static final String INPUT_TOPIC = "decorated-flow-v1";
    private static final String TEST_TENANT_ID = "test-tenant-12345";
    private static final String KAFKA_BOOTSTRAP_SERVERS = "localhost:19092";

    private final KafkaProducer<String, String> kafkaProducer;
    private final ObjectMapper objectMapper;

    public JsonDataFeeder() {
        this.objectMapper = new ObjectMapper();
        this.kafkaProducer = createKafkaProducer();
    }

    public static void main(String[] args) {
        JsonDataFeeder feeder = new JsonDataFeeder();
        
        try {
            if (args.length > 0) {
                switch (args[0].toLowerCase()) {
                    case "file":
                        if (args.length < 2) {
                            System.err.println("Usage: java JsonDataFeeder file <json-file-path>");
                            System.exit(1);
                        }
                        feeder.feedFromFile(args[1]);
                        break;
                    case "sample":
                        int count = args.length > 1 ? Integer.parseInt(args[1]) : 10;
                        feeder.feedSampleData(count);
                        break;
                    case "hourly":
                        int hours = args.length > 1 ? Integer.parseInt(args[1]) : 3;
                        feeder.feedHourlyData(hours);
                        break;
                    default:
                        printUsage();
                        System.exit(1);
                }
            } else {
                // Default: feed sample data
                feeder.feedSampleData(5);
            }
        } catch (Exception e) {
            log.error("Error feeding data", e);
            System.exit(1);
        } finally {
            feeder.close();
        }
    }

    private static void printUsage() {
        System.out.println("Usage:");
        System.out.println("  java JsonDataFeeder                    - Feed 5 sample records");
        System.out.println("  java JsonDataFeeder sample <count>     - Feed <count> sample records");
        System.out.println("  java JsonDataFeeder file <path>        - Feed data from JSON file");
        System.out.println("  java JsonDataFeeder hourly <hours>     - Feed hourly data for <hours> hours");
    }

    /**
     * Feed sample decorated flow JSON data
     */
    public void feedSampleData(int count) throws Exception {
        log.info("Feeding {} sample JSON records to IP classification service", count);

        List<Future<RecordMetadata>> futures = new ArrayList<>();

        for (int i = 0; i < count; i++) {
            String jsonData = createSampleDecoratedFlowJson(i);
            String key = TEST_TENANT_ID + "-sample-" + i;

            ProducerRecord<String, String> record = new ProducerRecord<>(
                INPUT_TOPIC, 
                key, 
                jsonData
            );

            log.info("Sending record {}: {}", i + 1, jsonData);
            Future<RecordMetadata> future = kafkaProducer.send(record);
            futures.add(future);
        }

        // Wait for all records to be sent
        for (Future<RecordMetadata> future : futures) {
            RecordMetadata metadata = future.get();
            log.info("Record sent to topic: {}, partition: {}, offset: {}", 
                    metadata.topic(), metadata.partition(), metadata.offset());
        }

        log.info("Successfully sent {} records", count);
    }

    /**
     * Feed data from a JSON file
     */
    public void feedFromFile(String filePath) throws Exception {
        log.info("Feeding data from file: {}", filePath);

        String content = Files.readString(Paths.get(filePath));
        
        // Try to parse as JSON array first
        try {
            List<Map> jsonArray = objectMapper.readValue(content, List.class);
            
            for (int i = 0; i < jsonArray.size(); i++) {
                String jsonData = objectMapper.writeValueAsString(jsonArray.get(i));
                String key = TEST_TENANT_ID + "-file-" + i;
                
                ProducerRecord<String, String> record = new ProducerRecord<>(
                    INPUT_TOPIC, 
                    key, 
                    jsonData
                );
                
                kafkaProducer.send(record).get();
                log.info("Sent record {} from file", i + 1);
            }
            
            log.info("Successfully sent {} records from file", jsonArray.size());
            
        } catch (Exception e) {
            // Try as single JSON object
            Map<String, Object> jsonObject = objectMapper.readValue(content, Map.class);
            String jsonData = objectMapper.writeValueAsString(jsonObject);
            String key = TEST_TENANT_ID + "-file-single";
            
            ProducerRecord<String, String> record = new ProducerRecord<>(
                INPUT_TOPIC, 
                key, 
                jsonData
            );
            
            kafkaProducer.send(record).get();
            log.info("Successfully sent single record from file");
        }
    }

    /**
     * Feed hourly batches of data
     */
    public void feedHourlyData(int hours) throws Exception {
        log.info("Feeding hourly data for {} hours", hours);

        for (int hour = 0; hour < hours; hour++) {
            log.info("Feeding data for hour: {}", hour + 1);
            
            // Generate 15-25 records per hour
            Random random = new Random();
            int recordCount = 15 + random.nextInt(10);
            
            for (int i = 0; i < recordCount; i++) {
                String jsonData = createHourlyDecoratedFlowJson(hour, i);
                String key = TEST_TENANT_ID + "-hour-" + hour + "-" + i;
                
                ProducerRecord<String, String> record = new ProducerRecord<>(
                    INPUT_TOPIC, 
                    key, 
                    jsonData
                );
                
                kafkaProducer.send(record).get();
            }
            
            log.info("Sent {} records for hour {}", recordCount, hour + 1);
            
            // Small delay between hours
            Thread.sleep(2000);
        }
    }

    private String createSampleDecoratedFlowJson(int index) throws Exception {
        Map<String, Object> flow = new HashMap<>();
        
        // Realistic IP addresses
        String[] srcIps = {"*************", "*********", "***********"};
        String[] destIps = {"*******", "*******", "**************"};
        
        flow.put("SrcIP", srcIps[index % srcIps.length]);
        flow.put("DestIP", destIps[index % destIps.length]);
        flow.put("SrcId", "src-vm-" + String.format("%03d", index));
        flow.put("CSSrcId", "css-src-" + String.format("%03d", index));
        flow.put("DestId", "dest-" + String.format("%03d", index));
        flow.put("CSDestId", "css-dest-" + String.format("%03d", index));
        
        // Common ports and protocols
        int[] ports = {80, 443, 53, 22, 3389, 8080, 8443};
        String[] protocols = {"tcp", "udp"};
        flow.put("Port", ports[index % ports.length]);
        flow.put("Proto", protocols[index % protocols.length]);
        
        flow.put("SentBytes", 1024 * (index + 1));
        flow.put("ReceivedBytes", 2048 * (index + 1));
        
        // Tenant information
        flow.put("IllumioTenantId", TEST_TENANT_ID);
        flow.put("SrcTenantId", TEST_TENANT_ID);
        flow.put("SrcSubId", "subscription-" + (index % 3));
        flow.put("SrcRegion", "us-east-1");
        flow.put("SrcResId", "resource-vm-" + index);
        flow.put("SrcVnetId", "vnet-" + (index % 2));
        flow.put("SrcUserName", "/org/test-org/user/user" + index);
        flow.put("DestTenantId", "external-tenant");
        flow.put("DestSubId", "external-sub");
        flow.put("DestRegion", "us-west-2");
        flow.put("DestResId", "external-resource-" + index);
        flow.put("DestVnetId", "external-vnet");
        flow.put("DestUserName", "/org/external/user/external" + index);
        
        return objectMapper.writeValueAsString(flow);
    }

    private String createHourlyDecoratedFlowJson(int hour, int recordIndex) throws Exception {
        Map<String, Object> flow = new HashMap<>();
        Random random = new Random();
        
        // Generate varied data for each hour
        flow.put("SrcIP", "192.168." + (hour + 1) + "." + (100 + recordIndex));
        flow.put("DestIP", "8.8." + (8 + recordIndex % 3) + "." + (8 + recordIndex % 10));
        flow.put("SrcId", "src-h" + hour + "-" + recordIndex);
        flow.put("CSSrcId", "css-src-h" + hour + "-" + recordIndex);
        flow.put("DestId", "dest-h" + hour + "-" + recordIndex);
        flow.put("CSDestId", "css-dest-h" + hour + "-" + recordIndex);
        
        int[] ports = {80, 443, 53, 22, 3389, 8080, 8443, 9090};
        String[] protocols = {"tcp", "udp"};
        flow.put("Port", ports[random.nextInt(ports.length)]);
        flow.put("Proto", protocols[random.nextInt(protocols.length)]);
        
        flow.put("SentBytes", random.nextInt(10000) + 100);
        flow.put("ReceivedBytes", random.nextInt(20000) + 200);
        
        // Consistent tenant
        flow.put("IllumioTenantId", TEST_TENANT_ID);
        flow.put("SrcTenantId", TEST_TENANT_ID);
        flow.put("SrcSubId", "subscription-" + (recordIndex % 5));
        flow.put("SrcRegion", "us-east-1");
        flow.put("SrcResId", "resource-h" + hour + "-" + recordIndex);
        flow.put("SrcVnetId", "vnet-" + (recordIndex % 3));
        flow.put("SrcUserName", "/org/test-org/user/user" + recordIndex);
        flow.put("DestTenantId", "external-tenant");
        flow.put("DestSubId", "external-sub");
        flow.put("DestRegion", "us-west-2");
        flow.put("DestResId", "external-resource-" + recordIndex);
        flow.put("DestVnetId", "external-vnet");
        flow.put("DestUserName", "/org/external/user/external" + recordIndex);
        
        // Add hour metadata
        flow.put("Hour", hour);
        flow.put("RecordIndex", recordIndex);
        flow.put("Timestamp", System.currentTimeMillis());
        
        return objectMapper.writeValueAsString(flow);
    }

    private KafkaProducer<String, String> createKafkaProducer() {
        Properties props = new Properties();
        props.put(ProducerConfig.BOOTSTRAP_SERVERS_CONFIG, KAFKA_BOOTSTRAP_SERVERS);
        props.put(ProducerConfig.KEY_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.VALUE_SERIALIZER_CLASS_CONFIG, StringSerializer.class.getName());
        props.put(ProducerConfig.ACKS_CONFIG, "all");
        props.put(ProducerConfig.RETRIES_CONFIG, 3);
        props.put(ProducerConfig.BATCH_SIZE_CONFIG, 16384);
        props.put(ProducerConfig.LINGER_MS_CONFIG, 10);

        return new KafkaProducer<>(props);
    }

    public void close() {
        if (kafkaProducer != null) {
            kafkaProducer.close();
        }
    }
}
