package com.illumio.data.integration;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.CommandLineRunner;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * Spring Boot application that runs continuous hourly data ingestion for IP classification service.
 * 
 * This application will:
 * 1. Start up and initialize the hourly data ingestion service
 * 2. Run continuously, feeding JSON data every hour (or at configured intervals)
 * 3. Send data to the IP classification service via Kafka
 * 4. Log all activities for monitoring
 * 
 * Usage:
 * 1. Ensure Kafka is running on localhost:19092
 * 2. Ensure IP classification service is running
 * 3. Run this application: java -jar continuous-hourly-ingestion.jar
 * 4. Monitor logs to see hourly data ingestion
 * 5. Press Ctrl+C to stop
 * 
 * Configuration:
 * - Set spring.profiles.active=hourly-ingestion
 * - Modify application-hourly-ingestion.yml for custom settings
 */
@Slf4j
@SpringBootApplication
@EnableScheduling
@ComponentScan(basePackages = {"com.illumio.data"})
@RequiredArgsConstructor
public class ContinuousHourlyIngestionApp implements CommandLineRunner {

    private final AutomaticHourlyDataIngestionService ingestionService;

    public static void main(String[] args) {
        // Set default profile if not specified
        if (System.getProperty("spring.profiles.active") == null) {
            System.setProperty("spring.profiles.active", "hourly-ingestion");
        }
        
        log.info("🚀 Starting Continuous Hourly Data Ingestion Application");
        SpringApplication.run(ContinuousHourlyIngestionApp.class, args);
    }

    @Override
    public void run(String... args) throws Exception {
        printStartupBanner();
        
        // Trigger immediate ingestion for testing
        log.info("🔄 Triggering initial data ingestion...");
        try {
            ingestionService.triggerImmediateIngestion();
            log.info("✅ Initial data ingestion completed successfully");
        } catch (Exception e) {
            log.error("❌ Initial data ingestion failed", e);
        }
        
        log.info("⏰ Scheduled hourly ingestion is now active");
        log.info("📝 Monitor logs to see hourly data ingestion activities");
        log.info("🛑 Press Ctrl+C to stop the application");
        
        // Keep the application running
        Thread.currentThread().join();
    }

    private void printStartupBanner() {
        log.info("=" .repeat(80));
        log.info("🏭 CONTINUOUS HOURLY DATA INGESTION FOR IP CLASSIFICATION SERVICE");
        log.info("=" .repeat(80));
        log.info("📋 Application Details:");
        log.info("   • Service: IP Classification MMDB");
        log.info("   • Data Format: Decorated Flow JSON");
        log.info("   • Input Topic: decorated-flow-v1");
        log.info("   • Output Topic: ip-classification-v1");
        log.info("   • Schedule: Every hour (configurable)");
        log.info("   • Test Tenant: test-tenant-12345");
        log.info("");
        log.info("🔧 Prerequisites:");
        log.info("   • Kafka running on localhost:19092");
        log.info("   • IP Classification service running");
        log.info("   • BlueMmdb service running (optional for testing)");
        log.info("");
        log.info("📊 Monitoring:");
        log.info("   • Input:  kafka-console-consumer.sh --bootstrap-server localhost:19092 --topic decorated-flow-v1");
        log.info("   • Output: kafka-console-consumer.sh --bootstrap-server localhost:19092 --topic ip-classification-v1");
        log.info("=" .repeat(80));
    }
}
