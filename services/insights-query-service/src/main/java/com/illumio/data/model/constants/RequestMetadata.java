package com.illumio.data.model.constants;

import lombok.Getter;

import java.util.Arrays;
import java.util.Collections;
import java.util.EnumSet;
import java.util.List;
import java.util.Set;

import static com.illumio.data.model.constants.Fields.AGGREGATED_BYTES;
import static com.illumio.data.model.constants.Fields.AGGREGATED_FLOWS;
import static com.illumio.data.model.constants.Fields.DESTINATION_REGION;
import static com.illumio.data.model.constants.Fields.DESTINATION_ROLE;
import static com.illumio.data.model.constants.Fields.DESTINATION_SUBSCRIPTION_ID;
import static com.illumio.data.model.constants.Fields.DESTINATION_ZONE;
import static com.illumio.data.model.constants.Fields.PORT;
import static com.illumio.data.model.constants.Fields.PROTOCOL;
import static com.illumio.data.model.constants.Fields.SOURCE_COUNTRY;
import static com.illumio.data.model.constants.Fields.SOURCE_EXTERNAL_LABEL;
import static com.illumio.data.model.constants.Fields.SOURCE_EXTERNAL_LABEL_CATEGORY;
import static com.illumio.data.model.constants.Fields.SOURCE_IP;
import static com.illumio.data.model.constants.Fields.SOURCE_REGION;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_CATEGORY;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_ID;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_NAME;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_TYPE;
import static com.illumio.data.model.constants.Fields.SOURCE_ZONE;
import static com.illumio.data.model.constants.Fields.SOURCE_ROLE;
import static com.illumio.data.model.constants.Fields.DESTINATION_COUNTRY;
import static com.illumio.data.model.constants.Fields.DESTINATION_EXTERNAL_LABEL;
import static com.illumio.data.model.constants.Fields.DESTINATION_EXTERNAL_LABEL_CATEGORY;
import static com.illumio.data.model.constants.Fields.LLM;
import static com.illumio.data.model.constants.Fields.TRAFFIC_STATUS;
import static com.illumio.data.model.constants.Fields.RESOURCE_NAME;
import static com.illumio.data.model.constants.Fields.IP;

@Getter
public enum RequestMetadata {
    DESTINATION_ROLE_LEVEL_TRAFFIC("destRoleTraffic", "riskyTraffic","4321", getSummarizeFields("destRoleTraffic"), getGroupByFields("destRoleTraffic"), "RiskyTraffic"),
    RISKY_SERVICE_TRAFFIC("riskyServiceTraffic", "riskyTraffic", "1234", getSummarizeFields("riskyServiceTraffic"), getGroupByFields("riskyServiceTraffic"), "RiskyTraffic"),
    ZONE_LEVEL_TRAFFIC("zoneLevelTraffic", "riskyTraffic", "5678", getSummarizeFields("zoneLevelTraffic"), getGroupByFields("zoneLevelTraffic"), "RiskyTraffic"),
    TOP_DESTINATION_ROLES("topDestinationRoles", "riskyTraffic", "9012", getSummarizeFields("topDestinationRoles"), getGroupByFields("topDestinationRoles"), "RiskyTraffic"),
    TOP_WORKLOADS("topWorkloads", "riskyTraffic", "3456", getSummarizeFields("topWorkloads"), getGroupByFields("topWorkloads"), "RiskyTraffic"),
    RESOURCE_INSIGHTS("resourceInsights", "0", "0", getSummarizeFields("topWorkloads"), getGroupByFields("topWorkloads"), "DecoratedFlows"),
    TOP_MALICIOUS_IPS("topMaliciousIps", "maliciousIp", "1122", getSummarizeFields("topMaliciousIps"), getGroupByFields("topMaliciousIps"), "MaliciousIpTraffic"),
    THREAT_MAP("threatMap", "maliciousIp", "2233", getSummarizeFields("threatMap"), getGroupByFields("threatMap"), "MaliciousIpTraffic"),
    TOP_CATEGORY_WITH_MALICIOUS_IP("topCategoryWithMaliciousIp", "maliciousIp", "3344", getSummarizeFields("topCategoryWithMaliciousIp"), getGroupByFields("topCategoryWithMaliciousIp"), "MaliciousIpTraffic"),
    TOP_ROLES("topRoles", "maliciousIp", "4455", getSummarizeFields("topRoles"), getGroupByFields("topRoles"), "MaliciousIpTraffic"),
    TOP_SERVICES("topServices", "maliciousIp", "5566", getSummarizeFields("topServices"), getGroupByFields("topServices"), "MaliciousIpTraffic"),
    EXTERNAL_DESTINATION_CATEGORY("externalDestinationCategory","externalDataTransfer","3399",getSummarizeFields("externalDestinationCategory"),getGroupByFields("externalDestinationCategory"), "ExfiltrationTraffic"),
    EXTERNAL_SERVICE_TRANSFER("externalServiceTransfer","externalDataTransfer","4499",getSummarizeFields("externalServiceTransfer"),getGroupByFields("externalServiceTransfer"), "ExfiltrationTraffic"),
    TOP_SOURCE_ROLE_TRANSFER("topSourceRoleTransfer","externalDataTransfer","5599",getSummarizeFields("topSourceRoleTransfer"),getGroupByFields("topSourceRoleTransfer"), "ExfiltrationTraffic"),
    EXTERNAL_GEO_TRANSFER("externalGeoTransfer","externalDataTransfer","6699",getSummarizeFields("externalGeoTransfer"),getGroupByFields("externalGeoTransfer"), "ExfiltrationTraffic"),
    TOP_SOURCE_TRANSFER("topSourceTransfer","externalDataTransfer","7799",getSummarizeFields("topSourceTransfer"),getGroupByFields("topSourceTransfer"), "ExfiltrationTraffic"),
    LLM_IN_USE("llmInUse", "shadowLlm", "1881", getSummarizeFields("llmInUse"), getGroupByFields("llmInUse"), "ExfiltrationTraffic"),
    TOP_CATEGORY_WITH_LLM("topCategoryWithLlm", "shadowLlm", "1991", getSummarizeFields("topCategoryWithLlm"), getGroupByFields("topCategoryWithLlm"), "ExfiltrationTraffic"),
    TOP_SOURCES_WITH_LLM("topSourcesWithLlm", "shadowLlm", "1771", getSummarizeFields("topSourcesWithLlm"), getGroupByFields("topSourcesWithLlm"), "ExfiltrationTraffic"),
    THIRD_PARTY_DEPENDENCY_INBOUND("thirdPartyInbound","InsightsHub","9991",getSummarizeFields("thirdPartyInbound"),getGroupByFields("thirdPartyInbound"), "InfiltrationTraffic"),
    THIRD_PARTY_DEPENDENCY_OUTBOUND("thirdPartyOutbound","InsightsHub","9992",getSummarizeFields("thirdPartyOutbound"),getGroupByFields("thirdPartyOutbound"), "ExfiltrationTraffic"),
    RISKY_SERVICE_TRAFFIC_INSIGHTS_HUB("riskyServiceTraffic", "insightsHub", "9876", getSummarizeFields("riskyServiceTraffic"), getGroupByFields("riskyServiceTraffic"), "RiskyTraffic"),
    TOP_DESTINATION_ROLES_INSIGHTS_HUB("topDestinationRoles", "insightsHub", "8765", getSummarizeFields("topDestinationRoles"), getGroupByFields("topDestinationRoles"), "RiskyTraffic"),
    TOP_SOURCE_TRANSFER_INSIGHTS_HUB("topSourceTransfer","insightsHub","7654",getSummarizeFields("topSourceTransfer"),getGroupByFields("topSourceTransfer"), "ExfiltrationTraffic"),
    TOP_MALICIOUS_IPS_INSIGHTS_HUB("topMaliciousIps", "insightsHub", "6543", getSummarizeFields("topMaliciousIps"), getGroupByFields("topMaliciousIps"), "MaliciousIpTraffic"),
    TOP_CROSS_REGION_TRAFFIC("topCrossRegionTraffic", "insightsHub", "5432", getSummarizeFields("topCrossRegionTraffic"), getGroupByFields("topCrossRegionTraffic"), "CrossRegionTraffic"),
    TOP_REGION_TO_COUNTRY_TRAFFIC("topRegionToCountryTraffic", "insightsHub", "3210", getSummarizeFields("topRegionToCountryTraffic"), getGroupByFields("topRegionToCountryTraffic"), "CrossRegionTraffic"),
    UNENCRYPTED_SERVICES("doraUnencryptedServices", "dora", "9993", getSummarizeFields("doraUnencryptedServices"), getGroupByFields("doraUnencryptedServices"), "RiskyTraffic"),
    DORA_TOP_ICT("doraTopIct", "dora", "9995", getSummarizeFields("doraTopIct"), getGroupByFields("doraTopIct"), "MaliciousIpTraffic"),
    DORA_CRITICAL_ICT("doraCriticalIct", "dora", "9994", getSummarizeFields("doraCriticalIct"), getGroupByFields("doraCriticalIct"), "RiskyTraffic"),
    RISKY_TRAFFIC_BY_ROLES_RESOURCE_INSIGHTS("riskyTrafficByRolesResourceInsights", "insightsHub", "1661", getSummarizeFields("riskyTrafficByRolesResourceInsights"), getGroupByFields("riskyTrafficByRolesResourceInsights"), "RiskyTraffic"),
    MALICIOUS_IP_TRAFFIC_RESOURCE_INSIGHTS("maliciousIpTrafficResourceInsights", "insightsHub", "1551", getSummarizeFields("maliciousIpTrafficResourceInsights"), getGroupByFields("maliciousIpTrafficResourceInsights"), "MaliciousIpTraffic"),
    EXTERNAL_DATA_TRANSFER_RESOURCE_INSIGHTS("externalDataTransferResourceInsights", "insightsHub", "1441", getSummarizeFields("externalDataTransferResourceInsights"), getGroupByFields("externalDataTransferResourceInsights"), "ExfiltrationTraffic"),
    RISKY_SERVICES_TRAFFIC_RESOURCE_INSIGHTS("riskyServicesTrafficResourceInsights", "insightsHub", "1331", getSummarizeFields("riskyServicesTrafficResourceInsights"), getGroupByFields("riskyServicesTrafficResourceInsights"), "RiskyTraffic"),
    WIDGET_BATCH_INSIGHT("widgetBatchInsight", "batch", "0000", Collections.emptyList(), Collections.emptyList(), "");

    private final String requestType;
    private final String pageId;
    private final String widgetId;
    private final String tableName;
    private final List<Fields> summarizeFields;
    private final List<Fields> groupByFields;

    @Getter
    private static final Set<String> validSortOrder = Set.of("asc", "desc");
    
    // Set of widgets that require processing with both inbound and outbound traffic directions
    private static final Set<RequestMetadata> DIRECTION_DEPENDENT_WIDGETS = EnumSet.of(
        TOP_MALICIOUS_IPS,
        THREAT_MAP,
        TOP_CATEGORY_WITH_MALICIOUS_IP,
        TOP_ROLES,
        TOP_SERVICES,
        TOP_MALICIOUS_IPS_INSIGHTS_HUB
    );

    // Set of widgets that require processing with tenant and subscription traffic directions
    private static final Set<RequestMetadata> CATEGORY_DEPENDENT_WIDGETS = EnumSet.of(
            TOP_CATEGORY_WITH_MALICIOUS_IP
    );

    RequestMetadata(String requestType, String pageId, String widgetId, List<Fields> summarizeFields, List<Fields> groupByFields, String tableName) {
        this.requestType = requestType;
        this.pageId = pageId;
        this.widgetId = widgetId;
        this.summarizeFields = summarizeFields;
        this.groupByFields = groupByFields;
        this.tableName = tableName;
    }

    public static List<Fields> getSummarizeFields(String requestType) {
        return switch (requestType) {
            case "riskyServiceTraffic", "zoneLevelTraffic", "destRoleTraffic", "externalDestinationCategory", "externalServiceTransfer", "topSourceRoleTransfer", "externalGeoTransfer", "thirdPartyInbound", "thirdPartyOutbound", "riskyTrafficByRolesResourceInsights", "maliciousIpTrafficResourceInsights", "externalDataTransferResourceInsights", "riskyServicesTrafficResourceInsights", "doraUnencryptedServices" -> Arrays.asList(AGGREGATED_FLOWS, AGGREGATED_BYTES);
            case "topDestinationRoles", "topWorkloads", "topSourceTransfer", "topSourcesWithLlm", "doraCriticalIct" -> Collections.emptyList();
            case "topMaliciousIps", "threatMap", "topCategoryWithMaliciousIp", "topRoles", "topServices", "llmInUse", "topCategoryWithLlm", "topCrossRegionTraffic", "topRegionToCountryTraffic", "doraTopIct" -> Collections.emptyList();

            default -> throw new IllegalArgumentException("Invalid request type: " + requestType);
        };
    }

    public static List<Fields> getGroupByFields(String requestType) {
        return switch (requestType) {
            case "riskyServiceTraffic", "externalServiceTransfer", "doraUnencryptedServices" -> Arrays.asList(PORT, PROTOCOL);
            case "zoneLevelTraffic" -> Arrays.asList(SOURCE_ZONE, DESTINATION_ZONE);
            case "destRoleTraffic" -> Arrays.asList(SOURCE_RESOURCE_ID, SOURCE_RESOURCE_NAME, SOURCE_RESOURCE_CATEGORY, SOURCE_RESOURCE_TYPE, DESTINATION_ROLE);
            case "topDestinationRoles" -> Arrays.asList(DESTINATION_ROLE);
            case "topWorkloads", "topSourceTransfer" -> Arrays.asList(SOURCE_RESOURCE_ID, SOURCE_RESOURCE_NAME, SOURCE_RESOURCE_CATEGORY, SOURCE_RESOURCE_TYPE);
            case "externalDestinationCategory", "thirdPartyOutbound" -> Arrays.asList(DESTINATION_EXTERNAL_LABEL_CATEGORY, DESTINATION_EXTERNAL_LABEL);
            case "topSourceRoleTransfer" -> Arrays.asList(SOURCE_ROLE);
            case "externalGeoTransfer" -> Arrays.asList(DESTINATION_COUNTRY);
            case "thirdPartyInbound" -> Arrays.asList(SOURCE_EXTERNAL_LABEL_CATEGORY, SOURCE_EXTERNAL_LABEL);

            case "topMaliciousIps" -> Arrays.asList(SOURCE_IP);
            case "threatMap" -> Arrays.asList(SOURCE_COUNTRY);
            case "topCategoryWithMaliciousIp" -> Arrays.asList(DESTINATION_SUBSCRIPTION_ID, SOURCE_IP);
            case "topRoles" -> Arrays.asList(DESTINATION_ROLE);
            case "topServices" -> Arrays.asList(PORT, PROTOCOL);

            case "llmInUse" -> Arrays.asList(LLM);
            case "topCategoryWithLlm" -> Arrays.asList(DESTINATION_EXTERNAL_LABEL);
            case "topSourcesWithLlm" -> Arrays.asList(DESTINATION_SUBSCRIPTION_ID, SOURCE_IP);

            case "topCrossRegionTraffic" -> Arrays.asList(TRAFFIC_STATUS, SOURCE_REGION, DESTINATION_REGION, SOURCE_ZONE, DESTINATION_ZONE);
            case "topRegionToCountryTraffic" -> Arrays.asList(SOURCE_REGION, DESTINATION_COUNTRY, SOURCE_ZONE, DESTINATION_ZONE);

            case "riskyTrafficByRolesResourceInsights" -> Arrays.asList(RESOURCE_NAME);
            case "maliciousIpTrafficResourceInsights" -> Arrays.asList(IP);
            case "externalDataTransferResourceInsights" -> Arrays.asList(IP);
            case "riskyServicesTrafficResourceInsights" -> Arrays.asList(PORT, PROTOCOL);

            case "doraTopIct", "doraCriticalIct" -> Collections.emptyList();

            default -> throw new IllegalArgumentException("Invalid request type: " + requestType);
        };
    }

    public static RequestMetadata getRequestMetadataForWidgetId(String widgetId) {
        return Arrays.stream(RequestMetadata.values())
                .filter(field -> field.widgetId.equals(widgetId))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }

    public static boolean isDirectionDependent(RequestMetadata metadata) {
        return DIRECTION_DEPENDENT_WIDGETS.contains(metadata);
    }

    public static boolean isDirectionDependentWidgetId(String widgetId) {
        try {
            RequestMetadata metadata = getRequestMetadataForWidgetId(widgetId);
            return isDirectionDependent(metadata);
        } catch (IllegalArgumentException e) {
            return false;
        }
    }

    public static boolean isCategoryDependent(RequestMetadata metadata) {
        return CATEGORY_DEPENDENT_WIDGETS.contains(metadata);
    }

    public static boolean isCategoryDependentWidgetId(String widgetId) {
        try {
            RequestMetadata metadata = getRequestMetadataForWidgetId(widgetId);
            return isCategoryDependent(metadata);
        } catch (IllegalArgumentException e) {
            return false;
        }
    }
}
