package com.illumio.data.response;
import com.illumio.data.builder.ResponseBuilder;
import com.illumio.data.model.Pagination;
import com.illumio.data.model.RequestContext;
import com.illumio.data.model.RequestPayload;
import com.illumio.data.model.SortByFields;
import com.illumio.data.model.TimeFrame;
import com.microsoft.azure.kusto.data.KustoResultSetTable;
import lombok.Data;
import org.springframework.stereotype.Component;
import org.springframework.util.MultiValueMap;

import java.util.Arrays;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Map;
import java.util.HashMap;
import java.util.stream.Collectors;

import static com.illumio.data.model.constants.Fields.COUNT;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_NAME;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_ID;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_CATEGORY;
import static com.illumio.data.model.constants.Fields.SOURCE_RESOURCE_TYPE;
import static com.illumio.data.model.constants.Fields.AGGREGATE_FIELD;
import static com.illumio.data.model.constants.Fields.TIME_SERIES;
import static com.illumio.data.model.constants.RequestMetadata.TOP_SOURCES_WITH_LLM;

@Data
@Component
public class TopSourcesWithLlmResponse implements ResponseBuilder<TopSourcesWithLlmResponse> {
    String widgetId;
    String title;
    TimeFrame currentTimeFrame;
    TimeFrame comparisonTimeFrame;
    List<String> columns;
    List<String> columnTypes;
    List<String> columnDisplayNames;
    List<List<Object>> data;
    List<SortByFields> sortByFields;
    Pagination pagination;

    @Override
    public TopSourcesWithLlmResponse buildResponse(KustoResultSetTable resultSetTable, RequestContext requestContext) {
        RequestPayload payload = requestContext.getRequestPayload().get();
        TopSourcesWithLlmResponse response = new TopSourcesWithLlmResponse();

        //Set Widget Specific Metadata
        response.setWidgetId(TOP_SOURCES_WITH_LLM.getWidgetId());
        response.setTitle("Top Sources with LLM");
        response.setColumns(Arrays.asList(SOURCE_RESOURCE_ID.getFieldKey(), SOURCE_RESOURCE_NAME.getFieldKey(), SOURCE_RESOURCE_CATEGORY.getFieldKey(), SOURCE_RESOURCE_TYPE.getFieldKey(), AGGREGATE_FIELD.getFieldKey(), TIME_SERIES.getFieldKey()));
        response.setColumnTypes(Arrays.asList(SOURCE_RESOURCE_ID.getFieldType(), SOURCE_RESOURCE_NAME.getFieldType(), SOURCE_RESOURCE_CATEGORY.getFieldType(), SOURCE_RESOURCE_TYPE.getFieldType(), AGGREGATE_FIELD.getFieldType(), TIME_SERIES.getFieldType()));
        response.setColumnDisplayNames(Arrays.asList(SOURCE_RESOURCE_ID.getFieldDisplayName(), SOURCE_RESOURCE_NAME.getFieldDisplayName(), SOURCE_RESOURCE_CATEGORY.getFieldDisplayName(), SOURCE_RESOURCE_TYPE.getFieldDisplayName(), AGGREGATE_FIELD.getFieldDisplayName(), TIME_SERIES.getFieldDisplayName()));

        //Set Request Data
        response.setCurrentTimeFrame(payload.getCurrentTimeFrame());
        response.setComparisonTimeFrame(payload.getCurrentTimeFrame());
        response.setPagination(payload.getPagination());
        response.setSortByFields(payload.getSortByFields());

        //Set Response Data
        Map<String, Map<String, List<List<Object>>>> groupedData = new HashMap<>();
        while(resultSetTable.next()) {
            String resourceId = (String) resultSetTable.getObject(SOURCE_RESOURCE_ID.getTableColumnName());
            String resourceName = (String) resultSetTable.getObject(SOURCE_RESOURCE_NAME.getTableColumnName());
            String resourceCategory = (String) resultSetTable.getObject(SOURCE_RESOURCE_CATEGORY.getTableColumnName());
            String resourceType = (String) resultSetTable.getObject(SOURCE_RESOURCE_TYPE.getTableColumnName());
            String aggregateField = (String) resultSetTable.getObject(AGGREGATE_FIELD.getTableColumnName());
            String timestamps = (String) resultSetTable.getObject(TIME_SERIES.getTableColumnName());
            Long counts = getLongValue(resultSetTable, COUNT.getTableColumnName());

            // Composite key for grouping
            String compositeKey = resourceId + "|" + resourceName + "|" + resourceCategory + "|" + resourceType;

            // Group by composite key and then by aggregateField.
            groupedData
                    .computeIfAbsent(compositeKey, k -> new HashMap<>())
                    .computeIfAbsent(aggregateField, k -> new ArrayList<>())
                    .add(Arrays.asList(timestamps, counts));
        }

        // Build final formatted data: for each composite key and then each aggregate group
        List<List<Object>> formattedData = groupedData.entrySet().stream()
                .flatMap(outer -> {
                    // Split the composite key back into its resource-level parts.
                    String[] parts = outer.getKey().split("\\|", -1);
                    String srcResId = parts[0];
                    String srcResName = parts[1];
                    String srcResCategory = parts[2];
                    String srcResType = parts[3];

                    return outer.getValue().entrySet().stream()
                            .map(inner -> Arrays.asList(
                                    srcResId,
                                    srcResName,
                                    srcResCategory,
                                    srcResType,
                                    inner.getKey(),
                                    inner.getValue()
                            ));
                })
                .collect(Collectors.toList());
        response.setData(formattedData);

        return response;
    }

    @Override
    public TopSourcesWithLlmResponse buildAggregatedResponse(List<KustoResultSetTable> resultList, Optional<MultiValueMap<String, String>> params) {
        return null;
    }

    private Long getLongValue(KustoResultSetTable resultSetTable, String columnName) {
        Object value = resultSetTable.getObject(columnName);
        return (value != null) ? ((Number) value).longValue() : null;
    }

}