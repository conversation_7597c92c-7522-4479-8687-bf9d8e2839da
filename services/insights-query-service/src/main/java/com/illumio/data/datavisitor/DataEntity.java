package com.illumio.data.datavisitor;

import com.illumio.data.model.RequestContext;
import com.illumio.data.model.constants.RequestMetadata;
import com.illumio.data.builder.ResponseBuilder;
import reactor.core.publisher.Mono;

public interface DataEntity<T> {
    <R> Mono<R> accept(DataVisitor<T> visitor, RequestMetadata requestMetadata, RequestContext requestContext, ResponseBuilder<R> responseBuilder);
}
