package com.illumio.data.service.batch_widget;

import com.illumio.data.model.constants.RequestMetadata;
import com.illumio.data.model.widget.response.CategoryResponse;
import com.illumio.data.model.widget.response.CombinedResponse;
import com.illumio.data.model.widget.response.DirectionalResponse;
import com.illumio.data.model.widget.result.CategoryResults;
import com.illumio.data.model.widget.result.CombinedResults;
import com.illumio.data.model.widget.result.DirectionalResults;
import com.illumio.data.response.WidgetBatchResponse.WidgetResult;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Service responsible for combining widget results based on widget types.
 */
@Slf4j
@Service
public class WidgetResultCombinerService {

    /**
     * Combines direction-specific and category-specific results for the same widget ID into single results.
     */
    public List<WidgetResult> combineResults(List<WidgetResult> results) {
        // Group results by widget ID
        Map<String, List<WidgetResult>> resultsByWidget = new HashMap<>();
        
        for (WidgetResult result : results) {
            resultsByWidget
                .computeIfAbsent(result.getWidgetId(), k -> new ArrayList<>())
                .add(result);
        }
        
        // Combine direction-specific results
        List<WidgetResult> combinedResults = new ArrayList<>();
        
        for (Map.Entry<String, List<WidgetResult>> entry : resultsByWidget.entrySet()) {
            String widgetId = entry.getKey();
            List<WidgetResult> widgetResults = entry.getValue();
            
            // Check if this is a direction-dependent widget
            boolean isDirectionDependent = false;
            boolean isCategoryDependent = false;
            try {
                isDirectionDependent = RequestMetadata.isDirectionDependentWidgetId(widgetId);
                isCategoryDependent = RequestMetadata.isCategoryDependentWidgetId(widgetId);
            } catch (Exception e) {
                log.warn("Error checking widget {} type", widgetId, e);
            }
            
            // Process widgets based on their dependencies
            processWidget(widgetId, widgetResults, combinedResults, isCategoryDependent, isDirectionDependent);
        }
        
        return combinedResults;
    }
    
    /**
     * Generic method to process widgets based on their dependencies.
     */
    private void processWidget(String widgetId, List<WidgetResult> widgetResults, 
            List<WidgetResult> combinedResults, boolean isCategoryDependent, boolean isDirectionDependent) {
        
        if (isCategoryDependent && isDirectionDependent) {
            // Handle widgets that are both category and direction dependent
            processCombinedDependentWidget(widgetId, widgetResults, combinedResults);
        } else if (isCategoryDependent) {
            // Handle category-dependent widgets
            processCategoryDependentWidget(widgetId, widgetResults, combinedResults);
        } else if (isDirectionDependent) {
            // Handle direction-dependent widgets
            if (widgetResults.size() == 1) {
                processDirectionalWidgetWithSingleResult(widgetResults, combinedResults);
            } else {
                processDirectionalWidgetWithMultipleResults(widgetId, widgetResults, combinedResults);
            }
        } else {
            // For non-dependent widgets
            processNonDirectionalWidget(widgetResults, combinedResults);
        }
    }
    
    /**
     * Process a widget that is both category and direction dependent.
     */
    private void processCombinedDependentWidget(String widgetId, List<WidgetResult> widgetResults, 
            List<WidgetResult> combinedResults) {
        CombinedResults combinedTypeResults = new CombinedResults();
        boolean hasError = false;
        StringBuilder errorMessage = new StringBuilder();
        
        for (WidgetResult result : widgetResults) {
            if ("error".equals(result.getStatus())) {
                hasError = true;
                if (result.getMessage() != null) {
                    errorMessage.append(result.getMessage()).append("; ");
                }
            } else if (result.getData() instanceof CombinedResponse) {
                // Handle combined category and direction response
                CombinedResponse combinedResponse = (CombinedResponse) result.getData();
                combinedTypeResults.addResult(
                    combinedResponse.getCategory(), 
                    combinedResponse.getDirection(), 
                    combinedResponse.getData()
                );
            } else if (result.getData() instanceof DirectionalResponse) {
                DirectionalResponse dirResponse = (DirectionalResponse) result.getData();
                // Default to "default" category if no category information is available
                combinedTypeResults.addResult("default", dirResponse.getDirection(), dirResponse.getData());
            } else if (result.getData() instanceof CategoryResponse) {
                CategoryResponse catResponse = (CategoryResponse) result.getData();
                // For category responses that don't have direction, we can't add them to the combined results
                // This would likely be a misconfiguration
                log.warn("Received category response without direction for widget {}: {}", 
                        widgetId, catResponse.getCategory());
            }
        }
        
        if (hasError && combinedTypeResults.isEmpty()) {
            // All requests failed
            combinedResults.add(WidgetResult.error(widgetId, errorMessage.toString()));
        } else {
            // At least one request succeeded
            WidgetResult.WidgetResultBuilder resultBuilder = WidgetResult.builder()
                    .widgetId(widgetId)
                    .status("success")
                    .data(combinedTypeResults.getCategoryDirectionalResults());
            
            if (hasError) {
                resultBuilder.message(errorMessage.toString());
            }
            
            combinedResults.add(resultBuilder.build());
        }
    }
    
    /**
     * Process a category-dependent widget's results.
     */
    private void processCategoryDependentWidget(String widgetId, List<WidgetResult> widgetResults, 
            List<WidgetResult> combinedResults) {
        CategoryResults categoryResults = new CategoryResults();
        boolean hasError = false;
        StringBuilder errorMessage = new StringBuilder();
        
        for (WidgetResult result : widgetResults) {
            if ("error".equals(result.getStatus())) {
                hasError = true;
                if (result.getMessage() != null) {
                    errorMessage.append(result.getMessage()).append("; ");
                }
            } else if (result.getData() instanceof CategoryResponse) {
                CategoryResponse catResponse = (CategoryResponse) result.getData();
                categoryResults.addCategory(catResponse.getCategory(), catResponse.getData());
            }
        }
        
        if (hasError && categoryResults.isEmpty()) {
            // All category-specific requests failed
            combinedResults.add(WidgetResult.error(widgetId, errorMessage.toString()));
        } else {
            // At least one category-specific request succeeded
            WidgetResult.WidgetResultBuilder resultBuilder = WidgetResult.builder()
                    .widgetId(widgetId)
                    .status("success")
                    .data(categoryResults.getCategories());
            
            if (hasError) {
                resultBuilder.message(errorMessage.toString());
            }
            
            combinedResults.add(resultBuilder.build());
        }
    }
    
    /**
     * Process a non-directional widget's results.
     */
    private void processNonDirectionalWidget(List<WidgetResult> widgetResults, List<WidgetResult> combinedResults) {
        WidgetResult result = widgetResults.get(0);
        
        // Extract the actual data if it's a DirectionalResponse or CategoryResponse
        if (result.getData() instanceof DirectionalResponse) {
            DirectionalResponse dirResponse = (DirectionalResponse) result.getData();
            result = WidgetResult.builder()
                    .widgetId(result.getWidgetId())
                    .status(result.getStatus())
                    .message(result.getMessage())
                    .data(dirResponse.getData())
                    .build();
        } else if (result.getData() instanceof CategoryResponse) {
            CategoryResponse catResponse = (CategoryResponse) result.getData();
            result = WidgetResult.builder()
                    .widgetId(result.getWidgetId())
                    .status(result.getStatus())
                    .message(result.getMessage())
                    .data(catResponse.getData())
                    .build();
        }
        
        combinedResults.add(result);
    }
    
    /**
     * Process a directional widget that only has one result (possibly due to an error).
     */
    private void processDirectionalWidgetWithSingleResult(List<WidgetResult> widgetResults, 
            List<WidgetResult> combinedResults) {
        WidgetResult result = widgetResults.get(0);
        
        // If it's a directional response, create a proper DirectionalResults with the data
        if (result.getData() instanceof DirectionalResponse) {
            DirectionalResponse dirResponse = (DirectionalResponse) result.getData();
            DirectionalResults directionalResults = new DirectionalResults();
            
            if ("inbound".equals(dirResponse.getDirection())) {
                directionalResults.setInbound(dirResponse.getData());
            } else if ("outbound".equals(dirResponse.getDirection())) {
                directionalResults.setOutbound(dirResponse.getData());
            }
            
            // Create a new result with the directional structure
            result = WidgetResult.builder()
                    .widgetId(result.getWidgetId())
                    .status(result.getStatus())
                    .message(result.getMessage())
                    .data(directionalResults)
                    .build();
        }
        
        combinedResults.add(result);
    }
    
    /**
     * Process a directional widget with multiple results.
     */
    private void processDirectionalWidgetWithMultipleResults(String widgetId, List<WidgetResult> widgetResults, 
            List<WidgetResult> combinedResults) {
        DirectionalResults directionalResults = new DirectionalResults();
        boolean hasError = false;
        StringBuilder errorMessage = new StringBuilder();
        
        for (WidgetResult result : widgetResults) {
            if ("error".equals(result.getStatus())) {
                hasError = true;
                if (result.getMessage() != null) {
                    errorMessage.append(result.getMessage()).append("; ");
                }
            } else if (result.getData() instanceof DirectionalResponse) {
                DirectionalResponse dirResponse = (DirectionalResponse) result.getData();
                String direction = dirResponse.getDirection();
                
                if (direction != null) {
                    if ("inbound".equals(direction)) {
                        directionalResults.setInbound(dirResponse.getData());
                    } else if ("outbound".equals(direction)) {
                        directionalResults.setOutbound(dirResponse.getData());
                    }
                }
            }
        }
        
        if (hasError && directionalResults.isEmpty()) {
            // All direction-specific requests failed
            combinedResults.add(WidgetResult.error(widgetId, errorMessage.toString()));
        } else {
            // At least one direction-specific request succeeded
            WidgetResult.WidgetResultBuilder resultBuilder = WidgetResult.builder()
                    .widgetId(widgetId)
                    .status("success")
                    .data(directionalResults);
            
            if (hasError) {
                // Some direction-specific requests failed
                resultBuilder.message(errorMessage.toString());
            }
            
            combinedResults.add(resultBuilder.build());
        }
    }
} 