package com.illumio.data.model.constants;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Getter;

import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

@Getter
@JsonFormat(shape = JsonFormat.Shape.OBJECT) // Ensures JSON is an object
public enum Fields {
    SERVICE(FieldType.STRING.toString(), "service", "Service", "Service"),
    SEVERITY(FieldType.STRING.toString(), "severity", "Severity", "Severity"),
    SOURCE_IP(FieldType.STRING.toString(), "source_ip", "Source IP", "SrcIP"),
    DESTINATION_IP(FieldType.STRING.toString(), "destination_ip", "Destination IP", "DestIP"),
    PORT(FieldType.NUMBER.toString(), "port", "Port", "Port", true),
    PROTOCOL(FieldType.STRING.toString(), "protocol", "Protocol", "Proto", true),

    FLOWS_TREND(FieldType.STRING.toString(), "flows_trend", "Flows", "FlowTrend"),
    FLOWS(FieldType.NUMBER.toString(), "flows", "Flow Count", "FlowCount"),
    PREVIOUS_FLOWS(FieldType.NUMBER.toString(), "prev_flows", "Previous Flows", "FlowCount1"),
    BYTES_TREND(FieldType.STRING.toString(), "bytes_trend", "Bytes", "ByteTrend"),
    BYTES(FieldType.NUMBER.toString(), "bytes", "Bytes", "ByteCount"),

    PREVIOUS_BYTES(FieldType.NUMBER.toString(), "prev_bytes", "Previous Bytes", "ByteCount1"),
    SOURCE_ZONE(FieldType.STRING.toString(), "source_zone", "Source Zone", "SourceZone"),
    DESTINATION_ZONE(FieldType.STRING.toString(), "destination_zone", "Destination Zone", "DestinationZone"),
    SOURCE_ROLE(FieldType.STRING.toString(), "source_role", "Source Role", "SourceLabel"),
    DESTINATION_ROLE(FieldType.STRING.toString(), "destination_role", "Destination Role", "DestinationLabel"),

    SENT_BYTES(FieldType.NUMBER.toString(), "sent_bytes", "Sent Bytes", "SentBytes"),
    RECEIVED_BYTES(FieldType.NUMBER.toString(), "received_bytes", "Received Bytes", "ReceivedBytes"),
    SOURCE_TENANT_ID(FieldType.STRING.toString(), "source_tenant", "Source Tenant Id", "SrcTenantId"),
    DESTINATION_TENANT_ID(FieldType.STRING.toString(), "destination_tenant", "Destination Tenant Id", "DestTenantId"),
    SOURCE_SUBSCRIPTION_ID(FieldType.STRING.toString(), "source_subscription", "Source Subscription Id", "SrcSubId"),

    DESTINATION_SUBSCRIPTION_ID(
            FieldType.STRING.toString(), "destination_subscription", "Destination Subscription Id", "DestSubId"),
    SOURCE_REGION(FieldType.STRING.toString(), "source_region", "Source Region", "SrcRegion"),
    DESTINATION_REGION(FieldType.STRING.toString(), "destination_region", "Destination Region", "DestRegion"),
    SOURCE_RESOURCE_ID(FieldType.STRING.toString(), "source_resource_id", "Source Resource Id", "SrcResId"),
    DESTINATION_RESOURCE_ID(
            FieldType.STRING.toString(), "destination_resource_id", "Destination Resource Id", "DestResId"),

    SOURCE_VNET_ID(FieldType.STRING.toString(), "source_vnet", "Source Vnet Id", "SrcVnetId"),
    DESTINATION_VNET_ID(FieldType.STRING.toString(), "destination_vnet", "Destination Vnet Id", "DestVnetId"),
    SOURCE_CLOUD_TAGS(FieldType.STRING.toString(), "source_cloud_tags", "Source Cloud Tags", "SrcCloudTags"),
    DESTINATION_CLOUD_TAGS(
            FieldType.STRING.toString(), "destination_cloud_tags", "Destination Cloud Tags", "DestCloudTags"),
    TRAFFIC_STATUS(FieldType.STRING.toString(), "traffic_status", "Traffic Status", "TrafficStatus"),

    PACKETS_SENT(FieldType.NUMBER.toString(), "packets_sent", "Packets Sent", "PacketsSent"),
    PACKETS_RECEIVED(FieldType.NUMBER.toString(), "packets_received", "Packets Received", "PacketsReceived"),
    SOURCE_THREAT_LEVEL(FieldType.NUMBER.toString(), "source_threat_level", "Source Threat Level", "SrcThreatLevel"),
    DESTINATION_THREAT_LEVEL(
            FieldType.NUMBER.toString(), "destination_threat_level", "Destination Threat Level", "DestThreatLevel"),
    SOURCE_WELL_KNOWN(FieldType.STRING.toString(), "source_well_known", "Source Well Known", "SrcIsWellknown"),

    DESTINATION_WELL_KNOWN(
            FieldType.STRING.toString(), "destination_well_known", "Destination Well Known", "DestIsWellknown"),
    SOURCE_DOMAIN(FieldType.STRING.toString(), "source_domain", "Source Domain", "SrcDomain"),
    DESTINATION_DOMAIN(FieldType.STRING.toString(), "destination_domain", "Destination Domain", "DestDomain"),
    SOURCE_COUNTRY(FieldType.STRING.toString(), "source_country", "Source Country", "SrcCountry"),
    DESTINATION_COUNTRY(FieldType.STRING.toString(), "destination_country", "Destination Country", "DestCountry"),

    SOURCE_CITY(FieldType.STRING.toString(), "source_city", "Source City", "SrcCity"),
    DESTINATION_CITY(FieldType.STRING.toString(), "destination_city", "Destination City", "DestCity"),

    // Wrong type in DB currently
    START_TIME(FieldType.STRING.toString(), "start_time", "Start Time", "StartTime"),
    END_TIME(FieldType.STRING.toString(), "end_time", "End Time", "EndTime"),

    COUNT(FieldType.NUMBER.toString(), "count", "Count", "Count"),

    TOTAL_ROWS(FieldType.NUMBER.toString(), "total_rows", "Total Rows", "totalRows"),
    AGGREGATE_FIELD(FieldType.STRING.toString(), "aggregate_field", "Aggregate Field", "AggregateField"),
    TIME_SERIES(FieldType.STRING.toString(), "time_series", "Time Series", "TimeSeries"),

    /**
     * Special function in Kusto, not a database column
     */
    INGESTION_TIME(FieldType.TIMESTAMP.toString(), "ingestion_time", "Ingestion Time", "ingestion_time()"),

    DESTINATION_EXTERNAL_LABEL_CATEGORY(FieldType.STRING.toString(), "dest_ext_label_category", "Destination External Label Category", "DestinationExternalLabelCategory"),
    DESTINATION_EXTERNAL_LABEL(FieldType.STRING.toString(), "dest_ext_label", "Destination External Label", "DestinationExternalLabel"),


    SOURCE_EXTERNAL_LABEL(FieldType.STRING.toString(), "source_external_label", "Source External Label", "SourceExternalLabel"),
    SOURCE_EXTERNAL_LABEL_CATEGORY(FieldType.STRING.toString(), "source_external_label_category", "Source External Label Category", "SourceExternalLabelCategory"),


    SOURCE_CLOUD_PROVIDER(
            FieldType.STRING.toString(), "source_cloud_provider", "Source Cloud Provider", "SrcCloudProvider"),
    DESTINATION_CLOUD_PROVIDER(
            FieldType.STRING.toString(),
            "destination_cloud_provider",
            "Destination Cloud Provider",
            "DestCloudProvider"),

    TRAFFIC_DIRECTION(FieldType.STRING.toString(), "traffic_direction", "Traffic Direction", "TrafficDirection"),


    SOURCE_RESOURCE_NAME(FieldType.STRING.toString(), "source_resource_name", "Source Resource Name", "SrcResourceName"),
    SOURCE_RESOURCE_TYPE(FieldType.STRING.toString(), "source_resource_type", "Source Resource Type", "SrcResourceType"),
    SOURCE_RESOURCE_CAT(FieldType.STRING.toString(), "source_resource_cat", "Source Resource Category", "SrcResourceCategory"),

    DEST_RESOURCE_NAME(FieldType.STRING.toString(), "dest_resource_name", "Dest Resource Name", "DestResourceName"),
    DEST_RESOURCE_TYPE(FieldType.STRING.toString(), "dest_resource_type", "Dest Resource Type", "DestResourceType"),
    DEST_RESOURCE_CAT(FieldType.STRING.toString(), "dest_resource_cat", "Dest Resource Category", "DestResourceCategory"),
    ;

    private final String fieldType;
    private final String fieldKey;
    private final String fieldDisplayName;
    private final String tableColumnName;
    private final boolean selectedByDefault;

    private static final Map<String, String> fieldKeyToTableColumnNameMap = new HashMap<>();
    @Getter private static final Set<String> fieldKeySet = new HashSet<>();

    private static final List<Fields> DEPRECATED =
            List.of(FLOWS_TREND, PREVIOUS_FLOWS, BYTES_TREND, BYTES, PREVIOUS_BYTES);

    private static final List<Fields> INTERNAL_USE =
            List.of(COUNT, TOTAL_ROWS, AGGREGATE_FIELD, TIME_SERIES, SERVICE, SEVERITY,
                INGESTION_TIME, TRAFFIC_DIRECTION);

    static {
        for (Fields field : Fields.values()) {
            fieldKeyToTableColumnNameMap.put(field.getFieldKey(), field.getTableColumnName());
            fieldKeySet.add(field.getFieldKey());
        }
    }

    Fields(String fieldType, String fieldKey, String fieldDisplayName, String tableColumnName) {
        this(fieldType, fieldKey, fieldDisplayName, tableColumnName, false);
    }

    Fields(
            String fieldType,
            String fieldKey,
            String fieldDisplayName,
            String tableColumnName,
            boolean selectedByDefault) {
        this.fieldType = fieldType;
        this.fieldKey = fieldKey;
        this.fieldDisplayName = fieldDisplayName;
        this.tableColumnName = tableColumnName;
        this.selectedByDefault = selectedByDefault;
    }

    public boolean isNumericField(){
        return this.fieldType.equalsIgnoreCase(FieldType.NUMBER.toString());
    }

    public boolean isTimeStampField(){
        return this.fieldType.equalsIgnoreCase(FieldType.TIMESTAMP.toString());
    }

    public boolean isStringField(){
        return this.fieldType.equalsIgnoreCase(FieldType.STRING.toString());
    }

    public static String getTableColumnNameByFieldKey(String fieldKey) {
        return fieldKeyToTableColumnNameMap.get(fieldKey);
    }

    public static Fields getFieldByFieldKey(String fieldKey) {
        return Arrays.stream(Fields.values())
                .filter(field -> field.getFieldKey().equals(fieldKey))
                .findFirst()
                .orElse(null);
    }

    public static boolean isNumericField(String fieldKey) {
        Fields field = getFieldByFieldKey(fieldKey);
        if (field == null) { return false; }

        return field.isNumericField();
    }

    public static boolean isTimeStampField(String fieldKey) {
        Fields field = getFieldByFieldKey(fieldKey);
        if (field == null) { return false; }

        return field.isTimeStampField();
    }

    public static Fields getFieldByTableColumnName(String tableColumnName) {
        return Arrays.stream(Fields.values())
            .filter(field -> field.getTableColumnName().equals(tableColumnName))
            .findFirst()
            .orElse(null);
    }

    public static List<Fields> getFieldsForUI() {
        return Arrays.stream(Fields.values())
                .filter(f -> !DEPRECATED.contains(f))
                .filter(f -> !INTERNAL_USE.contains(f))
                .collect(Collectors.toList());
    }

    public static Set<String> getFieldsKeyForUI() {
        return Arrays.stream(Fields.values())
            .filter(f -> !DEPRECATED.contains(f))
            .filter(f -> !INTERNAL_USE.contains(f))
            .map(Fields::getFieldKey)
            .collect(Collectors.toSet());
    }
}
