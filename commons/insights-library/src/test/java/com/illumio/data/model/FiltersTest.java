package com.illumio.data.model;

import com.illumio.data.model.constants.Fields;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;

import java.util.List;

public class FiltersTest {
    @Test
    void testKQLStringFilter() {
        Filters f =
                Filters.builder()
                        .categoryType("string")
                        .categoryName("protocol")
                        .categoryValue(List.of("tcp", "udp"))
                        .build();

        String kql = f.buildKQL();

        // the field name transfer to the column names based on Fields
        Assertions.assertEquals("| where Proto  in~  ('tcp', 'udp')", kql);

        Assertions.assertEquals("Proto  in~  ('tcp', 'udp')", f.buildInnerKQL());
    }

    @Test
    void testKQLNumberFilter() {
        Filters f =
                Filters.builder()
                        .categoryType("integer")
                        .categoryName("port")
                        .categoryValue(List.of(21117, 21118))
                        .build();

        String kql = f.buildKQL();

        // the field name transfer to the column names based on Fields
        Assertions.assertEquals("| where Port  in~  (21117, 21118)", kql);

        Assertions.assertEquals("Port  in~  (21117, 21118)", f.buildInnerKQL());
    }

    @Test
    void testDestExtLabelFilter() {
        Filters category =
                Filters.builder()
                        .categoryType("string")
                        .categoryName(Fields.DESTINATION_EXTERNAL_LABEL_CATEGORY.getFieldKey())
                        .categoryValue(List.of("CSP"))
                        .build();

        Filters label =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.DESTINATION_EXTERNAL_LABEL.getFieldKey())
                .categoryValue(List.of("Azure"))
                .build();

        String result = SrcDestExtLabelFilter.adjustExtLabel(category, label);
        Assertions.assertEquals(" DestResId == '' and DestCloudProvider in~ ('azure','microsoft')", result);


        category =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.DESTINATION_EXTERNAL_LABEL_CATEGORY.getFieldKey())
                .categoryValue(List.of("IP"))
                .build();

        label =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.DESTINATION_EXTERNAL_LABEL.getFieldKey())
                .categoryValue(List.of("malicious"))
                .build();

        result = SrcDestExtLabelFilter.adjustExtLabel(category, label);
        Assertions.assertEquals(" DestThreatLevel >= 2 ", result);

        label =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.DESTINATION_EXTERNAL_LABEL.getFieldKey())
                .categoryValue(List.of("Known Internet"))
                .build();

        result = SrcDestExtLabelFilter.adjustExtLabel(category, label);
        Assertions.assertEquals(" DestIsWellknown == 'true' ", result);

        label =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.DESTINATION_EXTERNAL_LABEL.getFieldKey())
                .categoryValue(List.of("Unknown Internet"))
                .build();

        result = SrcDestExtLabelFilter.adjustExtLabel(category, label);
        Assertions.assertEquals(" DestIsWellknown != 'true' ", result);
    }

    @Test
    void testSrcExtLabelFilter() {
        Filters category =
                Filters.builder()
                        .categoryType("string")
                        .categoryName(Fields.SOURCE_EXTERNAL_LABEL_CATEGORY.getFieldKey())
                        .categoryValue(List.of("CSP"))
                        .build();

        Filters label =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.SOURCE_EXTERNAL_LABEL.getFieldKey())
                .categoryValue(List.of("Azure"))
                .build();

        String result = SrcDestExtLabelFilter.adjustExtLabel(category, label);
        Assertions.assertEquals(" SrcResId == '' and SrcCloudProvider in~ ('azure','microsoft')", result);


        category =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.SOURCE_EXTERNAL_LABEL_CATEGORY.getFieldKey())
                .categoryValue(List.of("IP"))
                .build();

        label =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.SOURCE_EXTERNAL_LABEL.getFieldKey())
                .categoryValue(List.of("malicious"))
                .build();

        result = SrcDestExtLabelFilter.adjustExtLabel(category, label);
        Assertions.assertEquals(" SrcThreatLevel >= 2 ", result);

        label =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.SOURCE_EXTERNAL_LABEL.getFieldKey())
                .categoryValue(List.of("Known Internet"))
                .build();

        result = SrcDestExtLabelFilter.adjustExtLabel(category, label);
        Assertions.assertEquals(" SrcIsWellknown == 'true' ", result);

        label =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.SOURCE_EXTERNAL_LABEL.getFieldKey())
                .categoryValue(List.of("Unknown Internet"))
                .build();

        result = SrcDestExtLabelFilter.adjustExtLabel(category, label);
        Assertions.assertEquals(" SrcIsWellknown != 'true' ", result);

        category =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.SOURCE_EXTERNAL_LABEL_CATEGORY.getFieldKey())
                .categoryValue(List.of("APP"))
                .build();

        label =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.SOURCE_EXTERNAL_LABEL.getFieldKey())
                .categoryValue(List.of("CloudDrive"))
                .build();
        result = SrcDestExtLabelFilter.adjustExtLabel(category, label);
        Assertions.assertEquals("((SourceExternalLabelCategory  in~  ( 'APP' )) and (SourceExternalLabel  in~  ( 'CloudDrive' )))", result);
    }

    @Test
    void testInOutBoundFilterDecorator() {
        Filters f =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.TRAFFIC_DIRECTION.getFieldKey())
                .categoryValue(List.of("OUTBOUND"))
                .build();

        Filters decorator = new InOutBoundFilterDecorator(f);

        String kql = decorator.buildKQL();
        Assertions.assertEquals("| where SrcResId != '' and DestResId == ''", kql);

        f =
            Filters.builder()
                .categoryType("string")
                .categoryName(Fields.TRAFFIC_DIRECTION.getFieldKey())
                .categoryValue(List.of("inbound"))
                .build();

        decorator = new InOutBoundFilterDecorator(f);

        kql = decorator.buildKQL();
        Assertions.assertEquals("| where SrcResId == '' and DestResId != ''", kql);
    }


}
