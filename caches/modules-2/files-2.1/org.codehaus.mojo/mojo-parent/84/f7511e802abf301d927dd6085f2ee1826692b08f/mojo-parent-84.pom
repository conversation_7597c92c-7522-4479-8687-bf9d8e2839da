<?xml version="1.0" encoding="UTF-8"?>
<!--
  Licensed to the Apache Software Foundation (ASF) under one
  or more contributor license agreements.  See the NOTICE file
  distributed with this work for additional information
  regarding copyright ownership.  The ASF licenses this file
  to you under the Apache License, Version 2.0 (the
  "License"); you may not use this file except in compliance
  with the License.  You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

  Unless required by applicable law or agreed to in writing,
  software distributed under the License is distributed on an
  "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
  KIND, either express or implied.  See the License for the
  specific language governing permissions and limitations
  under the License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <groupId>org.codehaus.mojo</groupId>
  <artifactId>mojo-parent</artifactId>
  <version>84</version>
  <packaging>pom</packaging>

  <name>MojoHaus Parent</name>
  <description>Parent POM for all MojoHaus hosted Apache Maven plugins and components.</description>
  <url>https://www.mojohaus.org/${project.artifactId}</url>
  <organization>
    <name>MojoHaus</name>
    <url>https://www.mojohaus.org</url>
  </organization>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>aheritier</id>
      <name>Arnaud Heritier</name>
      <url>https://github.com/aheritier</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>Batmat</id>
      <name>Baptiste Mathus</name>
      <url>https://github.com/Batmat</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>davidkarlsen</id>
      <name>David Karlsen</name>
      <url>https://github.com/davidkarlsen</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>Godin</id>
      <name>Evgeny Mandrikov</name>
      <url>https://github.com/Godin</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>hboutemy</id>
      <name>Hervé Boutemy</name>
      <url>https://github.com/hboutemy</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>khmarbaise</id>
      <name>Karl-Heinz Marbaise</name>
      <url>https://github.com/khmarbaise</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>lennartj</id>
      <name>Lennart Jörelid</name>
      <url>https://github.com/lennartj</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>mfriedenhagen</id>
      <name>Mirko Friedenhagen</name>
      <url>https://github.com/mfriedenhagen</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>andham</id>
      <name>Anders Hammar</name>
      <url>https://github.com/andham</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>olamy</id>
      <name>Olivier Lamy</name>
      <url>https://github.com/olamy</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>sjaranowski</id>
      <name>Slawomir Jaranowski</name>
      <url>https://github.com/slawekjaranowski</url>
      <organization>MojoHaus</organization>
      <organizationUrl>https://github.com/mojohaus</organizationUrl>
    </developer>
    <developer>
      <id>slachiewicz</id>
      <name>Sylwester Lachiewicz</name>
      <url>https://github.com/slachiewicz</url>
    </developer>
  </developers>

  <mailingLists>
    <mailingList>
      <name>MojoHaus Development List</name>
      <subscribe><EMAIL></subscribe>
      <unsubscribe><EMAIL></unsubscribe>
      <post><EMAIL></post>
      <archive>https://groups.google.com/forum/#!forum/mojohaus-dev</archive>
    </mailingList>
    <mailingList>
      <name>Maven User List</name>
      <subscribe>mailto:<EMAIL></subscribe>
      <unsubscribe>mailto:<EMAIL></unsubscribe>
      <post>mailto:<EMAIL></post>
      <archive>https://lists.apache.org/list.html?<EMAIL></archive>
    </mailingList>
  </mailingLists>

  <scm>
    <connection>scm:git:https://github.com/mojohaus/mojo-parent.git</connection>
    <developerConnection>scm:git:https://github.com/mojohaus/mojo-parent.git</developerConnection>
    <tag>84</tag>
    <url>https://github.com/mojohaus/mojo-parent/tree/${project.scm.tag}</url>
  </scm>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/mojohaus/${project.artifactId}/issues</url>
  </issueManagement>
  <ciManagement>
    <system>GitHub</system>
    <url>https://github.com/mojohaus/${project.artifactId}/actions</url>
  </ciManagement>

  <distributionManagement>
    <repository>
      <id>ossrh-staging</id>
      <url>https://oss.sonatype.org/service/local/staging/deploy/maven2</url>
    </repository>
    <snapshotRepository>
      <id>ossrh-snapshots</id>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <site>
      <id>github</id>
      <url>scm:git:ssh://**************/mojohaus/mojo-parent.git</url>
    </site>
  </distributionManagement>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <mojo.java.target>8</mojo.java.target>
    <maven.compiler.source>${mojo.java.target}</maven.compiler.source>
    <maven.compiler.target>${mojo.java.target}</maven.compiler.target>

    <minimalJavaBuildVersion>${mojo.java.target}</minimalJavaBuildVersion>
    <minimalMavenBuildVersion>3.6.3</minimalMavenBuildVersion>
    <mavenVersion>${minimalMavenBuildVersion}</mavenVersion>

    <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
    <!-- NOTE: We deliberately do not set maven.test.redirectTestOutputToFile here to workaround MNG-1992 -->
    <surefire.redirectTestOutputToFile>true</surefire.redirectTestOutputToFile>
    <!-- this property makes sure NetBeans 6.8+ picks up some formatting rules from checkstyle -->
    <netbeans.checkstyle.format>true</netbeans.checkstyle.format>
    <animal-sniffer-maven-plugin.version>1.23</animal-sniffer-maven-plugin.version>
    <build-helper-maven-plugin.version>3.6.0</build-helper-maven-plugin.version>
    <flatten-maven-plugin.version>1.6.0</flatten-maven-plugin.version>
    <l10n-maven-plugin.version>1.0.0</l10n-maven-plugin.version>
    <license-maven-plugin.version>2.4.0</license-maven-plugin.version>
    <maven-antrun-plugin.version>3.1.0</maven-antrun-plugin.version>
    <maven-assembly-plugin.version>3.7.1</maven-assembly-plugin.version>
    <maven-changes-plugin.version>2.11</maven-changes-plugin.version>
    <maven-checkstyle-plugin.version>3.4.0</maven-checkstyle-plugin.version>
    <maven-clean-plugin.version>3.3.2</maven-clean-plugin.version>
    <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
    <maven-dependency-plugin.version>3.7.0</maven-dependency-plugin.version>
    <maven-deploy-plugin.version>3.1.2</maven-deploy-plugin.version>
    <maven-ear-plugin.version>3.3.0</maven-ear-plugin.version>
    <maven-enforcer-plugin.version>3.5.0</maven-enforcer-plugin.version>
    <maven-failsafe-plugin.version>3.2.5</maven-failsafe-plugin.version>
    <maven-fluido-skin.version>1.12.0</maven-fluido-skin.version>
    <maven-gpg-plugin.version>3.2.4</maven-gpg-plugin.version>
    <maven-help-plugin.version>3.4.1</maven-help-plugin.version>
    <maven-install-plugin.version>3.1.2</maven-install-plugin.version>
    <maven-invoker-plugin.version>3.7.0</maven-invoker-plugin.version>
    <maven-jar-plugin.version>3.4.1</maven-jar-plugin.version>
    <maven-javadoc-plugin.version>3.7.0</maven-javadoc-plugin.version>
    <maven-jxr-plugin.version>3.4.0</maven-jxr-plugin.version>
    <maven-plugin-plugin.version>3.13.1</maven-plugin-plugin.version>
    <maven-project-info-reports-plugin.version>3.5.0</maven-project-info-reports-plugin.version>
    <maven-pmd-plugin.version>3.19.0</maven-pmd-plugin.version>
    <maven-release-plugin.version>3.0.1</maven-release-plugin.version>
    <maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
    <maven-scm-publish-plugin.version>3.2.1</maven-scm-publish-plugin.version>
    <maven-shade-plugin.version>3.6.0</maven-shade-plugin.version>
    <maven-site-plugin.version>3.12.1</maven-site-plugin.version>
    <maven-source-plugin.version>3.3.1</maven-source-plugin.version>
    <maven-surefire-plugin.version>3.2.5</maven-surefire-plugin.version>
    <maven-surefire-report-plugin.version>3.2.5</maven-surefire-report-plugin.version>
    <maven-war-plugin.version>3.4.0</maven-war-plugin.version>
    <maven-wrapper-plugin.version>3.3.2</maven-wrapper-plugin.version>

    <mrm-maven-plugin.version>1.6.0</mrm-maven-plugin.version>
    <modello-maven-plugin.version>2.4.0</modello-maven-plugin.version>
    <plexus-component-metadata.version>2.2.0</plexus-component-metadata.version>
    <taglist-maven-plugin.version>3.0.0</taglist-maven-plugin.version>
    <versions-maven-plugin.version>2.16.2</versions-maven-plugin.version>
    <sisu-maven-plugin.version>0.9.0.M3</sisu-maven-plugin.version>
    <spotless-maven-plugin.version>2.43.0</spotless-maven-plugin.version>
    <project.build.outputTimestamp>2024-06-13T22:12:53Z</project.build.outputTimestamp>
    <!-- mono-module doesn't require site:stage for scm-publish -->
    <scmpublish.content>${project.reporting.outputDirectory}</scmpublish.content>
    <junit5.version>5.10.2</junit5.version>
    <!-- 10.x requires Java 11 -->
    <checkstyle.version>9.3</checkstyle.version>
    <!-- by default use simple configuration for checkstyle rules to use with spotless -->
    <checkstyle.config.location>config/maven_checks_nocodestyle.xml</checkstyle.config.location>

    <invoker.streamLogsOnFailures>true</invoker.streamLogsOnFailures>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-plugin-api</artifactId>
        <!-- Minimum required Maven Version -->
        <version>${mavenVersion}</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>4.13.2</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${junit5.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.maven.plugin-tools</groupId>
        <artifactId>maven-plugin-annotations</artifactId>
        <!--must be same version of m-plugin-p -->
        <version>${maven-plugin-plugin.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <pluginManagement>
      <plugins>
        <!-- Apache plugins in alphabetical order -->
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>${maven-antrun-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>${maven-assembly-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-checkstyle-plugin</artifactId>
          <version>${maven-checkstyle-plugin.version}</version>
          <configuration>
            <sourceDirectories>
              <sourceDirectory>src/main/java</sourceDirectory>
            </sourceDirectories>
            <testSourceDirectories>
              <testSourceDirectory>src/test/java</testSourceDirectory>
            </testSourceDirectories>
            <includeTestSourceDirectory>true</includeTestSourceDirectory>
            <configLocation>${checkstyle.config.location}</configLocation>
            <!-- by default don't check headers -->
            <headerLocation>mojohaus/config/checkstyle/empty-header.txt</headerLocation>
          </configuration>
          <dependencies>
            <dependency>
              <groupId>org.apache.maven.shared</groupId>
              <artifactId>maven-shared-resources</artifactId>
              <version>6</version>
            </dependency>
            <dependency>
              <groupId>org.codehaus.mojo</groupId>
              <artifactId>mojo-parent</artifactId>
              <version>84</version>
              <classifier>config</classifier>
            </dependency>
            <dependency>
              <groupId>com.puppycrawl.tools</groupId>
              <artifactId>checkstyle</artifactId>
              <version>${checkstyle.version}</version>
            </dependency>
          </dependencies>
          <executions>
            <execution>
              <goals>
                <goal>check</goal>
              </goals>
              <phase>process-sources</phase>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>${maven-clean-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>${maven-dependency-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${maven-deploy-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${maven-enforcer-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-help-plugin</artifactId>
          <version>${maven-help-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-gpg-plugin</artifactId>
          <version>${maven-gpg-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>${maven-install-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-invoker-plugin</artifactId>
          <version>${maven-invoker-plugin.version}</version>
          <configuration>
            <streamLogsOnFailures>${invoker.streamLogsOnFailures}</streamLogsOnFailures>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${maven-jar-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-war-plugin</artifactId>
          <version>${maven-war-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-ear-plugin</artifactId>
          <version>${maven-ear-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${maven-javadoc-plugin.version}</version>
          <configuration>
            <quiet>true</quiet>
            <locale>en</locale>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jxr-plugin</artifactId>
          <version>${maven-jxr-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-plugin</artifactId>
          <version>${maven-plugin-plugin.version}</version>
          <executions>
            <execution>
              <id>help-mojo</id>
              <goals>
                <goal>helpmojo</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-plugin-report-plugin</artifactId>
          <version>${maven-plugin-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-project-info-reports-plugin</artifactId>
          <version>${maven-project-info-reports-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-release-plugin</artifactId>
          <version>${maven-release-plugin.version}</version>
          <configuration>
            <!-- do not deploy site but use instructions in README.md -->
            <goals>deploy</goals>
            <arguments>-Pmojo-release</arguments>
            <autoVersionSubmodules>true</autoVersionSubmodules>
            <tagNameFormat>@{project.version}</tagNameFormat>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>${maven-resources-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-scm-publish-plugin</artifactId>
          <version>${maven-scm-publish-plugin.version}</version>
          <configuration>
            <pubScmUrl>${project.scm.developerConnection}</pubScmUrl>
            <scmBranch>gh-pages</scmBranch>
          </configuration>
          <executions>
            <execution>
              <id>scm-publish</id>
              <goals>
                <goal>publish-scm</goal>
              </goals>
              <!-- deploy site with maven-scm-publish-plugin -->
              <phase>site-deploy</phase>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>${maven-shade-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-site-plugin</artifactId>
          <version>${maven-site-plugin.version}</version>
          <configuration>
            <!-- don't deploy site with maven-site-plugin -->
            <skipDeploy>true</skipDeploy>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>${maven-source-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
          <configuration>
            <redirectTestOutputToFile>${surefire.redirectTestOutputToFile}</redirectTestOutputToFile>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>${maven-failsafe-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-report-plugin</artifactId>
          <version>${maven-surefire-report-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-wrapper-plugin</artifactId>
          <version>${maven-wrapper-plugin.version}</version>
        </plugin>

        <!-- Codehaus plugins in alphabetical order -->
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>animal-sniffer-maven-plugin</artifactId>
          <version>${animal-sniffer-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>${build-helper-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>flatten-maven-plugin</artifactId>
          <version>${flatten-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>l10n-maven-plugin</artifactId>
          <version>${l10n-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>license-maven-plugin</artifactId>
          <version>${license-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.modello</groupId>
          <artifactId>modello-maven-plugin</artifactId>
          <version>${modello-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>mrm-maven-plugin</artifactId>
          <version>${mrm-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.plexus</groupId>
          <artifactId>plexus-component-metadata</artifactId>
          <version>${plexus-component-metadata.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>taglist-maven-plugin</artifactId>
          <version>${taglist-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>versions-maven-plugin</artifactId>
          <version>${versions-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.eclipse.sisu</groupId>
          <artifactId>sisu-maven-plugin</artifactId>
          <version>${sisu-maven-plugin.version}</version>
          <executions>
            <execution>
              <id>generate-index</id>
              <goals>
                <goal>main-index</goal>
                <goal>test-index</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>com.diffplug.spotless</groupId>
          <artifactId>spotless-maven-plugin</artifactId>
          <version>${spotless-maven-plugin.version}</version>
          <configuration>
            <java>
              <!-- orders of used formatters are important -->
              <!-- eg. palantir override importOrder, so should be first -->
              <palantirJavaFormat />
              <removeUnusedImports />
              <importOrder>
                <order>javax,java,,\#</order>
              </importOrder>
            </java>
            <pom>
              <sortPom>
                <expandEmptyElements>false</expandEmptyElements>
                <!-- https://issues.apache.org/jira/browse/MRELEASE-1111 -->
                <spaceBeforeCloseEmptyElement>true</spaceBeforeCloseEmptyElement>
              </sortPom>
            </pom>
            <markdown>
              <includes>
                <include>**/*.md</include>
              </includes>
              <excludes>
                <excludes>target/**</excludes>
              </excludes>
              <flexmark />
            </markdown>
            <upToDateChecking>
              <enabled>true</enabled>
            </upToDateChecking>
          </configuration>
          <executions>
            <execution>
              <id>spotless-check</id>
              <goals>
                <goal>check</goal>
              </goals>
              <phase>process-sources</phase>
            </execution>
          </executions>
        </plugin>
      </plugins>
    </pluginManagement>

    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>mojo-enforcer-rules</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <phase>validate</phase>
            <configuration>
              <rules>
                <bannedDependencies>
                  <excludes>
                    <exclude>org.codehaus.plexus:plexus-component-api</exclude>
                  </excludes>
                  <message>The plexus-component-api conflicts with the plexus-container-default used by Maven. You probably added a dependency that is missing the exclusions.</message>
                </bannedDependencies>
                <requireNoRepositories>
                  <message>Mojo is synchronized with repo1.maven.org.  The rules for repo1.maven.org are that pom.xml files should not include repository definitions.  If repository definitions are included, they must be limited to SNAPSHOT only repositories.</message>
                  <banRepositories>true</banRepositories>
                  <banPluginRepositories>true</banPluginRepositories>
                  <allowSnapshotRepositories>true</allowSnapshotRepositories>
                  <allowedPluginRepositories>
                    <allowedPluginRepository>ossrh-snapshots</allowedPluginRepository>
                    <allowedPluginRepository>apache.snapshots</allowedPluginRepository>
                  </allowedPluginRepositories>
                </requireNoRepositories>
                <requirePluginVersions>
                  <message>Best Practice is to always define plugin versions!</message>
                  <banLatest>true</banLatest>
                  <banRelease>true</banRelease>
                </requirePluginVersions>
                <requireMavenVersion>
                  <version>${minimalMavenBuildVersion}</version>
                  <message>You need at least Maven ${minimalMavenBuildVersion} to build MojoHaus projects.</message>
                </requireMavenVersion>
                <requireJavaVersion>
                  <version>${minimalJavaBuildVersion}</version>
                </requireJavaVersion>
                <requireProperty>
                  <property>project.scm.connection</property>
                  <!-- because Maven adds the artifactId to the SCM-Url automatically for modules in a multimodule project,
                  we need to allow stuff after .git.
                  -->
                  <regex>scm:git:https://github.com/.*\.git.*</regex>
                  <regexMessage>https (scm:git:https://github.com/.*\.git) is the preferred protocol for project.scm.connection, current value: ${project.scm.connection}</regexMessage>
                </requireProperty>
                <requireProperty>
                  <property>project.scm.url</property>
                  <!-- because Maven adds the artifactId to the SCM-Url automatically for modules in a multimodule project,
                  we need to allow stuff after .git.
                  -->
                  <regex>https://github.com/.*</regex>
                  <regexMessage>Use https://github.com/.* as project.scm.url, especially using the prefix scm:git here will lead to unbrowseable links during site generation, current value: ${project.scm.url}</regexMessage>
                </requireProperty>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.diffplug.spotless</groupId>
        <artifactId>spotless-maven-plugin</artifactId>
        <executions>
          <execution>
            <id>spotless-check</id>
            <goals>
              <goal>check</goal>
            </goals>
            <phase>validate</phase>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <inherited>false</inherited>
        <executions>
          <execution>
            <id>attach-descriptor</id>
            <goals>
              <goal>attach-descriptor</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-assembly-plugin</artifactId>
        <executions>
          <execution>
            <id>assembly-configuration</id>
            <goals>
              <goal>single</goal>
            </goals>
            <phase>package</phase>
            <inherited>false</inherited>
            <configuration>
              <descriptors>
                <descriptor>config-assembly.xml</descriptor>
              </descriptors>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <profiles>
    <profile>
      <id>jdk8</id>
      <activation>
        <jdk>[1.8,11)</jdk>
      </activation>
      <properties>
        <!-- last version working with java 8 -->
        <spotless-maven-plugin.version>2.30.0</spotless-maven-plugin.version>
      </properties>
    </profile>
    <profile>
      <id>java11+</id>
      <activation>
        <jdk>[11,)</jdk>
      </activation>
      <properties>
        <maven.compiler.release>${mojo.java.target}</maven.compiler.release>
      </properties>
    </profile>

    <profile>
      <id>mojo-release</id>
      <properties>
        <!-- due to m-deploy-p 3.x we need use Maven 3.9.x at least -->
        <!-- https://issues.apache.org/jira/browse/MNG-7055 -->
        <minimalMavenBuildVersion>3.9.0</minimalMavenBuildVersion>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-assembly-plugin</artifactId>
            <dependencies>
              <dependency>
                <groupId>org.apache.apache.resources</groupId>
                <artifactId>apache-source-release-assembly-descriptor</artifactId>
                <version>1.5</version>
              </dependency>
            </dependencies>
            <executions>
              <execution>
                <id>attach-source-release-distro</id>
                <goals>
                  <goal>single</goal>
                </goals>
                <phase>package</phase>
                <configuration>
                  <runOnlyAtExecutionRoot>true</runOnlyAtExecutionRoot>
                  <descriptorRefs>
                    <descriptorRef>source-release</descriptorRef>
                  </descriptorRefs>
                </configuration>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-deploy-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <executions>
              <execution>
                <id>default-jar-no-fork</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <failOnError>false</failOnError>
            </configuration>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <goals>
                  <goal>sign</goal>
                </goals>
                <phase>verify</phase>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>reporting</id>
      <activation>
        <property>
          <name>skipReports</name>
          <value>!true</value>
        </property>
      </activation>
      <reporting>
        <plugins>
          <!-- Apache plugins in alphabetical order -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>${maven-javadoc-plugin.version}</version>
            <reportSets>
              <reportSet>
                <reports>
                  <report>javadoc-no-fork</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-plugin-report-plugin</artifactId>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-project-info-reports-plugin</artifactId>
            <version>${maven-project-info-reports-plugin.version}</version>
            <reportSets>
              <reportSet>
                <reports>
                  <report>ci-management</report>
                  <report>dependencies</report>
                  <report>dependency-convergence</report>
                  <report>dependency-info</report>
                  <report>dependency-management</report>
                  <report>index</report>
                  <report>issue-management</report>
                  <report>licenses</report>
                  <report>mailing-lists</report>
                  <report>plugin-management</report>
                  <report>scm</report>
                  <report>team</report>
                  <report>summary</report>
                </reports>
              </reportSet>
            </reportSets>
          </plugin>
        </plugins>
      </reporting>
    </profile>
  </profiles>
</project>
