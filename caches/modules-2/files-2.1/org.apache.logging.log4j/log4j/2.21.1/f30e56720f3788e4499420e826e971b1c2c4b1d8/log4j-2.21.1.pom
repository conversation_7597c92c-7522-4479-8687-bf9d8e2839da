<?xml version="1.0" encoding="UTF-8"?>
<!--
  ~ Licensed to the Apache Software Foundation (ASF) under one or more
  ~ contributor license agreements.  See the NOTICE file distributed with
  ~ this work for additional information regarding copyright ownership.
  ~ The ASF licenses this file to you under the Apache License, Version 2.0
  ~ (the "License"); you may not use this file except in compliance with
  ~ the License.  You may obtain a copy of the License at
  ~
  ~      http://www.apache.org/licenses/LICENSE-2.0
  ~
  ~ Unless required by applicable law or agreed to in writing, software
  ~ distributed under the License is distributed on an "AS IS" BASIS,
  ~ WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
  ~ See the License for the specific language governing permissions and
  ~ limitations under the License.
  -->
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.apache.logging.log4j</groupId>
    <artifactId>log4j-bom</artifactId>
    <version>2.21.1</version>
  </parent>
  <groupId>org.apache.logging.log4j</groupId>
  <artifactId>log4j</artifactId>
  <version>2.21.1</version>
  <packaging>pom</packaging>
  <name>Apache Log4j Parent</name>
  <description>Apache Log4j Parent</description>
  <licenses>
    <license>
      <name>Apache-2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <properties>
    <jackson-bom.version>2.15.2</jackson-bom.version>
    <jmdns.version>3.5.8</jmdns.version>
    <zstd.version>1.5.5-6</zstd.version>
    <felix.version>7.0.5</felix.version>
    <wiremock.version>2.35.1</wiremock.version>
    <jconsole.version>1.7.0</jconsole.version>
    <commons-dbcp2.version>2.9.0</commons-dbcp2.version>
    <docker-maven-plugin.version>0.43.4</docker-maven-plugin.version>
    <javax-servlet-jsp.version>2.3.3</javax-servlet-jsp.version>
    <mongodb.version>4.10.2</mongodb.version>
    <system-stubs.version>2.0.2</system-stubs.version>
    <jakartaee-bom.version>9.1.0</jakartaee-bom.version>
    <java-allocation-instrumenter.version>3.3.3</java-allocation-instrumenter.version>
    <hadoop.version>1.2.1</hadoop.version>
    <javax-inject.version>1</javax-inject.version>
    <javax-servlet.version>4.0.1</javax-servlet.version>
    <jakarta-mail.version>2.1.2</jakarta-mail.version>
    <slf4j.version>1.7.36</slf4j.version>
    <xmlunit.version>2.9.1</xmlunit.version>
    <commons-logging.version>1.2</commons-logging.version>
    <commons-lang3.version>3.13.0</commons-lang3.version>
    <log4j.version>1.2.17</log4j.version>
    <xz.version>1.9</xz.version>
    <commons-codec.version>1.15</commons-codec.version>
    <junit.version>4.13.2</junit.version>
    <groovy.version>3.0.19</groovy.version>
    <logback.version>1.2.12</logback.version>
    <jna.version>5.13.0</jna.version>
    <lightcouch.version>0.2.0</lightcouch.version>
    <oro.version>2.0.8</oro.version>
    <conversant.disruptor.version>1.2.15</conversant.disruptor.version>
    <elasticsearch-java.version>8.10.3</elasticsearch-java.version>
    <maven.version>3.9.0</maven.version>
    <osgi.api.version>6.0.0</osgi.api.version>
    <javax-activation.version>1.2.0</javax-activation.version>
    <h2.version>2.2.222</h2.version>
    <commons-compress.version>1.24.0</commons-compress.version>
    <HdrHistogram.version>2.1.12</HdrHistogram.version>
    <kafka.version>3.4.0</kafka.version>
    <woodstox.version>6.5.1</woodstox.version>
    <javax-mail.version>1.6.2</javax-mail.version>
    <tomcat-juli.version>10.0.27</tomcat-juli.version>
    <cassandra.version>3.11.16</cassandra.version>
    <plexus-utils.version>3.5.0</plexus-utils.version>
    <jmh.version>1.37</jmh.version>
    <kubernetes-client.version>5.12.4</kubernetes-client.version>
    <byte-buddy.version>1.14.8</byte-buddy.version>
    <netty.version>4.1.97.Final</netty.version>
    <activemq.version>5.17.4</activemq.version>
    <jetty.version>9.4.53.v20231009</jetty.version>
    <guava.version>32.1.2-jre</guava.version>
    <angus-mail.version>2.0.2</angus-mail.version>
    <hamcrest.version>2.2</hamcrest.version>
    <surefire.version>3.1.2</surefire.version>
    <log4j2-cachefile-transformer.version>2.15.0</log4j2-cachefile-transformer.version>
    <spring-boot.version>2.7.15</spring-boot.version>
    <javax-jms.version>2.0.1</javax-jms.version>
    <httpclient.version>4.5.14</httpclient.version>
    <commons-httpclient.version>3.1</commons-httpclient.version>
    <asciidoctor-maven-plugin.version>2.2.4</asciidoctor-maven-plugin.version>
    <asm.version>9.5</asm.version>
    <assertj.version>3.24.2</assertj.version>
    <jansi.version>2.4.0</jansi.version>
    <disruptor.version>3.4.4</disruptor.version>
    <exam-maven-plugin.version>4.13.5</exam-maven-plugin.version>
    <angus-activation.version>2.0.1</angus-activation.version>
    <commons-io.version>2.11.0</commons-io.version>
    <spring-framework.version>5.3.29</spring-framework.version>
    <bsh.version>2.0b6</bsh.version>
    <embedded-ldap.version>0.9.0</embedded-ldap.version>
    <json-unit.version>2.38.0</json-unit.version>
    <hsqldb.version>2.7.2</hsqldb.version>
    <javax-persistence.version>2.2</javax-persistence.version>
    <mockito.version>4.11.0</mockito.version>
    <flapdoodle-embed.version>4.7.1</flapdoodle-embed.version>
    <flume.version>1.11.0</flume.version>
    <junit-pioneer.version>1.9.1</junit-pioneer.version>
    <org.eclipse.persistence.version>2.7.11</org.eclipse.persistence.version>
    <httpcore.version>4.4.16</httpcore.version>
    <maven-taglib-plugin.version>2.4</maven-taglib-plugin.version>
    <pax-exam.version>4.13.5</pax-exam.version>
    <junit-jupiter.version>5.10.0</junit-jupiter.version>
    <commons-csv.version>1.10.0</commons-csv.version>
    <je.version>18.3.12</je.version>
    <jeromq.version>0.5.3</jeromq.version>
    <cassandra-driver.version>3.11.5</cassandra-driver.version>
    <awaitility.version>4.2.0</awaitility.version>
    <jakarta-activation.version>2.1.2</jakarta-activation.version>
    <org.eclipse.osgi.version>3.13.0.v20180226-1711</org.eclipse.osgi.version>
    <log4j2-ecs-layout.version>1.5.0</log4j2-ecs-layout.version>
    <flapdoodle-reverse.version>1.6.0</flapdoodle-reverse.version>
    <jctools.version>4.0.1</jctools.version>
    <velocity.version>1.7</velocity.version>
    <commons-pool2.version>2.11.1</commons-pool2.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-osgi-test</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-layout-template-json-test</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ow2.asm</groupId>
        <artifactId>asm-bom</artifactId>
        <version>${asm.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.codehaus.groovy</groupId>
        <artifactId>groovy-bom</artifactId>
        <version>${groovy.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson</groupId>
        <artifactId>jackson-bom</artifactId>
        <version>${jackson-bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>jakarta.platform</groupId>
        <artifactId>jakarta.jakartaee-bom</artifactId>
        <version>${jakartaee-bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-bom</artifactId>
        <version>${jetty.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${junit-jupiter.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.fabric8</groupId>
        <artifactId>kubernetes-client-bom</artifactId>
        <version>${kubernetes-client.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-bom</artifactId>
        <version>${mockito.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-bom</artifactId>
        <version>${netty.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-framework-bom</artifactId>
        <version>${spring-framework.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-api-java9</artifactId>
        <version>${project.version}</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core-java9</artifactId>
        <version>${project.version}</version>
        <type>zip</type>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-broker</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>angus-activation</artifactId>
        <version>${angus-activation.version}</version>
      </dependency>
      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-core</artifactId>
        <version>${assertj.version}</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility</artifactId>
        <version>${awaitility.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache-extras.beanshell</groupId>
        <artifactId>bsh</artifactId>
        <version>${bsh.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>bson</artifactId>
        <version>${mongodb.version}</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy</artifactId>
        <version>${byte-buddy.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.cassandra</groupId>
        <artifactId>cassandra-all</artifactId>
        <version>${cassandra.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
          </exclusion>
          <exclusion>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-core</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.datastax.cassandra</groupId>
        <artifactId>cassandra-driver-core</artifactId>
        <version>${cassandra-driver.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.cassandra</groupId>
        <artifactId>cassandra-thrift</artifactId>
        <version>${cassandra.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>jcl-over-slf4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>log4j-over-slf4j</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>${commons-codec.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-compress</artifactId>
        <version>${commons-compress.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-csv</artifactId>
        <version>${commons-csv.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-dbcp2</artifactId>
        <version>${commons-dbcp2.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-httpclient</groupId>
        <artifactId>commons-httpclient</artifactId>
        <version>${commons-httpclient.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>${commons-io.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons-lang3.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-logging</groupId>
        <artifactId>commons-logging</artifactId>
        <version>${commons-logging.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
        <version>${commons-pool2.version}</version>
      </dependency>
      <dependency>
        <groupId>de.flapdoodle.embed</groupId>
        <artifactId>de.flapdoodle.embed.mongo</artifactId>
        <version>${flapdoodle-embed.version}</version>
      </dependency>
      <dependency>
        <groupId>de.flapdoodle.embed</groupId>
        <artifactId>de.flapdoodle.embed.process</artifactId>
        <version>${flapdoodle-embed.version}</version>
      </dependency>
      <dependency>
        <groupId>de.flapdoodle.reverse</groupId>
        <artifactId>de.flapdoodle.reverse</artifactId>
        <version>${flapdoodle-reverse.version}</version>
      </dependency>
      <dependency>
        <groupId>com.conversantmedia</groupId>
        <artifactId>disruptor</artifactId>
        <version>${conversant.disruptor.version}</version>
      </dependency>
      <dependency>
        <groupId>com.lmax</groupId>
        <artifactId>disruptor</artifactId>
        <version>${disruptor.version}</version>
      </dependency>
      <dependency>
        <groupId>co.elastic.clients</groupId>
        <artifactId>elasticsearch-java</artifactId>
        <version>${elasticsearch-java.version}</version>
      </dependency>
      <dependency>
        <groupId>org.zapodot</groupId>
        <artifactId>embedded-ldap-junit</artifactId>
        <version>${embedded-ldap.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.flume.flume-ng-channels</groupId>
        <artifactId>flume-file-channel</artifactId>
        <version>${flume.version}</version>
        <exclusions>
          <exclusion>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.mortbay.jetty</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.mortbay.jetty</groupId>
            <artifactId>servlet-api-2.5</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.flume</groupId>
        <artifactId>flume-ng-core</artifactId>
        <version>${flume.version}</version>
        <exclusions>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.flume</groupId>
        <artifactId>flume-ng-embedded-agent</artifactId>
        <version>${flume.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.flume</groupId>
        <artifactId>flume-ng-node</artifactId>
        <version>${flume.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-log4j12</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.flume</groupId>
        <artifactId>flume-ng-sdk</artifactId>
        <version>${flume.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava-testlib</artifactId>
        <version>${guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <version>${h2.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.hadoop</groupId>
        <artifactId>hadoop-core</artifactId>
        <version>${hadoop.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-core-asl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.codehaus.jackson</groupId>
            <artifactId>jackson-mapper-asl</artifactId>
          </exclusion>
          <exclusion>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.mortbay.jetty</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest</artifactId>
        <version>${hamcrest.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-core</artifactId>
        <version>${hamcrest.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-library</artifactId>
        <version>${hamcrest.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hdrhistogram</groupId>
        <artifactId>HdrHistogram</artifactId>
        <version>${HdrHistogram.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hsqldb</groupId>
        <artifactId>hsqldb</artifactId>
        <version>${hsqldb.version}</version>
        <classifier>jdk8</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>${httpclient.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>${httpcore.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.activation</groupId>
        <artifactId>jakarta.activation-api</artifactId>
        <version>${jakarta-activation.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>jakarta.mail</artifactId>
        <version>${angus-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.mail</groupId>
        <artifactId>jakarta.mail-api</artifactId>
        <version>${jakarta-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>org.fusesource.jansi</groupId>
        <artifactId>jansi</artifactId>
        <version>${jansi.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.java-allocation-instrumenter</groupId>
        <artifactId>java-allocation-instrumenter</artifactId>
        <version>${java-allocation-instrumenter.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.activation</groupId>
        <artifactId>javax.activation-api</artifactId>
        <version>${javax-activation.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.inject</groupId>
        <artifactId>javax.inject</artifactId>
        <version>${javax-inject.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.jms</groupId>
        <artifactId>javax.jms-api</artifactId>
        <version>${javax-jms.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sun.mail</groupId>
        <artifactId>javax.mail</artifactId>
        <version>${javax-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.mail</groupId>
        <artifactId>javax.mail-api</artifactId>
        <version>${javax-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.persistence</groupId>
        <artifactId>javax.persistence-api</artifactId>
        <version>${javax-persistence.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet.jsp</groupId>
        <artifactId>javax.servlet.jsp-api</artifactId>
        <version>${javax-servlet-jsp.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>${javax-servlet.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sun</groupId>
        <artifactId>jconsole</artifactId>
        <version>${jconsole.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jctools</groupId>
        <artifactId>jctools-core</artifactId>
        <version>${jctools.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sleepycat</groupId>
        <artifactId>je</artifactId>
        <version>${je.version}</version>
      </dependency>
      <dependency>
        <groupId>org.zeromq</groupId>
        <artifactId>jeromq</artifactId>
        <version>${jeromq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jmdns</groupId>
        <artifactId>jmdns</artifactId>
        <version>${jmdns.version}</version>
      </dependency>
      <dependency>
        <groupId>org.openjdk.jmh</groupId>
        <artifactId>jmh-core</artifactId>
        <version>${jmh.version}</version>
      </dependency>
      <dependency>
        <groupId>org.openjdk.jmh</groupId>
        <artifactId>jmh-generator-annprocess</artifactId>
        <version>${jmh.version}</version>
      </dependency>
      <dependency>
        <groupId>net.java.dev.jna</groupId>
        <artifactId>jna</artifactId>
        <version>${jna.version}</version>
      </dependency>
      <dependency>
        <groupId>net.javacrumbs.json-unit</groupId>
        <artifactId>json-unit</artifactId>
        <version>${json-unit.version}</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit.version}</version>
      </dependency>
      <dependency>
        <groupId>org.junit-pioneer</groupId>
        <artifactId>junit-pioneer</artifactId>
        <version>${junit-pioneer.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-clients</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.lightcouch</groupId>
        <artifactId>lightcouch</artifactId>
        <version>${lightcouch.version}</version>
      </dependency>
      <dependency>
        <groupId>log4j</groupId>
        <artifactId>log4j</artifactId>
        <version>${log4j.version}</version>
      </dependency>
      <dependency>
        <groupId>co.elastic.logging</groupId>
        <artifactId>log4j2-ecs-layout</artifactId>
        <version>${log4j2-ecs-layout.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>${logback.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>${logback.version}</version>
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>${logback.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>${logback.version}</version>
        <type>test-jar</type>
      </dependency>
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven-core</artifactId>
        <version>${maven.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-core</artifactId>
        <version>${mongodb.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-legacy</artifactId>
        <version>${mongodb.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-sync</artifactId>
        <version>${mongodb.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.felix</groupId>
        <artifactId>org.apache.felix.framework</artifactId>
        <version>${felix.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.tycho</groupId>
        <artifactId>org.eclipse.osgi</artifactId>
        <version>${org.eclipse.osgi.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.persistence</groupId>
        <artifactId>org.eclipse.persistence.jpa</artifactId>
        <version>${org.eclipse.persistence.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.eclipse.persistence</groupId>
            <artifactId>jakarta.persistence</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.osgi</groupId>
        <artifactId>org.osgi.core</artifactId>
        <version>${osgi.api.version}</version>
      </dependency>
      <dependency>
        <groupId>oro</groupId>
        <artifactId>oro</artifactId>
        <version>${oro.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ops4j.pax.exam</groupId>
        <artifactId>pax-exam</artifactId>
        <version>${pax-exam.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ops4j.pax.exam</groupId>
        <artifactId>pax-exam-container-native</artifactId>
        <version>${pax-exam.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ops4j.pax.exam</groupId>
        <artifactId>pax-exam-junit4</artifactId>
        <version>${pax-exam.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ops4j.pax.exam</groupId>
        <artifactId>pax-exam-link-assembly</artifactId>
        <version>${pax-exam.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ops4j.pax.exam</groupId>
        <artifactId>pax-exam-spi</artifactId>
        <version>${pax-exam.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.plexus</groupId>
        <artifactId>plexus-utils</artifactId>
        <version>${plexus-utils.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-ext</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot</artifactId>
        <version>${spring-boot.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <version>${spring-boot.version}</version>
        <exclusions>
          <exclusion>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>uk.org.webcompere</groupId>
        <artifactId>system-stubs-core</artifactId>
        <version>${system-stubs.version}</version>
      </dependency>
      <dependency>
        <groupId>uk.org.webcompere</groupId>
        <artifactId>system-stubs-jupiter</artifactId>
        <version>${system-stubs.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-juli</artifactId>
        <version>${tomcat-juli.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.velocity</groupId>
        <artifactId>velocity</artifactId>
        <version>${velocity.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.tomakehurst</groupId>
        <artifactId>wiremock-jre8</artifactId>
        <version>${wiremock.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.woodstox</groupId>
        <artifactId>woodstox-core</artifactId>
        <version>${woodstox.version}</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-core</artifactId>
        <version>${xmlunit.version}</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-matchers</artifactId>
        <version>${xmlunit.version}</version>
      </dependency>
      <dependency>
        <groupId>org.tukaani</groupId>
        <artifactId>xz</artifactId>
        <version>${xz.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.luben</groupId>
        <artifactId>zstd-jni</artifactId>
        <version>${zstd.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <dependencies>
    <dependency>
      <groupId>biz.aQute.bnd</groupId>
      <artifactId>biz.aQute.bnd.annotation</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>osgi.annotation</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>org.osgi</groupId>
      <artifactId>org.osgi.annotation.bundle</artifactId>
      <scope>provided</scope>
    </dependency>
    <dependency>
      <groupId>com.github.spotbugs</groupId>
      <artifactId>spotbugs-annotations</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>io.fabric8</groupId>
          <artifactId>docker-maven-plugin</artifactId>
          <version>${docker-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.ops4j.pax.exam</groupId>
          <artifactId>exam-maven-plugin</artifactId>
          <version>${exam-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>net.sourceforge.maven-taglib</groupId>
          <artifactId>maven-taglib-plugin</artifactId>
          <version>${maven-taglib-plugin.version}</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-enforcer-plugin</artifactId>
        <executions>
          <execution>
            <id>ban-logging-dependencies</id>
            <goals>
              <goal>enforce</goal>
            </goals>
            <configuration>
              <rules>
                <bannedDependencies>
                  <excludes>
                    <exclude>org.slf4j:jcl-over-slf4j</exclude>
                    <exclude>org.springframework:spring-jcl</exclude>
                    <exclude>org.slf4j:log4j-over-slf4j</exclude>
                    <exclude>ch.qos.reload4j:reload4j</exclude>
                    <exclude>org.slf4j:slf4j-log4j12</exclude>
                    <exclude>org.slf4j:slf4j-reload4j</exclude>
                    <exclude>org.ops4j.pax.logging:*</exclude>
                    <exclude>ch.qos.logback:*</exclude>
                  </excludes>
                </bannedDependencies>
              </rules>
            </configuration>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
