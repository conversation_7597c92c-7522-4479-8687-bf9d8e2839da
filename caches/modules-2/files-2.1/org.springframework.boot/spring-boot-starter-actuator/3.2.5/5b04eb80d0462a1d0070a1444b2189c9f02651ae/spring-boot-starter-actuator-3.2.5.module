{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-starter-actuator", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-actuator-autoconfigure", "version": {"requires": "3.2.5"}}, {"group": "io.micrometer", "module": "micrometer-observation", "version": {"requires": "1.12.5"}}, {"group": "io.micrometer", "module": "micrometer-jakarta9", "version": {"requires": "1.12.5"}}], "files": [{"name": "spring-boot-starter-actuator-3.2.5.jar", "url": "spring-boot-starter-actuator-3.2.5.jar", "size": 4805, "sha512": "79663b666142445b25c23da33a0cfb8e9c9be8276f4b9bdc5c06edfb82057931d41c4f07ce79f9fbd80a38693c198b90b0522907e2a437d5eae144ee880e53ea", "sha256": "16aac2d97456b196f5d8bb4ac05139931f373ef727b698e2fe29f37b003e72f8", "sha1": "14ac0ed95f3913fb90d3a49884e561c88ad2d844", "md5": "e50478310870ab7c8a44985fb1f1be52"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-actuator-autoconfigure", "version": {"requires": "3.2.5"}}, {"group": "io.micrometer", "module": "micrometer-observation", "version": {"requires": "1.12.5"}}, {"group": "io.micrometer", "module": "micrometer-jakarta9", "version": {"requires": "1.12.5"}}], "files": [{"name": "spring-boot-starter-actuator-3.2.5.jar", "url": "spring-boot-starter-actuator-3.2.5.jar", "size": 4805, "sha512": "79663b666142445b25c23da33a0cfb8e9c9be8276f4b9bdc5c06edfb82057931d41c4f07ce79f9fbd80a38693c198b90b0522907e2a437d5eae144ee880e53ea", "sha256": "16aac2d97456b196f5d8bb4ac05139931f373ef727b698e2fe29f37b003e72f8", "sha1": "14ac0ed95f3913fb90d3a49884e561c88ad2d844", "md5": "e50478310870ab7c8a44985fb1f1be52"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-starter-actuator-3.2.5.jar", "url": "spring-boot-starter-actuator-3.2.5.jar", "size": 4805, "sha512": "79663b666142445b25c23da33a0cfb8e9c9be8276f4b9bdc5c06edfb82057931d41c4f07ce79f9fbd80a38693c198b90b0522907e2a437d5eae144ee880e53ea", "sha256": "16aac2d97456b196f5d8bb4ac05139931f373ef727b698e2fe29f37b003e72f8", "sha1": "14ac0ed95f3913fb90d3a49884e561c88ad2d844", "md5": "e50478310870ab7c8a44985fb1f1be52"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-actuator-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-actuator-3.2.5.jar", "url": "spring-boot-starter-actuator-3.2.5.jar", "size": 4805, "sha512": "79663b666142445b25c23da33a0cfb8e9c9be8276f4b9bdc5c06edfb82057931d41c4f07ce79f9fbd80a38693c198b90b0522907e2a437d5eae144ee880e53ea", "sha256": "16aac2d97456b196f5d8bb4ac05139931f373ef727b698e2fe29f37b003e72f8", "sha1": "14ac0ed95f3913fb90d3a49884e561c88ad2d844", "md5": "e50478310870ab7c8a44985fb1f1be52"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-actuator-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-actuator-3.2.5-javadoc.jar", "url": "spring-boot-starter-actuator-3.2.5-javadoc.jar", "size": 4744, "sha512": "73b812c4e3f985d122dc279a9154e56eb1d7ea64705698eaeae049cf42f7513dc22c95b1052af5cea7ece02c4fc22c273010d9fddaa217f10e995c6d41aab4f1", "sha256": "e387efaeb5603a0aab96a7a532536ed60828243b41a9033ad99162fef47f29cb", "sha1": "2c83047d08bdac4d863fa5498cd9056ccf008d45", "md5": "f2c86bf7e8e9a5c5b0a1b76c02fcf36e"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-actuator-3.2.5-sources.jar", "url": "spring-boot-starter-actuator-3.2.5-sources.jar", "size": 4744, "sha512": "8422423b4f84ac2a2cc3de0d4c515a94c0c9dfc29ac14c62bf10390efef72c6c91a9ede9a219e04fdc8276ca144c19b54044fed6594e555447b27c28ec86de25", "sha256": "211516ec5344e2b671b6e6d3d55d0cda8c52b400e7134826a62f5e67c9970f43", "sha1": "5fbcefc0b361af5954e03211d81726a3ed28fa8f", "md5": "a9a8b817b8a08cc80813fcd396ae5feb"}]}]}