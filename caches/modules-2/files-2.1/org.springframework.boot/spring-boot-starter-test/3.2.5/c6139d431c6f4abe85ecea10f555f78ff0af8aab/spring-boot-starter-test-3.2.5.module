{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-starter-test", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-test", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-test-autoconfigure", "version": {"requires": "3.2.5"}}, {"group": "com.jayway.jsonpath", "module": "json-path", "version": {"requires": "2.9.0"}}, {"group": "jakarta.xml.bind", "module": "jakarta.xml.bind-api", "version": {"requires": "4.0.2"}}, {"group": "net.minidev", "module": "json-smart", "version": {"requires": "2.5.1"}}, {"group": "org.assertj", "module": "assertj-core", "version": {"requires": "3.24.2"}}, {"group": "org.awaitility", "module": "awaitility", "version": {"requires": "4.2.1"}}, {"group": "org.hamcrest", "module": "hamcrest", "version": {"requires": "2.2"}}, {"group": "org.junit.jupiter", "module": "junit-jupiter", "version": {"requires": "5.10.2"}}, {"group": "org.mockito", "module": "mockito-core", "version": {"requires": "5.7.0"}}, {"group": "org.mockito", "module": "mockito-junit-jupiter", "version": {"requires": "5.7.0"}}, {"group": "org.skyscreamer", "module": "<PERSON><PERSON><PERSON><PERSON>", "version": {"requires": "1.5.1"}}, {"group": "org.springframework", "module": "spring-core", "version": {"requires": "6.1.6"}}, {"group": "org.springframework", "module": "spring-test", "version": {"requires": "6.1.6"}}, {"group": "org.xmlunit", "module": "xmlunit-core", "version": {"requires": "2.9.1"}, "excludes": [{"group": "javax.xml.bind", "module": "jaxb-api"}]}], "files": [{"name": "spring-boot-starter-test-3.2.5.jar", "url": "spring-boot-starter-test-3.2.5.jar", "size": 4791, "sha512": "d0c71f267c2622d4c64ce8c3b0e4271cecaf6867b158e97b1540f806033979e41b86988a4d88fc81a8c5e361eefbc009dd56698b8bf8d0ce2d6cfe5f675c6923", "sha256": "2060b6be298adc25bbcecb302d5fcf8c2d12b92557d6ce347615230139cd0057", "sha1": "c2f2d1d01ef908d04bfa367ec48d13ab15f2b16f", "md5": "ff91766d6cd33ac4bad433e6cd9cf9a2"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-test", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-test-autoconfigure", "version": {"requires": "3.2.5"}}, {"group": "com.jayway.jsonpath", "module": "json-path", "version": {"requires": "2.9.0"}}, {"group": "jakarta.xml.bind", "module": "jakarta.xml.bind-api", "version": {"requires": "4.0.2"}}, {"group": "net.minidev", "module": "json-smart", "version": {"requires": "2.5.1"}}, {"group": "org.assertj", "module": "assertj-core", "version": {"requires": "3.24.2"}}, {"group": "org.awaitility", "module": "awaitility", "version": {"requires": "4.2.1"}}, {"group": "org.hamcrest", "module": "hamcrest", "version": {"requires": "2.2"}}, {"group": "org.junit.jupiter", "module": "junit-jupiter", "version": {"requires": "5.10.2"}}, {"group": "org.mockito", "module": "mockito-core", "version": {"requires": "5.7.0"}}, {"group": "org.mockito", "module": "mockito-junit-jupiter", "version": {"requires": "5.7.0"}}, {"group": "org.skyscreamer", "module": "<PERSON><PERSON><PERSON><PERSON>", "version": {"requires": "1.5.1"}}, {"group": "org.springframework", "module": "spring-core", "version": {"requires": "6.1.6"}}, {"group": "org.springframework", "module": "spring-test", "version": {"requires": "6.1.6"}}, {"group": "org.xmlunit", "module": "xmlunit-core", "version": {"requires": "2.9.1"}, "excludes": [{"group": "javax.xml.bind", "module": "jaxb-api"}]}], "files": [{"name": "spring-boot-starter-test-3.2.5.jar", "url": "spring-boot-starter-test-3.2.5.jar", "size": 4791, "sha512": "d0c71f267c2622d4c64ce8c3b0e4271cecaf6867b158e97b1540f806033979e41b86988a4d88fc81a8c5e361eefbc009dd56698b8bf8d0ce2d6cfe5f675c6923", "sha256": "2060b6be298adc25bbcecb302d5fcf8c2d12b92557d6ce347615230139cd0057", "sha1": "c2f2d1d01ef908d04bfa367ec48d13ab15f2b16f", "md5": "ff91766d6cd33ac4bad433e6cd9cf9a2"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-starter-test-3.2.5.jar", "url": "spring-boot-starter-test-3.2.5.jar", "size": 4791, "sha512": "d0c71f267c2622d4c64ce8c3b0e4271cecaf6867b158e97b1540f806033979e41b86988a4d88fc81a8c5e361eefbc009dd56698b8bf8d0ce2d6cfe5f675c6923", "sha256": "2060b6be298adc25bbcecb302d5fcf8c2d12b92557d6ce347615230139cd0057", "sha1": "c2f2d1d01ef908d04bfa367ec48d13ab15f2b16f", "md5": "ff91766d6cd33ac4bad433e6cd9cf9a2"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-test-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-test-3.2.5.jar", "url": "spring-boot-starter-test-3.2.5.jar", "size": 4791, "sha512": "d0c71f267c2622d4c64ce8c3b0e4271cecaf6867b158e97b1540f806033979e41b86988a4d88fc81a8c5e361eefbc009dd56698b8bf8d0ce2d6cfe5f675c6923", "sha256": "2060b6be298adc25bbcecb302d5fcf8c2d12b92557d6ce347615230139cd0057", "sha1": "c2f2d1d01ef908d04bfa367ec48d13ab15f2b16f", "md5": "ff91766d6cd33ac4bad433e6cd9cf9a2"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-test-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-test-3.2.5-javadoc.jar", "url": "spring-boot-starter-test-3.2.5-javadoc.jar", "size": 4741, "sha512": "a4f48d308ac3c55e9de74ec7eae2a16e6f225a3a2c1e9f8c723eaa5f75ecd01152a9ac1e07ae0308178a47634d811170f27ae1ae16c02c1775134b0c69d8a63b", "sha256": "79270972444d865408d0652d44ed0ba3b05efd90599d280b8b5357c3e1d89193", "sha1": "f0892a760a65752f998997ddcb0b8622742b35fb", "md5": "d0f4dcd7ef858c893ead668156e12448"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-test-3.2.5-sources.jar", "url": "spring-boot-starter-test-3.2.5-sources.jar", "size": 4740, "sha512": "470fa7397a2a2e8159028bc2f8b8253bd217a7d264453c441b7e8eca5a3512e519c31463fc8a7ebc25e2dd0c7b950fdec2272dd40e15c60e0cff3a6a7e81274a", "sha256": "f8afd39d4b60aa0cb41fbc0d120fa9096662fb584800784e21edd54978159cdf", "sha1": "13a7bd7102185abe8e20bba9f705acb0c3177f99", "md5": "421e8564976d8143c0d680f4d6527d49"}]}]}