{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-autoconfigure", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot", "version": {"requires": "3.2.5"}}], "files": [{"name": "spring-boot-autoconfigure-3.2.5.jar", "url": "spring-boot-autoconfigure-3.2.5.jar", "size": 1932178, "sha512": "320897a3335dac3bc7481a2f45f471f21f2b18d849e9832450b7f38de3ba80ffcb50e6da70bb8f9bb118d0dd42d0f8a428b16e234c4f82f0e7a19553757ad2a9", "sha256": "f2c280b60a726e9b9231cf8c80bb0878c8912a946ccf6d5f8804e30a0de741ac", "sha1": "6385a2c00a03edb896b2833e4bdee2ae53cd69b8", "md5": "a20e638fbe1e0980165e90088a7a4b7a"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot", "version": {"requires": "3.2.5"}}], "files": [{"name": "spring-boot-autoconfigure-3.2.5.jar", "url": "spring-boot-autoconfigure-3.2.5.jar", "size": 1932178, "sha512": "320897a3335dac3bc7481a2f45f471f21f2b18d849e9832450b7f38de3ba80ffcb50e6da70bb8f9bb118d0dd42d0f8a428b16e234c4f82f0e7a19553757ad2a9", "sha256": "f2c280b60a726e9b9231cf8c80bb0878c8912a946ccf6d5f8804e30a0de741ac", "sha1": "6385a2c00a03edb896b2833e4bdee2ae53cd69b8", "md5": "a20e638fbe1e0980165e90088a7a4b7a"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-autoconfigure-3.2.5.jar", "url": "spring-boot-autoconfigure-3.2.5.jar", "size": 1932178, "sha512": "320897a3335dac3bc7481a2f45f471f21f2b18d849e9832450b7f38de3ba80ffcb50e6da70bb8f9bb118d0dd42d0f8a428b16e234c4f82f0e7a19553757ad2a9", "sha256": "f2c280b60a726e9b9231cf8c80bb0878c8912a946ccf6d5f8804e30a0de741ac", "sha1": "6385a2c00a03edb896b2833e4bdee2ae53cd69b8", "md5": "a20e638fbe1e0980165e90088a7a4b7a"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-autoconfigure-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-autoconfigure-3.2.5.jar", "url": "spring-boot-autoconfigure-3.2.5.jar", "size": 1932178, "sha512": "320897a3335dac3bc7481a2f45f471f21f2b18d849e9832450b7f38de3ba80ffcb50e6da70bb8f9bb118d0dd42d0f8a428b16e234c4f82f0e7a19553757ad2a9", "sha256": "f2c280b60a726e9b9231cf8c80bb0878c8912a946ccf6d5f8804e30a0de741ac", "sha1": "6385a2c00a03edb896b2833e4bdee2ae53cd69b8", "md5": "a20e638fbe1e0980165e90088a7a4b7a"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-autoconfigure-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-autoconfigure-3.2.5-javadoc.jar", "url": "spring-boot-autoconfigure-3.2.5-javadoc.jar", "size": 3019094, "sha512": "6bedebfd98192ab157def77daf84e2fd8fd560068950debe92509d170b9a2ef65bfff09ef8fdb3e1a3317e051f940454eb0964479b3b622964950bb9ab14a35a", "sha256": "a09e8b05bff1d57dcb3abe607b7bada089399d6e67d41d6c7b35e4a0099fa9e7", "sha1": "4dd9eb98d58fc3ec4b16fcaf7dc9e95cd040fa82", "md5": "d25275500fce1fbb1325eb39d49e94a3"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-autoconfigure-3.2.5-sources.jar", "url": "spring-boot-autoconfigure-3.2.5-sources.jar", "size": 1121089, "sha512": "fd7f756b04a8caca45ca41e88608b68319cd0ec7253d9ee9bfe142e6bf469c15518f530f75d41c072e689268b9f0bf78ba67b394c48713893e6a998b030b2137", "sha256": "d51ce126ee8469108f4ce204d24a8f35c9752228a11ff42ff4c9ed53b656c1b6", "sha1": "c1d688a3c1c5c11b696790a6aafcc98aa68e53d7", "md5": "b83f00d478544fbc2de465984aa64351"}]}]}