{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-loader-tools", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.apache.commons", "module": "commons-compress", "version": {"requires": "1.21"}}, {"group": "org.springframework", "module": "spring-core", "version": {"requires": "6.0.10"}}], "files": [{"name": "spring-boot-loader-tools-3.2.5.jar", "url": "spring-boot-loader-tools-3.2.5.jar", "size": 435310, "sha512": "7d03d2964d5af398f497591127ed1838402cc99b503836c15d1fc8c0f801a39c07357d5e785aaf2d130876dbe817ac1db91d9eaa7c167ef3e59adfc36e0dd898", "sha256": "9f38dbf0ea3fba6dc3cc6d8a59680e29e1da0c3516c73e03c01f03a18bb68df4", "sha1": "16b0edd12c67210736b12f7cd3eb98f51620fe10", "md5": "9d518481712dfab465193a52fd64fd7b"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.apache.commons", "module": "commons-compress", "version": {"requires": "1.21"}}, {"group": "org.springframework", "module": "spring-core", "version": {"requires": "6.0.10"}}], "files": [{"name": "spring-boot-loader-tools-3.2.5.jar", "url": "spring-boot-loader-tools-3.2.5.jar", "size": 435310, "sha512": "7d03d2964d5af398f497591127ed1838402cc99b503836c15d1fc8c0f801a39c07357d5e785aaf2d130876dbe817ac1db91d9eaa7c167ef3e59adfc36e0dd898", "sha256": "9f38dbf0ea3fba6dc3cc6d8a59680e29e1da0c3516c73e03c01f03a18bb68df4", "sha1": "16b0edd12c67210736b12f7cd3eb98f51620fe10", "md5": "9d518481712dfab465193a52fd64fd7b"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-loader-tools-3.2.5-javadoc.jar", "url": "spring-boot-loader-tools-3.2.5-javadoc.jar", "size": 251104, "sha512": "74d06d01282c79dbe767947cc1a4777d954b0dc4ae64bae4397657617e966e0289c4c5c3cce6bcf5a560c5c427e6f6510a2c4cda35c128083e752291c5ba1fb8", "sha256": "12f03bebd3a2cb2c33c973f73cce8bfb8bb80f8ce4f78ea9e13d343496df678a", "sha1": "4a4476e23ddf22cc70fd050a698d0eb29d376316", "md5": "7746f2afd7285a37b2fd7d0063fd1bca"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-loader-tools-3.2.5-sources.jar", "url": "spring-boot-loader-tools-3.2.5-sources.jar", "size": 74899, "sha512": "baeafa46a7bf759d29690e752fc2b08f62ce903e648eb416bcba2a42e8db2975b655b131bfe059fa9f01647800a47206e598c1e3ede921de0ce8929e3bdb3462", "sha256": "ad5847a392801be9446e508be81d265ecf04092a84cd0e3d1a1f2ba287ce042b", "sha1": "0d1c9da4d0ee3ebf98bcae16e6c68ed61f81b22f", "md5": "40dd842605423cc08f5a1cc01bfc62ae"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-loader-tools-3.2.5.jar", "url": "spring-boot-loader-tools-3.2.5.jar", "size": 435310, "sha512": "7d03d2964d5af398f497591127ed1838402cc99b503836c15d1fc8c0f801a39c07357d5e785aaf2d130876dbe817ac1db91d9eaa7c167ef3e59adfc36e0dd898", "sha256": "9f38dbf0ea3fba6dc3cc6d8a59680e29e1da0c3516c73e03c01f03a18bb68df4", "sha1": "16b0edd12c67210736b12f7cd3eb98f51620fe10", "md5": "9d518481712dfab465193a52fd64fd7b"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-loader-tools-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-loader-tools-3.2.5.jar", "url": "spring-boot-loader-tools-3.2.5.jar", "size": 435310, "sha512": "7d03d2964d5af398f497591127ed1838402cc99b503836c15d1fc8c0f801a39c07357d5e785aaf2d130876dbe817ac1db91d9eaa7c167ef3e59adfc36e0dd898", "sha256": "9f38dbf0ea3fba6dc3cc6d8a59680e29e1da0c3516c73e03c01f03a18bb68df4", "sha1": "16b0edd12c67210736b12f7cd3eb98f51620fe10", "md5": "9d518481712dfab465193a52fd64fd7b"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-loader-tools-maven-optional", "version": "3.2.5"}]}]}