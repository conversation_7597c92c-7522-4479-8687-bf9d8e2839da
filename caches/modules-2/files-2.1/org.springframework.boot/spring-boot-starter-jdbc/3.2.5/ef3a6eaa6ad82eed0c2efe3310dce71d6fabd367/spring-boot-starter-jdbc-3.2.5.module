{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-starter-jdbc", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "com.zaxxer", "module": "HikariCP", "version": {"requires": "5.0.1"}}, {"group": "org.springframework", "module": "spring-jdbc", "version": {"requires": "6.1.6"}}], "files": [{"name": "spring-boot-starter-jdbc-3.2.5.jar", "url": "spring-boot-starter-jdbc-3.2.5.jar", "size": 4770, "sha512": "f516d429de176915f50fe3e09d350334ac81a682f1a77bd7c57a96af36759c13867f8a19111d07c524cbc1a23a061d224cd9dd6cca760a7b3fee51bfa5f5139a", "sha256": "6324adb743d6928be517d6c6484b69988f30185eff3c7423bd4799ab3c53244c", "sha1": "2fc156645b02bef43dcd4e697ae6f4ba9388a978", "md5": "164d70fd18296fdf54b52e380ced69db"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "com.zaxxer", "module": "HikariCP", "version": {"requires": "5.0.1"}}, {"group": "org.springframework", "module": "spring-jdbc", "version": {"requires": "6.1.6"}}], "files": [{"name": "spring-boot-starter-jdbc-3.2.5.jar", "url": "spring-boot-starter-jdbc-3.2.5.jar", "size": 4770, "sha512": "f516d429de176915f50fe3e09d350334ac81a682f1a77bd7c57a96af36759c13867f8a19111d07c524cbc1a23a061d224cd9dd6cca760a7b3fee51bfa5f5139a", "sha256": "6324adb743d6928be517d6c6484b69988f30185eff3c7423bd4799ab3c53244c", "sha1": "2fc156645b02bef43dcd4e697ae6f4ba9388a978", "md5": "164d70fd18296fdf54b52e380ced69db"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-starter-jdbc-3.2.5.jar", "url": "spring-boot-starter-jdbc-3.2.5.jar", "size": 4770, "sha512": "f516d429de176915f50fe3e09d350334ac81a682f1a77bd7c57a96af36759c13867f8a19111d07c524cbc1a23a061d224cd9dd6cca760a7b3fee51bfa5f5139a", "sha256": "6324adb743d6928be517d6c6484b69988f30185eff3c7423bd4799ab3c53244c", "sha1": "2fc156645b02bef43dcd4e697ae6f4ba9388a978", "md5": "164d70fd18296fdf54b52e380ced69db"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-jdbc-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-jdbc-3.2.5.jar", "url": "spring-boot-starter-jdbc-3.2.5.jar", "size": 4770, "sha512": "f516d429de176915f50fe3e09d350334ac81a682f1a77bd7c57a96af36759c13867f8a19111d07c524cbc1a23a061d224cd9dd6cca760a7b3fee51bfa5f5139a", "sha256": "6324adb743d6928be517d6c6484b69988f30185eff3c7423bd4799ab3c53244c", "sha1": "2fc156645b02bef43dcd4e697ae6f4ba9388a978", "md5": "164d70fd18296fdf54b52e380ced69db"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-jdbc-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-jdbc-3.2.5-javadoc.jar", "url": "spring-boot-starter-jdbc-3.2.5-javadoc.jar", "size": 4743, "sha512": "ebe661edcbeade9f5a39cee3f6d530e7b117b89cdfbecc2278edb884146ae8bb654e785b17cad1179cbfcb06a13db2df2b9d615734fee407e1536e0053a0f54f", "sha256": "01eef4d9a078df6d673b1655bdc6d84f2354609167897035963f331d6881d958", "sha1": "1b1306fc0c54b376175638eff4c8abd61eb93592", "md5": "cb3d2a9661bb1353ddb221d807d501ea"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-jdbc-3.2.5-sources.jar", "url": "spring-boot-starter-jdbc-3.2.5-sources.jar", "size": 4742, "sha512": "daa4f49b01674d56fd6b8af1c6842cbbf6712fbba27641bc0cb7915bf052a16970430af3dab618da9fc730bab9b058725e89222fd76555e53d0424f22c846f9a", "sha256": "defb57b83c380b04658d36f6700352c38388c5e616549a91e64c2166dcf27e5b", "sha1": "c0c81c1f3a600b812b76ccd14e75fc14d2bfc8d8", "md5": "9832d8a0b8a2468fc96fa2f78877d005"}]}]}