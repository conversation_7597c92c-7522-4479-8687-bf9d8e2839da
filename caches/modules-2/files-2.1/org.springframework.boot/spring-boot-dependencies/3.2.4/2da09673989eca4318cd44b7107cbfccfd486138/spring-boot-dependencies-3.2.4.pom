<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.springframework.boot</groupId>
  <artifactId>spring-boot-dependencies</artifactId>
  <version>3.2.4</version>
  <packaging>pom</packaging>
  <name>spring-boot-dependencies</name>
  <description>Spring Boot Dependencies</description>
  <url>https://spring.io/projects/spring-boot</url>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <name>Spring</name>
      <email><EMAIL></email>
      <organization>VMware, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
    </developer>
  </developers>
  <scm>
    <url>https://github.com/spring-projects/spring-boot</url>
  </scm>
  <properties>
    <activemq.version>5.18.3</activemq.version>
    <angus-mail.version>2.0.3</angus-mail.version>
    <artemis.version>2.31.2</artemis.version>
    <aspectj.version>1.9.21</aspectj.version>
    <assertj.version>3.24.2</assertj.version>
    <awaitility.version>4.2.0</awaitility.version>
    <brave.version>5.16.0</brave.version>
    <build-helper-maven-plugin.version>3.4.0</build-helper-maven-plugin.version>
    <byte-buddy.version>1.14.12</byte-buddy.version>
    <cache2k.version>2.6.1.Final</cache2k.version>
    <caffeine.version>3.1.8</caffeine.version>
    <cassandra-driver.version>4.17.0</cassandra-driver.version>
    <classmate.version>1.6.0</classmate.version>
    <commons-codec.version>1.16.1</commons-codec.version>
    <commons-dbcp2.version>2.10.0</commons-dbcp2.version>
    <commons-lang3.version>3.13.0</commons-lang3.version>
    <commons-pool.version>1.6</commons-pool.version>
    <commons-pool2.version>2.12.0</commons-pool2.version>
    <couchbase-client.version>3.4.11</couchbase-client.version>
    <crac.version>1.4.0</crac.version>
    <db2-jdbc.version>11.5.9.0</db2-jdbc.version>
    <dependency-management-plugin.version>1.1.4</dependency-management-plugin.version>
    <derby.version>10.16.1.1</derby.version>
    <dropwizard-metrics.version>4.2.25</dropwizard-metrics.version>
    <ehcache3.version>3.10.8</ehcache3.version>
    <elasticsearch-client.version>8.10.4</elasticsearch-client.version>
    <flyway.version>9.22.3</flyway.version>
    <freemarker.version>2.3.32</freemarker.version>
    <git-commit-id-maven-plugin.version>6.0.0</git-commit-id-maven-plugin.version>
    <glassfish-jaxb.version>4.0.5</glassfish-jaxb.version>
    <glassfish-jstl.version>3.0.1</glassfish-jstl.version>
    <graphql-java.version>21.4</graphql-java.version>
    <groovy.version>4.0.20</groovy.version>
    <gson.version>2.10.1</gson.version>
    <h2.version>2.2.224</h2.version>
    <hamcrest.version>2.2</hamcrest.version>
    <hazelcast.version>5.3.6</hazelcast.version>
    <hibernate.version>6.4.4.Final</hibernate.version>
    <hibernate-validator.version>8.0.1.Final</hibernate-validator.version>
    <hikaricp.version>5.0.1</hikaricp.version>
    <hsqldb.version>2.7.2</hsqldb.version>
    <htmlunit.version>2.70.0</htmlunit.version>
    <httpasyncclient.version>4.1.5</httpasyncclient.version>
    <httpclient5.version>5.2.3</httpclient5.version>
    <httpcore.version>4.4.16</httpcore.version>
    <httpcore5.version>5.2.4</httpcore5.version>
    <infinispan.version>14.0.27.Final</infinispan.version>
    <influxdb-java.version>2.23</influxdb-java.version>
    <jackson-bom.version>2.15.4</jackson-bom.version>
    <jakarta-activation.version>2.1.3</jakarta-activation.version>
    <jakarta-annotation.version>2.1.1</jakarta-annotation.version>
    <jakarta-jms.version>3.1.0</jakarta-jms.version>
    <jakarta-json.version>2.1.3</jakarta-json.version>
    <jakarta-json-bind.version>3.0.0</jakarta-json-bind.version>
    <jakarta-mail.version>2.1.3</jakarta-mail.version>
    <jakarta-management.version>1.1.4</jakarta-management.version>
    <jakarta-persistence.version>3.1.0</jakarta-persistence.version>
    <jakarta-servlet.version>6.0.0</jakarta-servlet.version>
    <jakarta-servlet-jsp-jstl.version>3.0.0</jakarta-servlet-jsp-jstl.version>
    <jakarta-transaction.version>2.0.1</jakarta-transaction.version>
    <jakarta-validation.version>3.0.2</jakarta-validation.version>
    <jakarta-websocket.version>2.1.1</jakarta-websocket.version>
    <jakarta-ws-rs.version>3.1.0</jakarta-ws-rs.version>
    <jakarta-xml-bind.version>4.0.2</jakarta-xml-bind.version>
    <jakarta-xml-soap.version>3.0.1</jakarta-xml-soap.version>
    <jakarta-xml-ws.version>4.0.1</jakarta-xml-ws.version>
    <janino.version>3.1.12</janino.version>
    <javax-cache.version>1.1.1</javax-cache.version>
    <javax-money.version>1.1</javax-money.version>
    <jaxen.version>2.0.0</jaxen.version>
    <jaybird.version>5.0.4.java11</jaybird.version>
    <jboss-logging.version>3.5.3.Final</jboss-logging.version>
    <jdom2.version>2.0.6.1</jdom2.version>
    <jedis.version>5.0.2</jedis.version>
    <jersey.version>3.1.5</jersey.version>
    <jetty-reactive-httpclient.version>4.0.3</jetty-reactive-httpclient.version>
    <jetty.version>12.0.7</jetty.version>
    <jmustache.version>1.15</jmustache.version>
    <jooq.version>3.18.13</jooq.version>
    <json-path.version>2.9.0</json-path.version>
    <json-smart.version>2.5.0</json-smart.version>
    <jsonassert.version>1.5.1</jsonassert.version>
    <jtds.version>1.3.1</jtds.version>
    <junit.version>4.13.2</junit.version>
    <junit-jupiter.version>5.10.2</junit-jupiter.version>
    <kafka.version>3.6.1</kafka.version>
    <kotlin.version>1.9.23</kotlin.version>
    <kotlin-coroutines.version>1.7.3</kotlin-coroutines.version>
    <kotlin-serialization.version>1.6.3</kotlin-serialization.version>
    <lettuce.version>6.3.2.RELEASE</lettuce.version>
    <liquibase.version>4.24.0</liquibase.version>
    <log4j2.version>2.21.1</log4j2.version>
    <logback.version>1.4.14</logback.version>
    <lombok.version>1.18.30</lombok.version>
    <mariadb.version>3.3.3</mariadb.version>
    <maven-antrun-plugin.version>3.1.0</maven-antrun-plugin.version>
    <maven-assembly-plugin.version>3.6.0</maven-assembly-plugin.version>
    <maven-clean-plugin.version>3.3.2</maven-clean-plugin.version>
    <maven-compiler-plugin.version>3.11.0</maven-compiler-plugin.version>
    <maven-dependency-plugin.version>3.6.1</maven-dependency-plugin.version>
    <maven-deploy-plugin.version>3.1.1</maven-deploy-plugin.version>
    <maven-enforcer-plugin.version>3.4.1</maven-enforcer-plugin.version>
    <maven-failsafe-plugin.version>3.1.2</maven-failsafe-plugin.version>
    <maven-help-plugin.version>3.4.0</maven-help-plugin.version>
    <maven-install-plugin.version>3.1.1</maven-install-plugin.version>
    <maven-invoker-plugin.version>3.6.0</maven-invoker-plugin.version>
    <maven-jar-plugin.version>3.3.0</maven-jar-plugin.version>
    <maven-javadoc-plugin.version>3.6.3</maven-javadoc-plugin.version>
    <maven-resources-plugin.version>3.3.1</maven-resources-plugin.version>
    <maven-shade-plugin.version>3.5.2</maven-shade-plugin.version>
    <maven-source-plugin.version>3.3.0</maven-source-plugin.version>
    <maven-surefire-plugin.version>3.1.2</maven-surefire-plugin.version>
    <maven-war-plugin.version>3.4.0</maven-war-plugin.version>
    <micrometer.version>1.12.4</micrometer.version>
    <micrometer-tracing.version>1.2.4</micrometer-tracing.version>
    <mockito.version>5.7.0</mockito.version>
    <mongodb.version>4.11.1</mongodb.version>
    <mssql-jdbc.version>12.4.2.jre11</mssql-jdbc.version>
    <mysql.version>8.3.0</mysql.version>
    <native-build-tools-plugin.version>0.9.28</native-build-tools-plugin.version>
    <nekohtml.version>1.9.22</nekohtml.version>
    <neo4j-java-driver.version>5.18.0</neo4j-java-driver.version>
    <netty.version>4.1.107.Final</netty.version>
    <okhttp.version>4.12.0</okhttp.version>
    <opentelemetry.version>1.31.0</opentelemetry.version>
    <oracle-database.version>21.9.0.0</oracle-database.version>
    <oracle-r2dbc.version>1.1.1</oracle-r2dbc.version>
    <pooled-jms.version>3.1.5</pooled-jms.version>
    <postgresql.version>42.6.2</postgresql.version>
    <prometheus-client.version>0.16.0</prometheus-client.version>
    <pulsar.version>3.1.3</pulsar.version>
    <pulsar-reactive.version>0.5.3</pulsar-reactive.version>
    <quartz.version>2.3.2</quartz.version>
    <querydsl.version>5.0.0</querydsl.version>
    <r2dbc-h2.version>1.0.0.RELEASE</r2dbc-h2.version>
    <r2dbc-mariadb.version>1.1.4</r2dbc-mariadb.version>
    <r2dbc-mssql.version>1.0.2.RELEASE</r2dbc-mssql.version>
    <r2dbc-mysql.version>1.0.6</r2dbc-mysql.version>
    <r2dbc-pool.version>1.0.1.RELEASE</r2dbc-pool.version>
    <r2dbc-postgresql.version>1.0.4.RELEASE</r2dbc-postgresql.version>
    <r2dbc-proxy.version>1.1.4.RELEASE</r2dbc-proxy.version>
    <r2dbc-spi.version>1.0.0.RELEASE</r2dbc-spi.version>
    <rabbit-amqp-client.version>5.19.0</rabbit-amqp-client.version>
    <rabbit-stream-client.version>0.14.0</rabbit-stream-client.version>
    <reactive-streams.version>1.0.4</reactive-streams.version>
    <reactor-bom.version>2023.0.4</reactor-bom.version>
    <rest-assured.version>5.3.2</rest-assured.version>
    <rsocket.version>1.1.3</rsocket.version>
    <rxjava3.version>3.1.8</rxjava3.version>
    <saaj-impl.version>3.0.3</saaj-impl.version>
    <selenium.version>4.14.1</selenium.version>
    <selenium-htmlunit.version>4.13.0</selenium-htmlunit.version>
    <sendgrid.version>4.9.3</sendgrid.version>
    <slf4j.version>2.0.12</slf4j.version>
    <snakeyaml.version>2.2</snakeyaml.version>
    <spring-amqp.version>3.1.3</spring-amqp.version>
    <spring-authorization-server.version>1.2.3</spring-authorization-server.version>
    <spring-batch.version>5.1.1</spring-batch.version>
    <spring-data-bom.version>2023.1.4</spring-data-bom.version>
    <spring-framework.version>6.1.5</spring-framework.version>
    <spring-graphql.version>1.2.5</spring-graphql.version>
    <spring-hateoas.version>2.2.1</spring-hateoas.version>
    <spring-integration.version>6.2.3</spring-integration.version>
    <spring-kafka.version>3.1.3</spring-kafka.version>
    <spring-ldap.version>3.2.2</spring-ldap.version>
    <spring-pulsar.version>1.0.4</spring-pulsar.version>
    <spring-restdocs.version>3.0.1</spring-restdocs.version>
    <spring-retry.version>2.0.5</spring-retry.version>
    <spring-security.version>6.2.3</spring-security.version>
    <spring-session.version>3.2.2</spring-session.version>
    <spring-ws.version>4.0.10</spring-ws.version>
    <sqlite-jdbc.version>3.43.2.0</sqlite-jdbc.version>
    <testcontainers.version>1.19.7</testcontainers.version>
    <thymeleaf.version>3.1.2.RELEASE</thymeleaf.version>
    <thymeleaf-extras-data-attribute.version>2.0.1</thymeleaf-extras-data-attribute.version>
    <thymeleaf-extras-springsecurity.version>3.1.2.RELEASE</thymeleaf-extras-springsecurity.version>
    <thymeleaf-layout-dialect.version>3.3.0</thymeleaf-layout-dialect.version>
    <tomcat.version>10.1.19</tomcat.version>
    <unboundid-ldapsdk.version>6.0.11</unboundid-ldapsdk.version>
    <undertow.version>2.3.12.Final</undertow.version>
    <versions-maven-plugin.version>2.16.2</versions-maven-plugin.version>
    <webjars-locator-core.version>0.55</webjars-locator-core.version>
    <wsdl4j.version>1.6.3</wsdl4j.version>
    <xml-maven-plugin.version>1.1.0</xml-maven-plugin.version>
    <xmlunit2.version>2.9.1</xmlunit2.version>
    <yasson.version>3.0.3</yasson.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-amqp</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-blueprint</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-broker</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-client</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-client-jakarta</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-console</artifactId>
        <version>${activemq.version}</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-http</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jaas</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jdbc-store</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-jms-pool</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-kahadb-store</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-karaf</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-log4j-appender</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-mqtt</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-openwire-generator</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-openwire-legacy</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-osgi</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-partition</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-pool</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-ra</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-run</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-runtime-config</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-shiro</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-spring</artifactId>
        <version>${activemq.version}</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-stomp</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-web</artifactId>
        <version>${activemq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>angus-core</artifactId>
        <version>${angus-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>angus-mail</artifactId>
        <version>${angus-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>dsn</artifactId>
        <version>${angus-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>gimap</artifactId>
        <version>${angus-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>imap</artifactId>
        <version>${angus-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>jakarta.mail</artifactId>
        <version>${angus-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>logging-mailhandler</artifactId>
        <version>${angus-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>pop3</artifactId>
        <version>${angus-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.angus</groupId>
        <artifactId>smtp</artifactId>
        <version>${angus-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-amqp-protocol</artifactId>
        <version>${artemis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-commons</artifactId>
        <version>${artemis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-core-client</artifactId>
        <version>${artemis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jakarta-client</artifactId>
        <version>${artemis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jakarta-server</artifactId>
        <version>${artemis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jakarta-service-extensions</artifactId>
        <version>${artemis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-jdbc-store</artifactId>
        <version>${artemis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-journal</artifactId>
        <version>${artemis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-quorum-api</artifactId>
        <version>${artemis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-selector</artifactId>
        <version>${artemis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-server</artifactId>
        <version>${artemis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>artemis-service-extensions</artifactId>
        <version>${artemis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjrt</artifactId>
        <version>${aspectj.version}</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjtools</artifactId>
        <version>${aspectj.version}</version>
      </dependency>
      <dependency>
        <groupId>org.aspectj</groupId>
        <artifactId>aspectjweaver</artifactId>
        <version>${aspectj.version}</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility</artifactId>
        <version>${awaitility.version}</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility-groovy</artifactId>
        <version>${awaitility.version}</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility-kotlin</artifactId>
        <version>${awaitility.version}</version>
      </dependency>
      <dependency>
        <groupId>org.awaitility</groupId>
        <artifactId>awaitility-scala</artifactId>
        <version>${awaitility.version}</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy</artifactId>
        <version>${byte-buddy.version}</version>
      </dependency>
      <dependency>
        <groupId>net.bytebuddy</groupId>
        <artifactId>byte-buddy-agent</artifactId>
        <version>${byte-buddy.version}</version>
      </dependency>
      <dependency>
        <groupId>org.cache2k</groupId>
        <artifactId>cache2k-api</artifactId>
        <version>${cache2k.version}</version>
      </dependency>
      <dependency>
        <groupId>org.cache2k</groupId>
        <artifactId>cache2k-config</artifactId>
        <version>${cache2k.version}</version>
      </dependency>
      <dependency>
        <groupId>org.cache2k</groupId>
        <artifactId>cache2k-core</artifactId>
        <version>${cache2k.version}</version>
      </dependency>
      <dependency>
        <groupId>org.cache2k</groupId>
        <artifactId>cache2k-jcache</artifactId>
        <version>${cache2k.version}</version>
      </dependency>
      <dependency>
        <groupId>org.cache2k</groupId>
        <artifactId>cache2k-micrometer</artifactId>
        <version>${cache2k.version}</version>
      </dependency>
      <dependency>
        <groupId>org.cache2k</groupId>
        <artifactId>cache2k-spring</artifactId>
        <version>${cache2k.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>caffeine</artifactId>
        <version>${caffeine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>guava</artifactId>
        <version>${caffeine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>jcache</artifactId>
        <version>${caffeine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.ben-manes.caffeine</groupId>
        <artifactId>simulator</artifactId>
        <version>${caffeine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-core</artifactId>
        <version>${cassandra-driver.version}</version>
      </dependency>
      <dependency>
        <groupId>com.fasterxml</groupId>
        <artifactId>classmate</artifactId>
        <version>${classmate.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>${commons-codec.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-dbcp2</artifactId>
        <version>${commons-dbcp2.version}</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>${commons-lang3.version}</version>
      </dependency>
      <dependency>
        <groupId>commons-pool</groupId>
        <artifactId>commons-pool</artifactId>
        <version>${commons-pool.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-pool2</artifactId>
        <version>${commons-pool2.version}</version>
      </dependency>
      <dependency>
        <groupId>com.couchbase.client</groupId>
        <artifactId>java-client</artifactId>
        <version>${couchbase-client.version}</version>
      </dependency>
      <dependency>
        <groupId>org.crac</groupId>
        <artifactId>crac</artifactId>
        <version>${crac.version}</version>
      </dependency>
      <dependency>
        <groupId>com.ibm.db2</groupId>
        <artifactId>jcc</artifactId>
        <version>${db2-jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>io.spring.gradle</groupId>
        <artifactId>dependency-management-plugin</artifactId>
        <version>${dependency-management-plugin.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derby</artifactId>
        <version>${derby.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbyclient</artifactId>
        <version>${derby.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbynet</artifactId>
        <version>${derby.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbyoptionaltools</artifactId>
        <version>${derby.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbyshared</artifactId>
        <version>${derby.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.derby</groupId>
        <artifactId>derbytools</artifactId>
        <version>${derby.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache</artifactId>
        <version>${ehcache3.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache</artifactId>
        <version>${ehcache3.version}</version>
        <classifier>jakarta</classifier>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache-clustered</artifactId>
        <version>${ehcache3.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache-transactions</artifactId>
        <version>${ehcache3.version}</version>
      </dependency>
      <dependency>
        <groupId>org.ehcache</groupId>
        <artifactId>ehcache-transactions</artifactId>
        <version>${ehcache3.version}</version>
        <classifier>jakarta</classifier>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-client</artifactId>
        <version>${elasticsearch-client.version}</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.elasticsearch.client</groupId>
        <artifactId>elasticsearch-rest-client-sniffer</artifactId>
        <version>${elasticsearch-client.version}</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>co.elastic.clients</groupId>
        <artifactId>elasticsearch-java</artifactId>
        <version>${elasticsearch-client.version}</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-core</artifactId>
        <version>${flyway.version}</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-database-oracle</artifactId>
        <version>${flyway.version}</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-firebird</artifactId>
        <version>${flyway.version}</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-mysql</artifactId>
        <version>${flyway.version}</version>
      </dependency>
      <dependency>
        <groupId>org.flywaydb</groupId>
        <artifactId>flyway-sqlserver</artifactId>
        <version>${flyway.version}</version>
      </dependency>
      <dependency>
        <groupId>org.freemarker</groupId>
        <artifactId>freemarker</artifactId>
        <version>${freemarker.version}</version>
      </dependency>
      <dependency>
        <groupId>org.glassfish.web</groupId>
        <artifactId>jakarta.servlet.jsp.jstl</artifactId>
        <version>${glassfish-jstl.version}</version>
      </dependency>
      <dependency>
        <groupId>com.graphql-java</groupId>
        <artifactId>graphql-java</artifactId>
        <version>${graphql-java.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.gson</groupId>
        <artifactId>gson</artifactId>
        <version>${gson.version}</version>
      </dependency>
      <dependency>
        <groupId>com.h2database</groupId>
        <artifactId>h2</artifactId>
        <version>${h2.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest</artifactId>
        <version>${hamcrest.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-core</artifactId>
        <version>${hamcrest.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-library</artifactId>
        <version>${hamcrest.version}</version>
      </dependency>
      <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast</artifactId>
        <version>${hazelcast.version}</version>
      </dependency>
      <dependency>
        <groupId>com.hazelcast</groupId>
        <artifactId>hazelcast-spring</artifactId>
        <version>${hazelcast.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-agroal</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-ant</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-c3p0</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-community-dialects</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-core</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-envers</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-graalvm</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-hikaricp</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-jcache</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-jpamodelgen</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-micrometer</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-proxool</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-spatial</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-testing</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.orm</groupId>
        <artifactId>hibernate-vibur</artifactId>
        <version>${hibernate.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator</artifactId>
        <version>${hibernate-validator.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hibernate.validator</groupId>
        <artifactId>hibernate-validator-annotation-processor</artifactId>
        <version>${hibernate-validator.version}</version>
      </dependency>
      <dependency>
        <groupId>com.zaxxer</groupId>
        <artifactId>HikariCP</artifactId>
        <version>${hikaricp.version}</version>
      </dependency>
      <dependency>
        <groupId>org.hsqldb</groupId>
        <artifactId>hsqldb</artifactId>
        <version>${hsqldb.version}</version>
      </dependency>
      <dependency>
        <groupId>net.sourceforge.htmlunit</groupId>
        <artifactId>htmlunit</artifactId>
        <version>${htmlunit.version}</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpasyncclient</artifactId>
        <version>${httpasyncclient.version}</version>
        <exclusions>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5</artifactId>
        <version>${httpclient5.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5-cache</artifactId>
        <version>${httpclient5.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5-fluent</artifactId>
        <version>${httpclient5.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.client5</groupId>
        <artifactId>httpclient5-win</artifactId>
        <version>${httpclient5.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>${httpcore.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore-nio</artifactId>
        <version>${httpcore.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5</artifactId>
        <version>${httpcore5.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5-h2</artifactId>
        <version>${httpcore5.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5-reactive</artifactId>
        <version>${httpcore5.version}</version>
      </dependency>
      <dependency>
        <groupId>org.influxdb</groupId>
        <artifactId>influxdb-java</artifactId>
        <version>${influxdb-java.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.activation</groupId>
        <artifactId>jakarta.activation-api</artifactId>
        <version>${jakarta-activation.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.annotation</groupId>
        <artifactId>jakarta.annotation-api</artifactId>
        <version>${jakarta-annotation.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.jms</groupId>
        <artifactId>jakarta.jms-api</artifactId>
        <version>${jakarta-jms.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.json</groupId>
        <artifactId>jakarta.json-api</artifactId>
        <version>${jakarta-json.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.json.bind</groupId>
        <artifactId>jakarta.json.bind-api</artifactId>
        <version>${jakarta-json-bind.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.mail</groupId>
        <artifactId>jakarta.mail-api</artifactId>
        <version>${jakarta-mail.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.management.j2ee</groupId>
        <artifactId>jakarta.management.j2ee-api</artifactId>
        <version>${jakarta-management.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.persistence</groupId>
        <artifactId>jakarta.persistence-api</artifactId>
        <version>${jakarta-persistence.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.servlet</groupId>
        <artifactId>jakarta.servlet-api</artifactId>
        <version>${jakarta-servlet.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.servlet.jsp.jstl</groupId>
        <artifactId>jakarta.servlet.jsp.jstl-api</artifactId>
        <version>${jakarta-servlet-jsp-jstl.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.transaction</groupId>
        <artifactId>jakarta.transaction-api</artifactId>
        <version>${jakarta-transaction.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.validation</groupId>
        <artifactId>jakarta.validation-api</artifactId>
        <version>${jakarta-validation.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.websocket</groupId>
        <artifactId>jakarta.websocket-api</artifactId>
        <version>${jakarta-websocket.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.websocket</groupId>
        <artifactId>jakarta.websocket-client-api</artifactId>
        <version>${jakarta-websocket.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.ws.rs</groupId>
        <artifactId>jakarta.ws.rs-api</artifactId>
        <version>${jakarta-ws-rs.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.bind</groupId>
        <artifactId>jakarta.xml.bind-api</artifactId>
        <version>${jakarta-xml-bind.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.soap</groupId>
        <artifactId>jakarta.xml.soap-api</artifactId>
        <version>${jakarta-xml-soap.version}</version>
      </dependency>
      <dependency>
        <groupId>jakarta.xml.ws</groupId>
        <artifactId>jakarta.xml.ws-api</artifactId>
        <version>${jakarta-xml-ws.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>commons-compiler</artifactId>
        <version>${janino.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>commons-compiler-jdk</artifactId>
        <version>${janino.version}</version>
      </dependency>
      <dependency>
        <groupId>org.codehaus.janino</groupId>
        <artifactId>janino</artifactId>
        <version>${janino.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.cache</groupId>
        <artifactId>cache-api</artifactId>
        <version>${javax-cache.version}</version>
      </dependency>
      <dependency>
        <groupId>javax.money</groupId>
        <artifactId>money-api</artifactId>
        <version>${javax-money.version}</version>
      </dependency>
      <dependency>
        <groupId>jaxen</groupId>
        <artifactId>jaxen</artifactId>
        <version>${jaxen.version}</version>
      </dependency>
      <dependency>
        <groupId>org.firebirdsql.jdbc</groupId>
        <artifactId>jaybird</artifactId>
        <version>${jaybird.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.logging</groupId>
        <artifactId>jboss-logging</artifactId>
        <version>${jboss-logging.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jdom</groupId>
        <artifactId>jdom2</artifactId>
        <version>${jdom2.version}</version>
      </dependency>
      <dependency>
        <groupId>redis.clients</groupId>
        <artifactId>jedis</artifactId>
        <version>${jedis.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-reactive-httpclient</artifactId>
        <version>${jetty-reactive-httpclient.version}</version>
      </dependency>
      <dependency>
        <groupId>com.samskivert</groupId>
        <artifactId>jmustache</artifactId>
        <version>${jmustache.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq</artifactId>
        <version>${jooq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-codegen</artifactId>
        <version>${jooq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-kotlin</artifactId>
        <version>${jooq.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jooq</groupId>
        <artifactId>jooq-meta</artifactId>
        <version>${jooq.version}</version>
      </dependency>
      <dependency>
        <groupId>com.jayway.jsonpath</groupId>
        <artifactId>json-path</artifactId>
        <version>${json-path.version}</version>
      </dependency>
      <dependency>
        <groupId>com.jayway.jsonpath</groupId>
        <artifactId>json-path-assert</artifactId>
        <version>${json-path.version}</version>
      </dependency>
      <dependency>
        <groupId>net.minidev</groupId>
        <artifactId>json-smart</artifactId>
        <version>${json-smart.version}</version>
      </dependency>
      <dependency>
        <groupId>org.skyscreamer</groupId>
        <artifactId>jsonassert</artifactId>
        <version>${jsonassert.version}</version>
      </dependency>
      <dependency>
        <groupId>net.sourceforge.jtds</groupId>
        <artifactId>jtds</artifactId>
        <version>${jtds.version}</version>
      </dependency>
      <dependency>
        <groupId>junit</groupId>
        <artifactId>junit</artifactId>
        <version>${junit.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-api</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-basic-auth-extension</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-file</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-json</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-mirror</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-mirror-client</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-runtime</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>connect-transforms</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>generator</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-clients</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-clients</artifactId>
        <version>${kafka.version}</version>
        <classifier>test</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-log4j-appender</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-metadata</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-raft</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-server-common</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-server-common</artifactId>
        <version>${kafka.version}</version>
        <classifier>test</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-shell</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-storage</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-storage-api</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams-scala_2.12</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams-scala_2.13</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-streams-test-utils</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka-tools</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.12</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.12</artifactId>
        <version>${kafka.version}</version>
        <classifier>test</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.13</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>kafka_2.13</artifactId>
        <version>${kafka.version}</version>
        <classifier>test</classifier>
      </dependency>
      <dependency>
        <groupId>org.apache.kafka</groupId>
        <artifactId>trogdor</artifactId>
        <version>${kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>io.lettuce</groupId>
        <artifactId>lettuce-core</artifactId>
        <version>${lettuce.version}</version>
      </dependency>
      <dependency>
        <groupId>org.liquibase</groupId>
        <artifactId>liquibase-cdi</artifactId>
        <version>${liquibase.version}</version>
      </dependency>
      <dependency>
        <groupId>org.liquibase</groupId>
        <artifactId>liquibase-core</artifactId>
        <version>${liquibase.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-access</artifactId>
        <version>${logback.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-classic</artifactId>
        <version>${logback.version}</version>
      </dependency>
      <dependency>
        <groupId>ch.qos.logback</groupId>
        <artifactId>logback-core</artifactId>
        <version>${logback.version}</version>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <version>${lombok.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mariadb.jdbc</groupId>
        <artifactId>mariadb-java-client</artifactId>
        <version>${mariadb.version}</version>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-registry-stackdriver</artifactId>
        <version>${micrometer.version}</version>
        <exclusions>
          <exclusion>
            <groupId>javax.annotation</groupId>
            <artifactId>javax.annotation-api</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>bson</artifactId>
        <version>${mongodb.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>bson-record-codec</artifactId>
        <version>${mongodb.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-core</artifactId>
        <version>${mongodb.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-legacy</artifactId>
        <version>${mongodb.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-reactivestreams</artifactId>
        <version>${mongodb.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mongodb</groupId>
        <artifactId>mongodb-driver-sync</artifactId>
        <version>${mongodb.version}</version>
      </dependency>
      <dependency>
        <groupId>com.microsoft.sqlserver</groupId>
        <artifactId>mssql-jdbc</artifactId>
        <version>${mssql-jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>com.mysql</groupId>
        <artifactId>mysql-connector-j</artifactId>
        <version>${mysql.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.google.protobuf</groupId>
            <artifactId>protobuf-java</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>net.sourceforge.nekohtml</groupId>
        <artifactId>nekohtml</artifactId>
        <version>${nekohtml.version}</version>
      </dependency>
      <dependency>
        <groupId>org.neo4j.driver</groupId>
        <artifactId>neo4j-java-driver</artifactId>
        <version>${neo4j-java-driver.version}</version>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.r2dbc</groupId>
        <artifactId>oracle-r2dbc</artifactId>
        <version>${oracle-r2dbc.version}</version>
      </dependency>
      <dependency>
        <groupId>org.messaginghub</groupId>
        <artifactId>pooled-jms</artifactId>
        <version>${pooled-jms.version}</version>
      </dependency>
      <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>postgresql</artifactId>
        <version>${postgresql.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>bouncy-castle-bc</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>bouncy-castle-bcfips</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-1x-base</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-1x</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-2x-shaded</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-admin-api</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-admin-original</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-admin</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-all</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-api</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-auth-athenz</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-auth-sasl</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-messagecrypto-bc</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-original</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-tools-api</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-tools</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-common</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-config-validation</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-api</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-proto</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-functions-utils</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-aerospike</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-alluxio</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-aws</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-batch-data-generator</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-batch-discovery-triggerers</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-canal</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-cassandra</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-common</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-core</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-data-generator</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium-core</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium-mongodb</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium-mssql</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium-mysql</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium-oracle</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium-postgres</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-debezium</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-dynamodb</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-elastic-search</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-file</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-flume</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-hbase</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-hdfs2</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-hdfs3</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-http</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-influxdb</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc-clickhouse</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc-core</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc-mariadb</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc-openmldb</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc-postgres</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc-sqlite</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-jdbc</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-kafka-connect-adaptor-nar</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-kafka-connect-adaptor</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-kafka</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-kinesis</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-mongo</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-netty</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-nsq</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-rabbitmq</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-redis</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-solr</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io-twitter</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-io</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-metadata</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-presto-connector-original</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-presto-connector</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-sql</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-transaction-common</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-websocket</artifactId>
        <version>${pulsar.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-reactive-adapter</artifactId>
        <version>${pulsar-reactive.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-reactive-api</artifactId>
        <version>${pulsar-reactive.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-reactive-jackson</artifactId>
        <version>${pulsar-reactive.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-reactive-producer-cache-caffeine-shaded</artifactId>
        <version>${pulsar-reactive.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.pulsar</groupId>
        <artifactId>pulsar-client-reactive-producer-cache-caffeine</artifactId>
        <version>${pulsar-reactive.version}</version>
      </dependency>
      <dependency>
        <groupId>org.quartz-scheduler</groupId>
        <artifactId>quartz</artifactId>
        <version>${quartz.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.mchange</groupId>
            <artifactId>c3p0</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.zaxxer</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>org.quartz-scheduler</groupId>
        <artifactId>quartz-jobs</artifactId>
        <version>${quartz.version}</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-h2</artifactId>
        <version>${r2dbc-h2.version}</version>
      </dependency>
      <dependency>
        <groupId>org.mariadb</groupId>
        <artifactId>r2dbc-mariadb</artifactId>
        <version>${r2dbc-mariadb.version}</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-mssql</artifactId>
        <version>${r2dbc-mssql.version}</version>
      </dependency>
      <dependency>
        <groupId>io.asyncer</groupId>
        <artifactId>r2dbc-mysql</artifactId>
        <version>${r2dbc-mysql.version}</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-pool</artifactId>
        <version>${r2dbc-pool.version}</version>
      </dependency>
      <dependency>
        <groupId>org.postgresql</groupId>
        <artifactId>r2dbc-postgresql</artifactId>
        <version>${r2dbc-postgresql.version}</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-proxy</artifactId>
        <version>${r2dbc-proxy.version}</version>
      </dependency>
      <dependency>
        <groupId>io.r2dbc</groupId>
        <artifactId>r2dbc-spi</artifactId>
        <version>${r2dbc-spi.version}</version>
      </dependency>
      <dependency>
        <groupId>com.rabbitmq</groupId>
        <artifactId>amqp-client</artifactId>
        <version>${rabbit-amqp-client.version}</version>
      </dependency>
      <dependency>
        <groupId>com.rabbitmq</groupId>
        <artifactId>stream-client</artifactId>
        <version>${rabbit-stream-client.version}</version>
      </dependency>
      <dependency>
        <groupId>org.reactivestreams</groupId>
        <artifactId>reactive-streams</artifactId>
        <version>${reactive-streams.version}</version>
      </dependency>
      <dependency>
        <groupId>io.reactivex.rxjava3</groupId>
        <artifactId>rxjava</artifactId>
        <version>${rxjava3.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-test</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-test-autoconfigure</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-testcontainers</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-actuator</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-actuator-autoconfigure</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-autoconfigure-processor</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-buildpack-platform</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-metadata</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-configuration-processor</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-devtools</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-docker-compose</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-jarmode-layertools</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-loader</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-loader-classic</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-loader-tools</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-properties-migrator</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-activemq</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-actuator</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-amqp</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-aop</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-artemis</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-batch</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-cache</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-cassandra</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-cassandra-reactive</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-couchbase</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-couchbase-reactive</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-elasticsearch</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jdbc</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-jpa</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-ldap</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-mongodb</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-mongodb-reactive</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-r2dbc</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-redis-reactive</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-neo4j</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-rest</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-freemarker</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-graphql</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-groovy-templates</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-hateoas</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-integration</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jdbc</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jersey</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jetty</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-jooq</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-json</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-log4j2</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-logging</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-mail</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-mustache</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-oauth2-authorization-server</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-oauth2-client</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-oauth2-resource-server</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-pulsar</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-pulsar-reactive</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-quartz</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-reactor-netty</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-rsocket</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-security</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-test</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-thymeleaf</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-tomcat</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-undertow</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-validation</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-webflux</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-websocket</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-web-services</artifactId>
        <version>3.2.4</version>
      </dependency>
      <dependency>
        <groupId>com.sun.xml.messaging.saaj</groupId>
        <artifactId>saaj-impl</artifactId>
        <version>${saaj-impl.version}</version>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>htmlunit-driver</artifactId>
        <version>${selenium-htmlunit.version}</version>
      </dependency>
      <dependency>
        <groupId>com.sendgrid</groupId>
        <artifactId>sendgrid-java</artifactId>
        <version>${sendgrid.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jcl-over-slf4j</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>jul-to-slf4j</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>log4j-over-slf4j</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-ext</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-jdk-platform-logging</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-jdk14</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-log4j12</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-nop</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-reload4j</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-simple</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.yaml</groupId>
        <artifactId>snakeyaml</artifactId>
        <version>${snakeyaml.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-oauth2-authorization-server</artifactId>
        <version>${spring-authorization-server.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.graphql</groupId>
        <artifactId>spring-graphql</artifactId>
        <version>${spring-graphql.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.graphql</groupId>
        <artifactId>spring-graphql-test</artifactId>
        <version>${spring-graphql.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.hateoas</groupId>
        <artifactId>spring-hateoas</artifactId>
        <version>${spring-hateoas.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.kafka</groupId>
        <artifactId>spring-kafka</artifactId>
        <version>${spring-kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.kafka</groupId>
        <artifactId>spring-kafka-test</artifactId>
        <version>${spring-kafka.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-core</artifactId>
        <version>${spring-ldap.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-ldif-core</artifactId>
        <version>${spring-ldap.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-odm</artifactId>
        <version>${spring-ldap.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.ldap</groupId>
        <artifactId>spring-ldap-test</artifactId>
        <version>${spring-ldap.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.retry</groupId>
        <artifactId>spring-retry</artifactId>
        <version>${spring-retry.version}</version>
      </dependency>
      <dependency>
        <groupId>org.xerial</groupId>
        <artifactId>sqlite-jdbc</artifactId>
        <version>${sqlite-jdbc.version}</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf</groupId>
        <artifactId>thymeleaf</artifactId>
        <version>${thymeleaf.version}</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf</groupId>
        <artifactId>thymeleaf-spring6</artifactId>
        <version>${thymeleaf.version}</version>
      </dependency>
      <dependency>
        <groupId>com.github.mxab.thymeleaf.extras</groupId>
        <artifactId>thymeleaf-extras-data-attribute</artifactId>
        <version>${thymeleaf-extras-data-attribute.version}</version>
      </dependency>
      <dependency>
        <groupId>org.thymeleaf.extras</groupId>
        <artifactId>thymeleaf-extras-springsecurity6</artifactId>
        <version>${thymeleaf-extras-springsecurity.version}</version>
      </dependency>
      <dependency>
        <groupId>nz.net.ultraq.thymeleaf</groupId>
        <artifactId>thymeleaf-layout-dialect</artifactId>
        <version>${thymeleaf-layout-dialect.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-annotations-api</artifactId>
        <version>${tomcat.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-jdbc</artifactId>
        <version>${tomcat.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat</groupId>
        <artifactId>tomcat-jsp-api</artifactId>
        <version>${tomcat.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-core</artifactId>
        <version>${tomcat.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-el</artifactId>
        <version>${tomcat.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-jasper</artifactId>
        <version>${tomcat.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.tomcat.embed</groupId>
        <artifactId>tomcat-embed-websocket</artifactId>
        <version>${tomcat.version}</version>
      </dependency>
      <dependency>
        <groupId>com.unboundid</groupId>
        <artifactId>unboundid-ldapsdk</artifactId>
        <version>${unboundid-ldapsdk.version}</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-core</artifactId>
        <version>${undertow.version}</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-servlet</artifactId>
        <version>${undertow.version}</version>
      </dependency>
      <dependency>
        <groupId>io.undertow</groupId>
        <artifactId>undertow-websockets-jsr</artifactId>
        <version>${undertow.version}</version>
      </dependency>
      <dependency>
        <groupId>org.webjars</groupId>
        <artifactId>webjars-locator-core</artifactId>
        <version>${webjars-locator-core.version}</version>
      </dependency>
      <dependency>
        <groupId>wsdl4j</groupId>
        <artifactId>wsdl4j</artifactId>
        <version>${wsdl4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-assertj</artifactId>
        <version>${xmlunit2.version}</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-assertj3</artifactId>
        <version>${xmlunit2.version}</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-core</artifactId>
        <version>${xmlunit2.version}</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-jakarta-jaxb-impl</artifactId>
        <version>${xmlunit2.version}</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-legacy</artifactId>
        <version>${xmlunit2.version}</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-matchers</artifactId>
        <version>${xmlunit2.version}</version>
      </dependency>
      <dependency>
        <groupId>org.xmlunit</groupId>
        <artifactId>xmlunit-placeholders</artifactId>
        <version>${xmlunit2.version}</version>
      </dependency>
      <dependency>
        <groupId>org.eclipse</groupId>
        <artifactId>yasson</artifactId>
        <version>${yasson.version}</version>
      </dependency>
      <dependency>
        <groupId>org.assertj</groupId>
        <artifactId>assertj-bom</artifactId>
        <version>${assertj.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.zipkin.brave</groupId>
        <artifactId>brave-bom</artifactId>
        <version>${brave.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.datastax.oss</groupId>
        <artifactId>java-driver-bom</artifactId>
        <version>${cassandra-driver.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.dropwizard.metrics</groupId>
        <artifactId>metrics-bom</artifactId>
        <version>${dropwizard-metrics.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jaxb</groupId>
        <artifactId>jaxb-bom</artifactId>
        <version>${glassfish-jaxb.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.groovy</groupId>
        <artifactId>groovy-bom</artifactId>
        <version>${groovy.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.infinispan</groupId>
        <artifactId>infinispan-bom</artifactId>
        <version>${infinispan.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson</groupId>
        <artifactId>jackson-bom</artifactId>
        <version>${jackson-bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.glassfish.jersey</groupId>
        <artifactId>jersey-bom</artifactId>
        <version>${jersey.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty.ee10</groupId>
        <artifactId>jetty-ee10-bom</artifactId>
        <version>${jetty.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.eclipse.jetty</groupId>
        <artifactId>jetty-bom</artifactId>
        <version>${jetty.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${junit-jupiter.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlin</groupId>
        <artifactId>kotlin-bom</artifactId>
        <version>${kotlin.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-coroutines-bom</artifactId>
        <version>${kotlin-coroutines.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.jetbrains.kotlinx</groupId>
        <artifactId>kotlinx-serialization-bom</artifactId>
        <version>${kotlin-serialization.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-bom</artifactId>
        <version>${log4j2.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-bom</artifactId>
        <version>${micrometer.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.micrometer</groupId>
        <artifactId>micrometer-tracing-bom</artifactId>
        <version>${micrometer-tracing.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-bom</artifactId>
        <version>${mockito.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.netty</groupId>
        <artifactId>netty-bom</artifactId>
        <version>${netty.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp-bom</artifactId>
        <version>${okhttp.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-bom</artifactId>
        <version>${opentelemetry.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.oracle.database.jdbc</groupId>
        <artifactId>ojdbc-bom</artifactId>
        <version>${oracle-database.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.prometheus</groupId>
        <artifactId>simpleclient_bom</artifactId>
        <version>${prometheus-client.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.querydsl</groupId>
        <artifactId>querydsl-bom</artifactId>
        <version>${querydsl.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-bom</artifactId>
        <version>${reactor-bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.rest-assured</groupId>
        <artifactId>rest-assured-bom</artifactId>
        <version>${rest-assured.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.rsocket</groupId>
        <artifactId>rsocket-bom</artifactId>
        <version>${rsocket.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.seleniumhq.selenium</groupId>
        <artifactId>selenium-bom</artifactId>
        <version>${selenium.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.amqp</groupId>
        <artifactId>spring-amqp-bom</artifactId>
        <version>${spring-amqp.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.batch</groupId>
        <artifactId>spring-batch-bom</artifactId>
        <version>${spring-batch.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-bom</artifactId>
        <version>${spring-data-bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework</groupId>
        <artifactId>spring-framework-bom</artifactId>
        <version>${spring-framework.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.integration</groupId>
        <artifactId>spring-integration-bom</artifactId>
        <version>${spring-integration.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.pulsar</groupId>
        <artifactId>spring-pulsar-bom</artifactId>
        <version>${spring-pulsar.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-bom</artifactId>
        <version>${spring-restdocs.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.security</groupId>
        <artifactId>spring-security-bom</artifactId>
        <version>${spring-security.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.session</groupId>
        <artifactId>spring-session-bom</artifactId>
        <version>${spring-session.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.springframework.ws</groupId>
        <artifactId>spring-ws-bom</artifactId>
        <version>${spring-ws.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.testcontainers</groupId>
        <artifactId>testcontainers-bom</artifactId>
        <version>${testcontainers.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>build-helper-maven-plugin</artifactId>
          <version>${build-helper-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.flywaydb</groupId>
          <artifactId>flyway-maven-plugin</artifactId>
          <version>${flyway.version}</version>
        </plugin>
        <plugin>
          <groupId>io.github.git-commit-id</groupId>
          <artifactId>git-commit-id-maven-plugin</artifactId>
          <version>${git-commit-id-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.jooq</groupId>
          <artifactId>jooq-codegen-maven</artifactId>
          <version>${jooq.version}</version>
        </plugin>
        <plugin>
          <groupId>org.jetbrains.kotlin</groupId>
          <artifactId>kotlin-maven-plugin</artifactId>
          <version>${kotlin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.liquibase</groupId>
          <artifactId>liquibase-maven-plugin</artifactId>
          <version>${liquibase.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-antrun-plugin</artifactId>
          <version>${maven-antrun-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-assembly-plugin</artifactId>
          <version>${maven-assembly-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-clean-plugin</artifactId>
          <version>${maven-clean-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-compiler-plugin</artifactId>
          <version>${maven-compiler-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>${maven-dependency-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-deploy-plugin</artifactId>
          <version>${maven-deploy-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-enforcer-plugin</artifactId>
          <version>${maven-enforcer-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-failsafe-plugin</artifactId>
          <version>${maven-failsafe-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-help-plugin</artifactId>
          <version>${maven-help-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-install-plugin</artifactId>
          <version>${maven-install-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-invoker-plugin</artifactId>
          <version>${maven-invoker-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>${maven-jar-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>${maven-javadoc-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-resources-plugin</artifactId>
          <version>${maven-resources-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-shade-plugin</artifactId>
          <version>${maven-shade-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>${maven-source-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${maven-surefire-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-war-plugin</artifactId>
          <version>${maven-war-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.graalvm.buildtools</groupId>
          <artifactId>native-maven-plugin</artifactId>
          <version>${native-build-tools-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.springframework.boot</groupId>
          <artifactId>spring-boot-maven-plugin</artifactId>
          <version>3.2.4</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>versions-maven-plugin</artifactId>
          <version>${versions-maven-plugin.version}</version>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>xml-maven-plugin</artifactId>
          <version>${xml-maven-plugin.version}</version>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
</project>
