{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-starter-logging", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "ch.qos.logback", "module": "logback-classic", "version": {"requires": "1.4.14"}}, {"group": "org.apache.logging.log4j", "module": "log4j-to-slf4j", "version": {"requires": "2.21.1"}}, {"group": "org.slf4j", "module": "jul-to-slf4j", "version": {"requires": "2.0.13"}}], "files": [{"name": "spring-boot-starter-logging-3.2.5.jar", "url": "spring-boot-starter-logging-3.2.5.jar", "size": 4760, "sha512": "4a7341443b8e371ee0076ec01e366a772d5997533dceae89be37ba83dce6a2d7903e3f727984e4e20e7e351a838757944bbbb485cfeac93fc6d7cb75003665d1", "sha256": "bc9cc1e290acd15700a81dd06de660b218fd8562b4b6a5eb47952bba9db2526d", "sha1": "28cf3a346da7bb624381ccc21d7a27500181de63", "md5": "3466ba61e84fa6163c3926800344b7e1"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "ch.qos.logback", "module": "logback-classic", "version": {"requires": "1.4.14"}}, {"group": "org.apache.logging.log4j", "module": "log4j-to-slf4j", "version": {"requires": "2.21.1"}}, {"group": "org.slf4j", "module": "jul-to-slf4j", "version": {"requires": "2.0.13"}}], "files": [{"name": "spring-boot-starter-logging-3.2.5.jar", "url": "spring-boot-starter-logging-3.2.5.jar", "size": 4760, "sha512": "4a7341443b8e371ee0076ec01e366a772d5997533dceae89be37ba83dce6a2d7903e3f727984e4e20e7e351a838757944bbbb485cfeac93fc6d7cb75003665d1", "sha256": "bc9cc1e290acd15700a81dd06de660b218fd8562b4b6a5eb47952bba9db2526d", "sha1": "28cf3a346da7bb624381ccc21d7a27500181de63", "md5": "3466ba61e84fa6163c3926800344b7e1"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-starter-logging-3.2.5.jar", "url": "spring-boot-starter-logging-3.2.5.jar", "size": 4760, "sha512": "4a7341443b8e371ee0076ec01e366a772d5997533dceae89be37ba83dce6a2d7903e3f727984e4e20e7e351a838757944bbbb485cfeac93fc6d7cb75003665d1", "sha256": "bc9cc1e290acd15700a81dd06de660b218fd8562b4b6a5eb47952bba9db2526d", "sha1": "28cf3a346da7bb624381ccc21d7a27500181de63", "md5": "3466ba61e84fa6163c3926800344b7e1"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-logging-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-logging-3.2.5.jar", "url": "spring-boot-starter-logging-3.2.5.jar", "size": 4760, "sha512": "4a7341443b8e371ee0076ec01e366a772d5997533dceae89be37ba83dce6a2d7903e3f727984e4e20e7e351a838757944bbbb485cfeac93fc6d7cb75003665d1", "sha256": "bc9cc1e290acd15700a81dd06de660b218fd8562b4b6a5eb47952bba9db2526d", "sha1": "28cf3a346da7bb624381ccc21d7a27500181de63", "md5": "3466ba61e84fa6163c3926800344b7e1"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-logging-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-logging-3.2.5-javadoc.jar", "url": "spring-boot-starter-logging-3.2.5-javadoc.jar", "size": 4743, "sha512": "22c98965ab98b4b0eb76d4b392da97047eee0ac62ffb33d06acc686f3b239e6977ea6adef39799a0ce3fdbde0f2c0680062f2e6586222e721b97d55908800762", "sha256": "1ac356ec09ac1f5f1f9277b1e1381b75798d6c01c08000a24912aed62226ccb4", "sha1": "6ce587c63a24dfe2c940fb35c4ee75c960a79198", "md5": "2fec0f70f95cebd1262b297f6070a2d0"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-logging-3.2.5-sources.jar", "url": "spring-boot-starter-logging-3.2.5-sources.jar", "size": 4743, "sha512": "e929f535ab2da725e250fc587fbc8316cfa1034fd7c65547bc7a5b45329f38c0366a523277ef5646227849425e84f7ba99dd8116c44c4847a2dfe2dce0a505de", "sha256": "718f4cad426f17b10e7588fd8c947fb70b7c0f5cfcfdfe471ebc89bd6de931fb", "sha1": "2d73c37a0a0bb7445172e035ad001da283dc3fa9", "md5": "e77a2c8aa83e427886bf3c7398f09271"}]}]}