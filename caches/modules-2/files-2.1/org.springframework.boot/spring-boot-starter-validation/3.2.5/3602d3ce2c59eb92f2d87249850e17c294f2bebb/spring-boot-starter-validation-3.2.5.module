{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-starter-validation", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "org.apache.tomcat.embed", "module": "tomcat-embed-el", "version": {"requires": "10.1.20"}}, {"group": "org.hibernate.validator", "module": "hibernate-validator", "version": {"requires": "8.0.1.Final"}}], "files": [{"name": "spring-boot-starter-validation-3.2.5.jar", "url": "spring-boot-starter-validation-3.2.5.jar", "size": 4767, "sha512": "f0b4295e4d5fd7994a684d4f950bb90a38a8329149cce8225bfdd98b254569b9b4c3244a7c288e2970b54e83589adc783c726c2f075aaee478672617400290e3", "sha256": "c3e95be8c4dfa0e2d8733f28ce1bbac90c2eea1108270728249215ebc0fe87c7", "sha1": "03757dceb20eca3880ee1fb5f07159e42c7f7405", "md5": "c227bdd9029320ad068ccb5c6de80865"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "org.apache.tomcat.embed", "module": "tomcat-embed-el", "version": {"requires": "10.1.20"}}, {"group": "org.hibernate.validator", "module": "hibernate-validator", "version": {"requires": "8.0.1.Final"}}], "files": [{"name": "spring-boot-starter-validation-3.2.5.jar", "url": "spring-boot-starter-validation-3.2.5.jar", "size": 4767, "sha512": "f0b4295e4d5fd7994a684d4f950bb90a38a8329149cce8225bfdd98b254569b9b4c3244a7c288e2970b54e83589adc783c726c2f075aaee478672617400290e3", "sha256": "c3e95be8c4dfa0e2d8733f28ce1bbac90c2eea1108270728249215ebc0fe87c7", "sha1": "03757dceb20eca3880ee1fb5f07159e42c7f7405", "md5": "c227bdd9029320ad068ccb5c6de80865"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-starter-validation-3.2.5.jar", "url": "spring-boot-starter-validation-3.2.5.jar", "size": 4767, "sha512": "f0b4295e4d5fd7994a684d4f950bb90a38a8329149cce8225bfdd98b254569b9b4c3244a7c288e2970b54e83589adc783c726c2f075aaee478672617400290e3", "sha256": "c3e95be8c4dfa0e2d8733f28ce1bbac90c2eea1108270728249215ebc0fe87c7", "sha1": "03757dceb20eca3880ee1fb5f07159e42c7f7405", "md5": "c227bdd9029320ad068ccb5c6de80865"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-validation-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-validation-3.2.5.jar", "url": "spring-boot-starter-validation-3.2.5.jar", "size": 4767, "sha512": "f0b4295e4d5fd7994a684d4f950bb90a38a8329149cce8225bfdd98b254569b9b4c3244a7c288e2970b54e83589adc783c726c2f075aaee478672617400290e3", "sha256": "c3e95be8c4dfa0e2d8733f28ce1bbac90c2eea1108270728249215ebc0fe87c7", "sha1": "03757dceb20eca3880ee1fb5f07159e42c7f7405", "md5": "c227bdd9029320ad068ccb5c6de80865"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-validation-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-validation-3.2.5-javadoc.jar", "url": "spring-boot-starter-validation-3.2.5-javadoc.jar", "size": 4745, "sha512": "e274e1f104d979ffe321f3dde0672abe050b5c0387b49bdd5233faad5529974afd448fed1990e57ca1b97114269190298021ea3311f2e005adcb1e277f022f93", "sha256": "b975dea9ca6124a2d68859af4806e31bc4a4afde7174cb9af515b7441b5922ca", "sha1": "5023183954577533677da4258ccdd1df67737aaa", "md5": "2fc89e1e64c3297035bf25bcaa970a4d"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-validation-3.2.5-sources.jar", "url": "spring-boot-starter-validation-3.2.5-sources.jar", "size": 4744, "sha512": "1456de91053d07930db2861153723b7d91a604145f197c754aba089ecf4a9a199513bee9e0cd36959ec417d2e168758fded26f6a31e75c5384388adda4deb532", "sha256": "b66b08285d03eee1fce0254ec2a47244f82d75d680bc594986df5fa242103adb", "sha1": "17d07f8c86d0c0c72b23670a6f0efe33778c8ce5", "md5": "e4c13169dc31bde1f4a9246868fcf59c"}]}]}