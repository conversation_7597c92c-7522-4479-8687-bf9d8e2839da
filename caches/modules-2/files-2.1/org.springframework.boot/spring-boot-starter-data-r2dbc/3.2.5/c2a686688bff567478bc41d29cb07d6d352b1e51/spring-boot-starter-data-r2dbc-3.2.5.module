{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-starter-data-r2dbc", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.data", "module": "spring-data-r2dbc", "version": {"requires": "3.2.5"}}, {"group": "io.r2dbc", "module": "r2dbc-spi", "version": {"requires": "1.0.0.RELEASE"}}, {"group": "io.r2dbc", "module": "r2dbc-pool", "version": {"requires": "1.0.1.RELEASE"}}], "files": [{"name": "spring-boot-starter-data-r2dbc-3.2.5.jar", "url": "spring-boot-starter-data-r2dbc-3.2.5.jar", "size": 4751, "sha512": "c33b9c1897901afcefbac7da406b0b6df79273be92ba35cd314f8f13e61999d98eb860a96a3afc59bfe4da1daff47f497e15e88de45fdd1690de6750ba5b417b", "sha256": "05b0f5e3a398e4adbccae70c34a57692c25e6d347b7d8a03d2ae1203d98369af", "sha1": "e789022a060ecdfcfb54b269e37924bd6f2eb90a", "md5": "73284d84adea7efe5a529eac61a6e755"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.data", "module": "spring-data-r2dbc", "version": {"requires": "3.2.5"}}, {"group": "io.r2dbc", "module": "r2dbc-spi", "version": {"requires": "1.0.0.RELEASE"}}, {"group": "io.r2dbc", "module": "r2dbc-pool", "version": {"requires": "1.0.1.RELEASE"}}], "files": [{"name": "spring-boot-starter-data-r2dbc-3.2.5.jar", "url": "spring-boot-starter-data-r2dbc-3.2.5.jar", "size": 4751, "sha512": "c33b9c1897901afcefbac7da406b0b6df79273be92ba35cd314f8f13e61999d98eb860a96a3afc59bfe4da1daff47f497e15e88de45fdd1690de6750ba5b417b", "sha256": "05b0f5e3a398e4adbccae70c34a57692c25e6d347b7d8a03d2ae1203d98369af", "sha1": "e789022a060ecdfcfb54b269e37924bd6f2eb90a", "md5": "73284d84adea7efe5a529eac61a6e755"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-starter-data-r2dbc-3.2.5.jar", "url": "spring-boot-starter-data-r2dbc-3.2.5.jar", "size": 4751, "sha512": "c33b9c1897901afcefbac7da406b0b6df79273be92ba35cd314f8f13e61999d98eb860a96a3afc59bfe4da1daff47f497e15e88de45fdd1690de6750ba5b417b", "sha256": "05b0f5e3a398e4adbccae70c34a57692c25e6d347b7d8a03d2ae1203d98369af", "sha1": "e789022a060ecdfcfb54b269e37924bd6f2eb90a", "md5": "73284d84adea7efe5a529eac61a6e755"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-data-r2dbc-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-data-r2dbc-3.2.5.jar", "url": "spring-boot-starter-data-r2dbc-3.2.5.jar", "size": 4751, "sha512": "c33b9c1897901afcefbac7da406b0b6df79273be92ba35cd314f8f13e61999d98eb860a96a3afc59bfe4da1daff47f497e15e88de45fdd1690de6750ba5b417b", "sha256": "05b0f5e3a398e4adbccae70c34a57692c25e6d347b7d8a03d2ae1203d98369af", "sha1": "e789022a060ecdfcfb54b269e37924bd6f2eb90a", "md5": "73284d84adea7efe5a529eac61a6e755"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-data-r2dbc-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-data-r2dbc-3.2.5-javadoc.jar", "url": "spring-boot-starter-data-r2dbc-3.2.5-javadoc.jar", "size": 4748, "sha512": "809aea71fe9071ad1d24cc86d777b51767186783c7122d9c2291a66ec3710f46038edd8126ea327e105918177ad6add9d4211b5a76824d77d4eaf43ad9c5d074", "sha256": "d6f62fe6d321e36ca404fdf512726980a748ad4ac7e39510f42f35a2ecbd4682", "sha1": "d84d3bc1c98dde569441461586f0e02e67eec66c", "md5": "56198dcec1eeb7f27785b0a8c290729c"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-data-r2dbc-3.2.5-sources.jar", "url": "spring-boot-starter-data-r2dbc-3.2.5-sources.jar", "size": 4747, "sha512": "8c1bfe95cc9bc7a05a8431dc6f2329ec464e5b493fe8d90896dfb8c0df450e4c7f05e5ccd9e3985b51ec3916823bcf76dc3a6493194132751dd1aa7163db2884", "sha256": "c8e91f399b921c85948b3ca4465a12c6420a9aae74b2bd9e087aa6c8ac42e96d", "sha1": "f4b4de275a770819d320df3da5c99ebda0addb83", "md5": "b6e4424510f98e5ebf4990b63269804e"}]}]}