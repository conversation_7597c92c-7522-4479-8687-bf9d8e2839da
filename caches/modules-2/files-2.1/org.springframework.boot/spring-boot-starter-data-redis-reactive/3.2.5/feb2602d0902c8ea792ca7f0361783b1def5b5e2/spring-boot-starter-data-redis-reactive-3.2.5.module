{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-starter-data-redis-reactive", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "io.lettuce", "module": "lettuce-core", "version": {"requires": "6.3.2.RELEASE"}}, {"group": "io.projectreactor", "module": "reactor-core", "version": {"requires": "3.6.5"}}, {"group": "org.springframework.data", "module": "spring-data-redis", "version": {"requires": "3.2.5"}}], "files": [{"name": "spring-boot-starter-data-redis-reactive-3.2.5.jar", "url": "spring-boot-starter-data-redis-reactive-3.2.5.jar", "size": 4794, "sha512": "bd975f17938e23289fe690cf7030b12499b81b019ae33f9400c42db9fce2ff150159924e2f1a538b91c9c4f013c1313cafca84d28aa31d70c8f43108ef3b73ce", "sha256": "2b718dbb0f0c106ecd73d6c55e6bb2037d6c9d7ee1e998e8254f50e5c9df9604", "sha1": "32f0300c703b4a433757b44ea1932901256257bb", "md5": "4dcc2004d8bc9158a943f90c627517f2"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "io.lettuce", "module": "lettuce-core", "version": {"requires": "6.3.2.RELEASE"}}, {"group": "io.projectreactor", "module": "reactor-core", "version": {"requires": "3.6.5"}}, {"group": "org.springframework.data", "module": "spring-data-redis", "version": {"requires": "3.2.5"}}], "files": [{"name": "spring-boot-starter-data-redis-reactive-3.2.5.jar", "url": "spring-boot-starter-data-redis-reactive-3.2.5.jar", "size": 4794, "sha512": "bd975f17938e23289fe690cf7030b12499b81b019ae33f9400c42db9fce2ff150159924e2f1a538b91c9c4f013c1313cafca84d28aa31d70c8f43108ef3b73ce", "sha256": "2b718dbb0f0c106ecd73d6c55e6bb2037d6c9d7ee1e998e8254f50e5c9df9604", "sha1": "32f0300c703b4a433757b44ea1932901256257bb", "md5": "4dcc2004d8bc9158a943f90c627517f2"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-starter-data-redis-reactive-3.2.5.jar", "url": "spring-boot-starter-data-redis-reactive-3.2.5.jar", "size": 4794, "sha512": "bd975f17938e23289fe690cf7030b12499b81b019ae33f9400c42db9fce2ff150159924e2f1a538b91c9c4f013c1313cafca84d28aa31d70c8f43108ef3b73ce", "sha256": "2b718dbb0f0c106ecd73d6c55e6bb2037d6c9d7ee1e998e8254f50e5c9df9604", "sha1": "32f0300c703b4a433757b44ea1932901256257bb", "md5": "4dcc2004d8bc9158a943f90c627517f2"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-data-redis-reactive-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-data-redis-reactive-3.2.5.jar", "url": "spring-boot-starter-data-redis-reactive-3.2.5.jar", "size": 4794, "sha512": "bd975f17938e23289fe690cf7030b12499b81b019ae33f9400c42db9fce2ff150159924e2f1a538b91c9c4f013c1313cafca84d28aa31d70c8f43108ef3b73ce", "sha256": "2b718dbb0f0c106ecd73d6c55e6bb2037d6c9d7ee1e998e8254f50e5c9df9604", "sha1": "32f0300c703b4a433757b44ea1932901256257bb", "md5": "4dcc2004d8bc9158a943f90c627517f2"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-data-redis-reactive-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-data-redis-reactive-3.2.5-javadoc.jar", "url": "spring-boot-starter-data-redis-reactive-3.2.5-javadoc.jar", "size": 4759, "sha512": "ff1e06f5d09e54716160374661976efc1bddd60dd99b5a113e32087ea1f23c9fdda7fc0b6646f5dae7a60cad96748b171d9dc4b972f49da9754853002488f189", "sha256": "d6d318073ef84271b8a1957523fdc93070e14326339890850b6026f3c1ea6b5c", "sha1": "49ce804a72fcca3ff79a03e64790c872abf170b8", "md5": "b08fd5d636362a636786063a90b1f1ae"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-data-redis-reactive-3.2.5-sources.jar", "url": "spring-boot-starter-data-redis-reactive-3.2.5-sources.jar", "size": 4755, "sha512": "034dfc77e02d818db6aa9e5aee57d277547ec1bb070fe14a89b12dae87477a567f0c1f011a76e9b7467142d721810b9c337fa6653d8330f59a7df9556c86127e", "sha256": "c3e684e44f2c6d9c444ab34328ee3299e3282140a3a9555a162b3a5aff8eb1b9", "sha1": "10ef741e5c071ad539c47e52fbd0be6d6cc0b34c", "md5": "8307763cd84055e99bbe5d625adb2062"}]}]}