{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-starter-reactor-netty", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.projectreactor.netty", "module": "reactor-netty-http", "version": {"requires": "1.1.18"}}], "files": [{"name": "spring-boot-starter-reactor-netty-3.2.5.jar", "url": "spring-boot-starter-reactor-netty-3.2.5.jar", "size": 4777, "sha512": "9118ad74b747af030ca1f7b0babb990c95e2e887175a392940176d4a6f622f9918ce409f9b945402b6af1bca64b7ac25efd8f91f6e87fb7ef0b1a01653021aa0", "sha256": "76eac69ae4f02d7805d96f6bfca734df22cd10f5b219c7f1488e699f1dc284d2", "sha1": "1bdb8b6f9275dca52937da72234f2650db38c30a", "md5": "d014af316fe1f562a8fc53d3ea5506bc"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.projectreactor.netty", "module": "reactor-netty-http", "version": {"requires": "1.1.18"}}], "files": [{"name": "spring-boot-starter-reactor-netty-3.2.5.jar", "url": "spring-boot-starter-reactor-netty-3.2.5.jar", "size": 4777, "sha512": "9118ad74b747af030ca1f7b0babb990c95e2e887175a392940176d4a6f622f9918ce409f9b945402b6af1bca64b7ac25efd8f91f6e87fb7ef0b1a01653021aa0", "sha256": "76eac69ae4f02d7805d96f6bfca734df22cd10f5b219c7f1488e699f1dc284d2", "sha1": "1bdb8b6f9275dca52937da72234f2650db38c30a", "md5": "d014af316fe1f562a8fc53d3ea5506bc"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-starter-reactor-netty-3.2.5.jar", "url": "spring-boot-starter-reactor-netty-3.2.5.jar", "size": 4777, "sha512": "9118ad74b747af030ca1f7b0babb990c95e2e887175a392940176d4a6f622f9918ce409f9b945402b6af1bca64b7ac25efd8f91f6e87fb7ef0b1a01653021aa0", "sha256": "76eac69ae4f02d7805d96f6bfca734df22cd10f5b219c7f1488e699f1dc284d2", "sha1": "1bdb8b6f9275dca52937da72234f2650db38c30a", "md5": "d014af316fe1f562a8fc53d3ea5506bc"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-reactor-netty-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-reactor-netty-3.2.5.jar", "url": "spring-boot-starter-reactor-netty-3.2.5.jar", "size": 4777, "sha512": "9118ad74b747af030ca1f7b0babb990c95e2e887175a392940176d4a6f622f9918ce409f9b945402b6af1bca64b7ac25efd8f91f6e87fb7ef0b1a01653021aa0", "sha256": "76eac69ae4f02d7805d96f6bfca734df22cd10f5b219c7f1488e699f1dc284d2", "sha1": "1bdb8b6f9275dca52937da72234f2650db38c30a", "md5": "d014af316fe1f562a8fc53d3ea5506bc"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-reactor-netty-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-reactor-netty-3.2.5-javadoc.jar", "url": "spring-boot-starter-reactor-netty-3.2.5-javadoc.jar", "size": 4750, "sha512": "18c9a2708304e525082063665592c3ac8d219c8854997d69474c7135d6fbdc6892618f3b8afeea661cf3c690efa65b34868d7c385e49f0d44dc04d10b893a2b5", "sha256": "5f39f88c2a2028ebb618e9de33eaaef11d20b21a58450242d7c961bb8e2a15dd", "sha1": "cb22461f69a090dc0273e1db539f48da2e30e2d4", "md5": "3972a629733ba4dd5b13cb2706c2a4eb"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-reactor-netty-3.2.5-sources.jar", "url": "spring-boot-starter-reactor-netty-3.2.5-sources.jar", "size": 4748, "sha512": "1bf558a59ed00e89e11acceddad684a448bfd295f58efeb6958bb0274c10297eaa08f500ad5cb81f686126c54925d69d59b4bf183f87ce3137d2e838fc5633d4", "sha256": "24779960bf6975e32788e180b4a459a3013bf486d36e18a74fbaa70026c6d2d4", "sha1": "6aaf0d1a8be2fc065402f632abba17132273adc4", "md5": "0a8d5b2310558e83e2b2e36cd6d625cc"}]}]}