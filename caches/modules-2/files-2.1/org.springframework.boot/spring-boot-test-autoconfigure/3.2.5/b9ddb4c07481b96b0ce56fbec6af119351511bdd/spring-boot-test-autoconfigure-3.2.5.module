{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-test-autoconfigure", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-test", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-autoconfigure", "version": {"requires": "3.2.5"}}], "files": [{"name": "spring-boot-test-autoconfigure-3.2.5.jar", "url": "spring-boot-test-autoconfigure-3.2.5.jar", "size": 219172, "sha512": "7e70e648f4a732c9e03f41e9bac72cc3bf7d00b2659799fa8e2721bb9d693a944c3c07dd9b8ccb4addf67ee7895a3a49dc9583bca4ef1f88b0234cd6ecae5574", "sha256": "4d9de12dbb8863be2ce23929e351d03e84e4687fe460a412ec68c6e11094dae4", "sha1": "678ff2604c4650db84dc7a63eb058fb322a58807", "md5": "080541ceb4ecaa285cae2d13bea97732"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-test", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-autoconfigure", "version": {"requires": "3.2.5"}}], "files": [{"name": "spring-boot-test-autoconfigure-3.2.5.jar", "url": "spring-boot-test-autoconfigure-3.2.5.jar", "size": 219172, "sha512": "7e70e648f4a732c9e03f41e9bac72cc3bf7d00b2659799fa8e2721bb9d693a944c3c07dd9b8ccb4addf67ee7895a3a49dc9583bca4ef1f88b0234cd6ecae5574", "sha256": "4d9de12dbb8863be2ce23929e351d03e84e4687fe460a412ec68c6e11094dae4", "sha1": "678ff2604c4650db84dc7a63eb058fb322a58807", "md5": "080541ceb4ecaa285cae2d13bea97732"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-test-autoconfigure-3.2.5-javadoc.jar", "url": "spring-boot-test-autoconfigure-3.2.5-javadoc.jar", "size": 488402, "sha512": "5a54fc810fd2dd208b4a83accd5cc1f2fa41de3a9c91aa5e00703b2249f953c71a96f4a188f40cf7db724028e3dd9266732a09606ea6dd9cc77d8545b93d1a8f", "sha256": "38e8ed4208f78e5cf6a7c4b96b8cd644421b382b0c9c4156f582003f0c561b80", "sha1": "0c7c767db50b6f9d4087d43133e89b3e466cda8b", "md5": "63ffcadc27259b64548afe2915092f26"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-test-autoconfigure-3.2.5-sources.jar", "url": "spring-boot-test-autoconfigure-3.2.5-sources.jar", "size": 221429, "sha512": "21e6d152061a8d62524b8a89f6beff071e2a9be03d0a24e2a34b1d8dfaf02f190ddd968bb29c35c3eb242d7badbafe818d0754bbc0f7eb10d2c084b5a7f845cc", "sha256": "01cbb412cae825553b69a12defb2715def71fd796e2dcc6f494e205f0c384f44", "sha1": "2a1f78fb7bb6cbb74fafbac69cd84b96c5ce2023", "md5": "de03413aa1d798016141cd3443cf0495"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-test-autoconfigure-3.2.5.jar", "url": "spring-boot-test-autoconfigure-3.2.5.jar", "size": 219172, "sha512": "7e70e648f4a732c9e03f41e9bac72cc3bf7d00b2659799fa8e2721bb9d693a944c3c07dd9b8ccb4addf67ee7895a3a49dc9583bca4ef1f88b0234cd6ecae5574", "sha256": "4d9de12dbb8863be2ce23929e351d03e84e4687fe460a412ec68c6e11094dae4", "sha1": "678ff2604c4650db84dc7a63eb058fb322a58807", "md5": "080541ceb4ecaa285cae2d13bea97732"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-test-autoconfigure-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-test-autoconfigure-3.2.5.jar", "url": "spring-boot-test-autoconfigure-3.2.5.jar", "size": 219172, "sha512": "7e70e648f4a732c9e03f41e9bac72cc3bf7d00b2659799fa8e2721bb9d693a944c3c07dd9b8ccb4addf67ee7895a3a49dc9583bca4ef1f88b0234cd6ecae5574", "sha256": "4d9de12dbb8863be2ce23929e351d03e84e4687fe460a412ec68c6e11094dae4", "sha1": "678ff2604c4650db84dc7a63eb058fb322a58807", "md5": "080541ceb4ecaa285cae2d13bea97732"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-test-autoconfigure-maven-optional", "version": "3.2.5"}]}]}