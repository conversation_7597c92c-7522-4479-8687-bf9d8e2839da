{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-buildpack-platform", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.14.2"}}, {"group": "com.fasterxml.jackson.module", "module": "jackson-module-parameter-names", "version": {"requires": "2.14.2"}}, {"group": "net.java.dev.jna", "module": "jna-platform", "version": {"requires": "5.13.0"}}, {"group": "org.apache.commons", "module": "commons-compress", "version": {"requires": "1.21"}}, {"group": "org.apache.httpcomponents.client5", "module": "httpclient5", "version": {"requires": "5.2.3"}}, {"group": "org.springframework", "module": "spring-core", "version": {"requires": "6.0.10"}}, {"group": "org.tomlj", "module": "to<PERSON><PERSON><PERSON>", "version": {"requires": "1.0.0"}}], "files": [{"name": "spring-boot-buildpack-platform-3.2.5.jar", "url": "spring-boot-buildpack-platform-3.2.5.jar", "size": 272057, "sha512": "2a3450ed98c8cf835b505e3b165eb84f6945698eb0b4e0fca8f13075be9a343d3c645af4249fc2d9d5a2304a767a75eee2d96dcdf45d5219586cc91f4b4eed2e", "sha256": "c8f94e6f3107ad3ccb3ca3526cf281436c21330a4a713bcdcea0a1512c91b55f", "sha1": "050fd5f0e577cc907bb8f2551a4f1c04a2f0c361", "md5": "74cd3eaef7715cb60d77040c0d3767b9"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.14.2"}}, {"group": "com.fasterxml.jackson.module", "module": "jackson-module-parameter-names", "version": {"requires": "2.14.2"}}, {"group": "net.java.dev.jna", "module": "jna-platform", "version": {"requires": "5.13.0"}}, {"group": "org.apache.commons", "module": "commons-compress", "version": {"requires": "1.21"}}, {"group": "org.apache.httpcomponents.client5", "module": "httpclient5", "version": {"requires": "5.2.3"}}, {"group": "org.springframework", "module": "spring-core", "version": {"requires": "6.0.10"}}, {"group": "org.tomlj", "module": "to<PERSON><PERSON><PERSON>", "version": {"requires": "1.0.0"}}], "files": [{"name": "spring-boot-buildpack-platform-3.2.5.jar", "url": "spring-boot-buildpack-platform-3.2.5.jar", "size": 272057, "sha512": "2a3450ed98c8cf835b505e3b165eb84f6945698eb0b4e0fca8f13075be9a343d3c645af4249fc2d9d5a2304a767a75eee2d96dcdf45d5219586cc91f4b4eed2e", "sha256": "c8f94e6f3107ad3ccb3ca3526cf281436c21330a4a713bcdcea0a1512c91b55f", "sha1": "050fd5f0e577cc907bb8f2551a4f1c04a2f0c361", "md5": "74cd3eaef7715cb60d77040c0d3767b9"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-buildpack-platform-3.2.5-javadoc.jar", "url": "spring-boot-buildpack-platform-3.2.5-javadoc.jar", "size": 394245, "sha512": "be36c7d824b996841c861460d38902859f23501150b43c749f0cd4b21c5537f0658ea306ce333f82807b3fa37d7cea57ecd721b0839ad685b3cb9f71f0f965e8", "sha256": "2132bf447694092593711425047f705396b22bd62a92df7bd68a8e66d00921cc", "sha1": "e8823e918a2f18f20e00dce4187509303b7f5b60", "md5": "bf80d18ce92e7bf6c7cd3263d78c41f6"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-buildpack-platform-3.2.5-sources.jar", "url": "spring-boot-buildpack-platform-3.2.5-sources.jar", "size": 169386, "sha512": "aef2f6627e45bc3034c195152a8d02cbc63132438e1886e2b0818de6acaa7eb4a7abee196b4c7706765dff44a121a3d8d974516f2944f7bb1fc5f79ee8359f2f", "sha256": "cd2dd0a2a43eb6f57b4ebaa27a18617b82f4a9231ec4d49f6667119be63d83e2", "sha1": "f905f22c12915904c64570702a88723d4df5f3b3", "md5": "1198cc3050c651d5016b1d3b6ee2512c"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-buildpack-platform-3.2.5.jar", "url": "spring-boot-buildpack-platform-3.2.5.jar", "size": 272057, "sha512": "2a3450ed98c8cf835b505e3b165eb84f6945698eb0b4e0fca8f13075be9a343d3c645af4249fc2d9d5a2304a767a75eee2d96dcdf45d5219586cc91f4b4eed2e", "sha256": "c8f94e6f3107ad3ccb3ca3526cf281436c21330a4a713bcdcea0a1512c91b55f", "sha1": "050fd5f0e577cc907bb8f2551a4f1c04a2f0c361", "md5": "74cd3eaef7715cb60d77040c0d3767b9"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-buildpack-platform-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-buildpack-platform-3.2.5.jar", "url": "spring-boot-buildpack-platform-3.2.5.jar", "size": 272057, "sha512": "2a3450ed98c8cf835b505e3b165eb84f6945698eb0b4e0fca8f13075be9a343d3c645af4249fc2d9d5a2304a767a75eee2d96dcdf45d5219586cc91f4b4eed2e", "sha256": "c8f94e6f3107ad3ccb3ca3526cf281436c21330a4a713bcdcea0a1512c91b55f", "sha1": "050fd5f0e577cc907bb8f2551a4f1c04a2f0c361", "md5": "74cd3eaef7715cb60d77040c0d3767b9"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-buildpack-platform-maven-optional", "version": "3.2.5"}]}]}