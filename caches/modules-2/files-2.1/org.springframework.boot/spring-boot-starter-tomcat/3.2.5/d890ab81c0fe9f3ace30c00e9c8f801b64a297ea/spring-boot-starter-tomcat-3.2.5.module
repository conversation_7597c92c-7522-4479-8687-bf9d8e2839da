{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-starter-tomcat", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "jakarta.annotation", "module": "jakarta.annotation-api", "version": {"requires": "2.1.1"}}, {"group": "org.apache.tomcat.embed", "module": "tomcat-embed-core", "version": {"requires": "10.1.20"}, "excludes": [{"group": "org.apache.tomcat", "module": "tomcat-annotations-api"}]}, {"group": "org.apache.tomcat.embed", "module": "tomcat-embed-el", "version": {"requires": "10.1.20"}}, {"group": "org.apache.tomcat.embed", "module": "tomcat-embed-websocket", "version": {"requires": "10.1.20"}, "excludes": [{"group": "org.apache.tomcat", "module": "tomcat-annotations-api"}]}], "files": [{"name": "spring-boot-starter-tomcat-3.2.5.jar", "url": "spring-boot-starter-tomcat-3.2.5.jar", "size": 4793, "sha512": "cabec83e9645f4b413b6544f61a81e7b76f3602486d2b21d78bf9816e223b1b5ccbb17c562cfe0b219b650ee85d55801585f7fda8d17b19f2bd7e3b194f70559", "sha256": "f7e4356319eb689cb023441733d0cce10a63f99183a1144efadeaae96b84a7fa", "sha1": "a40ebfa6becb35b419b37e49e33b2822e22cf42a", "md5": "fb7a94a436fc90e4f7427fbb18f4c7b5"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "jakarta.annotation", "module": "jakarta.annotation-api", "version": {"requires": "2.1.1"}}, {"group": "org.apache.tomcat.embed", "module": "tomcat-embed-core", "version": {"requires": "10.1.20"}, "excludes": [{"group": "org.apache.tomcat", "module": "tomcat-annotations-api"}]}, {"group": "org.apache.tomcat.embed", "module": "tomcat-embed-el", "version": {"requires": "10.1.20"}}, {"group": "org.apache.tomcat.embed", "module": "tomcat-embed-websocket", "version": {"requires": "10.1.20"}, "excludes": [{"group": "org.apache.tomcat", "module": "tomcat-annotations-api"}]}], "files": [{"name": "spring-boot-starter-tomcat-3.2.5.jar", "url": "spring-boot-starter-tomcat-3.2.5.jar", "size": 4793, "sha512": "cabec83e9645f4b413b6544f61a81e7b76f3602486d2b21d78bf9816e223b1b5ccbb17c562cfe0b219b650ee85d55801585f7fda8d17b19f2bd7e3b194f70559", "sha256": "f7e4356319eb689cb023441733d0cce10a63f99183a1144efadeaae96b84a7fa", "sha1": "a40ebfa6becb35b419b37e49e33b2822e22cf42a", "md5": "fb7a94a436fc90e4f7427fbb18f4c7b5"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-starter-tomcat-3.2.5.jar", "url": "spring-boot-starter-tomcat-3.2.5.jar", "size": 4793, "sha512": "cabec83e9645f4b413b6544f61a81e7b76f3602486d2b21d78bf9816e223b1b5ccbb17c562cfe0b219b650ee85d55801585f7fda8d17b19f2bd7e3b194f70559", "sha256": "f7e4356319eb689cb023441733d0cce10a63f99183a1144efadeaae96b84a7fa", "sha1": "a40ebfa6becb35b419b37e49e33b2822e22cf42a", "md5": "fb7a94a436fc90e4f7427fbb18f4c7b5"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-tomcat-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-tomcat-3.2.5.jar", "url": "spring-boot-starter-tomcat-3.2.5.jar", "size": 4793, "sha512": "cabec83e9645f4b413b6544f61a81e7b76f3602486d2b21d78bf9816e223b1b5ccbb17c562cfe0b219b650ee85d55801585f7fda8d17b19f2bd7e3b194f70559", "sha256": "f7e4356319eb689cb023441733d0cce10a63f99183a1144efadeaae96b84a7fa", "sha1": "a40ebfa6becb35b419b37e49e33b2822e22cf42a", "md5": "fb7a94a436fc90e4f7427fbb18f4c7b5"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-tomcat-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-tomcat-3.2.5-javadoc.jar", "url": "spring-boot-starter-tomcat-3.2.5-javadoc.jar", "size": 4743, "sha512": "8ec53b0b6ae3d2d80deec4180af43540eee64b7d6a5bf01b2e0a7de78fc9c7ef4b4f5cdcef31d2aa13d8ba1400522208712b146e9851624deb7f217e4ddd74ed", "sha256": "0ab3deee85dd861bb03f415cd185ee11c3d573a23a87710c182afb436108bd06", "sha1": "c37f7b76f98d5737926847ddb6cc9bf08288dc2f", "md5": "146e1735715c6d9eacdb3fd5c6acdd4f"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-tomcat-3.2.5-sources.jar", "url": "spring-boot-starter-tomcat-3.2.5-sources.jar", "size": 4742, "sha512": "9d572052e58d96611a5e3e965897489ddb5879a132ce763033a98a44cac4273329253d5face2fc81a00f41cd678e13f0827053d024f64fb829850f50e8a321bc", "sha256": "328186d57d25d2459b9c385fb8e91b333f3bb574baf4ece9fb5ff93d89b29166", "sha1": "3d49eab8171e38e6958f5c2fc8c23adf7c81bd05", "md5": "14b957e98b806eae1f6528390dbb425b"}]}]}