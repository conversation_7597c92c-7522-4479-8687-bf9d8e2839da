{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-starter-data-jpa", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter-aop", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-starter-jdbc", "version": {"requires": "3.2.5"}}, {"group": "org.hibernate.orm", "module": "hibernate-core", "version": {"requires": "6.4.4.Final"}}, {"group": "org.springframework.data", "module": "spring-data-jpa", "version": {"requires": "3.2.5"}}, {"group": "org.springframework", "module": "spring-aspects", "version": {"requires": "6.1.6"}}], "files": [{"name": "spring-boot-starter-data-jpa-3.2.5.jar", "url": "spring-boot-starter-data-jpa-3.2.5.jar", "size": 4761, "sha512": "17dfe7dc394da471220cc767a08ff22102cadacbb23b6fd39e5ef7d095b0c9ee75c39c803b21aec21a15f2c9fe1c3a1f9525335f42e43ac91b92f8fe7d95b899", "sha256": "4c02f7d6b3ce24c28d44091a3d20317063612c9cbff6189e61c65a65f7b57e25", "sha1": "99c1272c135f1c44b0c94ed0b65dca9b201323b7", "md5": "47b58c896a24c955610c4dbddc19d535"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter-aop", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-starter-jdbc", "version": {"requires": "3.2.5"}}, {"group": "org.hibernate.orm", "module": "hibernate-core", "version": {"requires": "6.4.4.Final"}}, {"group": "org.springframework.data", "module": "spring-data-jpa", "version": {"requires": "3.2.5"}}, {"group": "org.springframework", "module": "spring-aspects", "version": {"requires": "6.1.6"}}], "files": [{"name": "spring-boot-starter-data-jpa-3.2.5.jar", "url": "spring-boot-starter-data-jpa-3.2.5.jar", "size": 4761, "sha512": "17dfe7dc394da471220cc767a08ff22102cadacbb23b6fd39e5ef7d095b0c9ee75c39c803b21aec21a15f2c9fe1c3a1f9525335f42e43ac91b92f8fe7d95b899", "sha256": "4c02f7d6b3ce24c28d44091a3d20317063612c9cbff6189e61c65a65f7b57e25", "sha1": "99c1272c135f1c44b0c94ed0b65dca9b201323b7", "md5": "47b58c896a24c955610c4dbddc19d535"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-starter-data-jpa-3.2.5.jar", "url": "spring-boot-starter-data-jpa-3.2.5.jar", "size": 4761, "sha512": "17dfe7dc394da471220cc767a08ff22102cadacbb23b6fd39e5ef7d095b0c9ee75c39c803b21aec21a15f2c9fe1c3a1f9525335f42e43ac91b92f8fe7d95b899", "sha256": "4c02f7d6b3ce24c28d44091a3d20317063612c9cbff6189e61c65a65f7b57e25", "sha1": "99c1272c135f1c44b0c94ed0b65dca9b201323b7", "md5": "47b58c896a24c955610c4dbddc19d535"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-data-jpa-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-data-jpa-3.2.5.jar", "url": "spring-boot-starter-data-jpa-3.2.5.jar", "size": 4761, "sha512": "17dfe7dc394da471220cc767a08ff22102cadacbb23b6fd39e5ef7d095b0c9ee75c39c803b21aec21a15f2c9fe1c3a1f9525335f42e43ac91b92f8fe7d95b899", "sha256": "4c02f7d6b3ce24c28d44091a3d20317063612c9cbff6189e61c65a65f7b57e25", "sha1": "99c1272c135f1c44b0c94ed0b65dca9b201323b7", "md5": "47b58c896a24c955610c4dbddc19d535"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-data-jpa-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-data-jpa-3.2.5-javadoc.jar", "url": "spring-boot-starter-data-jpa-3.2.5-javadoc.jar", "size": 4747, "sha512": "4e8b04262fe1be19fd2d71589c2e95998a8c76b73b7320c3c0ab49f616d8d39106c3e9ae35646e42f41007bdc6bb95a5d277682652f4c25cd2ae8170712151fe", "sha256": "24a22ad7d491e489c02366354e5f015fd58b42d8c66e8d31b684ee0221a69d9a", "sha1": "afb13a6fb7256d2a1cc1f28ad50a0d2a976cf1e6", "md5": "5a58514f51517ceef5c1021e2f079f97"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-data-jpa-3.2.5-sources.jar", "url": "spring-boot-starter-data-jpa-3.2.5-sources.jar", "size": 4746, "sha512": "fbb95b77c5f3331817d546029bd90a7141de607cebd330edd9eac317de691ae00f499713cb43305dcad05545743954936e4bb058617f9e6fcf0e74148562bc17", "sha256": "632f2c2e3d9e42a10578d8bbd6bc38567982cf7e2b2b027a2c765ad7abfa2e3a", "sha1": "2954ff1acf4c590dd4db5090794e7bcaf0928575", "md5": "d5fa570c7805952b589a6b63bd9ede38"}]}]}