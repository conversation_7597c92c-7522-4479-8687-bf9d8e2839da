{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-actuator-autoconfigure", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-actuator", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-autoconfigure", "version": {"requires": "3.2.5"}}], "files": [{"name": "spring-boot-actuator-autoconfigure-3.2.5.jar", "url": "spring-boot-actuator-autoconfigure-3.2.5.jar", "size": 755624, "sha512": "5269c7d6f8ad654400b0f4e9834d721b2f7f20ebd6c7f99e2c3827c64cbc424dea50498d26d86fb233b9ee5e7db5765a998910fb41df6605155ba4d120818f94", "sha256": "76cf22b94834de0bf57e0ed2e26e30d9ee4cbedf13aef5bd52a5417cfe6d9957", "sha1": "aa58ac33da22febe8ea6d179eb0bf3f76ed2850d", "md5": "889697ca0ac604d02458921acd8e751c"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.datatype", "module": "jackson-datatype-jsr310", "version": {"requires": "2.15.4"}}, {"group": "org.springframework.boot", "module": "spring-boot-actuator", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-autoconfigure", "version": {"requires": "3.2.5"}}], "files": [{"name": "spring-boot-actuator-autoconfigure-3.2.5.jar", "url": "spring-boot-actuator-autoconfigure-3.2.5.jar", "size": 755624, "sha512": "5269c7d6f8ad654400b0f4e9834d721b2f7f20ebd6c7f99e2c3827c64cbc424dea50498d26d86fb233b9ee5e7db5765a998910fb41df6605155ba4d120818f94", "sha256": "76cf22b94834de0bf57e0ed2e26e30d9ee4cbedf13aef5bd52a5417cfe6d9957", "sha1": "aa58ac33da22febe8ea6d179eb0bf3f76ed2850d", "md5": "889697ca0ac604d02458921acd8e751c"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-actuator-autoconfigure-3.2.5.jar", "url": "spring-boot-actuator-autoconfigure-3.2.5.jar", "size": 755624, "sha512": "5269c7d6f8ad654400b0f4e9834d721b2f7f20ebd6c7f99e2c3827c64cbc424dea50498d26d86fb233b9ee5e7db5765a998910fb41df6605155ba4d120818f94", "sha256": "76cf22b94834de0bf57e0ed2e26e30d9ee4cbedf13aef5bd52a5417cfe6d9957", "sha1": "aa58ac33da22febe8ea6d179eb0bf3f76ed2850d", "md5": "889697ca0ac604d02458921acd8e751c"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-actuator-autoconfigure-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-actuator-autoconfigure-3.2.5.jar", "url": "spring-boot-actuator-autoconfigure-3.2.5.jar", "size": 755624, "sha512": "5269c7d6f8ad654400b0f4e9834d721b2f7f20ebd6c7f99e2c3827c64cbc424dea50498d26d86fb233b9ee5e7db5765a998910fb41df6605155ba4d120818f94", "sha256": "76cf22b94834de0bf57e0ed2e26e30d9ee4cbedf13aef5bd52a5417cfe6d9957", "sha1": "aa58ac33da22febe8ea6d179eb0bf3f76ed2850d", "md5": "889697ca0ac604d02458921acd8e751c"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-actuator-autoconfigure-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-actuator-autoconfigure-3.2.5-javadoc.jar", "url": "spring-boot-actuator-autoconfigure-3.2.5-javadoc.jar", "size": 1342317, "sha512": "f3ae31914b25a8f2f6c0b989cc7c1353c958e436e5d6051dd7c251497c062c755b4428743fd395081843a58cd5947c3e1e7c2083ee89fa3d951a7ae6e2345817", "sha256": "3978d8dd1ab7be3b56846d405deca7ec386cc22bced51c84aa083d3cd6583aee", "sha1": "da10fdd20442bdb0887bf15659131a57e4e36166", "md5": "f8344a44bdc0383123921104d6d21868"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-actuator-autoconfigure-3.2.5-sources.jar", "url": "spring-boot-actuator-autoconfigure-3.2.5-sources.jar", "size": 534208, "sha512": "39aa8e12ae082299031d8b799469c3267a058da1fd4bddf0f51e26777b54072f96141f1aef72fd422f1fe73a0a347531f405fd62031280d4a3eee8a8869234dc", "sha256": "7e9518e29148cca37806fca4ab3ea90792b6f9d747d937588ebfe7de6b679498", "sha1": "1634f82395b89cd576cdcf523494f634c2f51894", "md5": "222c1d981b2a17a76c50a86b8fd93c1c"}]}]}