{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-starter-json", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "org.springframework", "module": "spring-web", "version": {"requires": "6.1.6"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.datatype", "module": "jackson-datatype-jdk8", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.datatype", "module": "jackson-datatype-jsr310", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.module", "module": "jackson-module-parameter-names", "version": {"requires": "2.15.4"}}], "files": [{"name": "spring-boot-starter-json-3.2.5.jar", "url": "spring-boot-starter-json-3.2.5.jar", "size": 4747, "sha512": "f1333f6b79858b2a320eac98e495f06b4968d9b4ec9ee4291ed19900f987e3aad475e8d905d7bf6b47e49f87180db8e358e023bf34bf61d20c826a355d026c5f", "sha256": "e78c3620b5dc8f50ffa51997b81c4e5b51562746effe54a9f8db740a15e1e7f4", "sha1": "6df311af4c242eb95c3526f48ab4f31c384a247e", "md5": "97e0b707543cb5d80e5bfd1b0dd7030a"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "org.springframework", "module": "spring-web", "version": {"requires": "6.1.6"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.datatype", "module": "jackson-datatype-jdk8", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.datatype", "module": "jackson-datatype-jsr310", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.module", "module": "jackson-module-parameter-names", "version": {"requires": "2.15.4"}}], "files": [{"name": "spring-boot-starter-json-3.2.5.jar", "url": "spring-boot-starter-json-3.2.5.jar", "size": 4747, "sha512": "f1333f6b79858b2a320eac98e495f06b4968d9b4ec9ee4291ed19900f987e3aad475e8d905d7bf6b47e49f87180db8e358e023bf34bf61d20c826a355d026c5f", "sha256": "e78c3620b5dc8f50ffa51997b81c4e5b51562746effe54a9f8db740a15e1e7f4", "sha1": "6df311af4c242eb95c3526f48ab4f31c384a247e", "md5": "97e0b707543cb5d80e5bfd1b0dd7030a"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-starter-json-3.2.5.jar", "url": "spring-boot-starter-json-3.2.5.jar", "size": 4747, "sha512": "f1333f6b79858b2a320eac98e495f06b4968d9b4ec9ee4291ed19900f987e3aad475e8d905d7bf6b47e49f87180db8e358e023bf34bf61d20c826a355d026c5f", "sha256": "e78c3620b5dc8f50ffa51997b81c4e5b51562746effe54a9f8db740a15e1e7f4", "sha1": "6df311af4c242eb95c3526f48ab4f31c384a247e", "md5": "97e0b707543cb5d80e5bfd1b0dd7030a"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-json-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-json-3.2.5.jar", "url": "spring-boot-starter-json-3.2.5.jar", "size": 4747, "sha512": "f1333f6b79858b2a320eac98e495f06b4968d9b4ec9ee4291ed19900f987e3aad475e8d905d7bf6b47e49f87180db8e358e023bf34bf61d20c826a355d026c5f", "sha256": "e78c3620b5dc8f50ffa51997b81c4e5b51562746effe54a9f8db740a15e1e7f4", "sha1": "6df311af4c242eb95c3526f48ab4f31c384a247e", "md5": "97e0b707543cb5d80e5bfd1b0dd7030a"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-json-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-json-3.2.5-javadoc.jar", "url": "spring-boot-starter-json-3.2.5-javadoc.jar", "size": 4743, "sha512": "e8e626042e03cf1f24f2ea4c69aef5afdf8c690ff847d9a74fce7ab767a679d2df9ed3474606d5c2492ade84ad04c42659ec4c9d0f91a26335c962b04c76995b", "sha256": "09223d828bc4615952a4024755816b0d11160d68c5ced94cdad498465f5fc125", "sha1": "356dfefb5cbc16d5ad49c873af56f0436d4309d6", "md5": "358583d44205c0d7f4bb910489a1c3ad"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-json-3.2.5-sources.jar", "url": "spring-boot-starter-json-3.2.5-sources.jar", "size": 4741, "sha512": "6650e975cac1eb8eafb17ec13d8877ef9b627613353649ed845cb3d75cbcc00c156226665516cdc20017b93f2a440eb5eb3904c40fc2cc9030a2c6345b58c4c0", "sha256": "44f02259af4fcd6b3688cedc6fa87c5f1f3b406bea2e939887446ba39369d615", "sha1": "381ec4db3d4fbe79c9ba04dfe8733145937bc53c", "md5": "c901dd5a786af6bcb0491fd35d70ff89"}]}]}