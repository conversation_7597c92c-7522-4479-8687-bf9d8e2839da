{"formatVersion": "1.1", "component": {"group": "org.springframework.boot", "module": "spring-boot-starter-webflux", "version": "3.2.5", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "7.6.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-starter-json", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-starter-reactor-netty", "version": {"requires": "3.2.5"}}, {"group": "org.springframework", "module": "spring-web", "version": {"requires": "6.1.6"}}, {"group": "org.springframework", "module": "spring-webflux", "version": {"requires": "6.1.6"}}], "files": [{"name": "spring-boot-starter-webflux-3.2.5.jar", "url": "spring-boot-starter-webflux-3.2.5.jar", "size": 4788, "sha512": "97917d39b90c0e5809290ce1d99e66dd8ad1152cc8561dc4084042ef56a56e48b59705fd083da9bb3f08dc0d196c2ba6bc5147b9161162b6f080ffa65de716b6", "sha256": "13a277322206b85508bdfa95a3a52df5b7177729a6e90fb4e413e8b4cd0a2714", "sha1": "a555a210408e8c7dc085889cd3c7b2404a7137f7", "md5": "e11ce9825d9e8d1ccb77728d819978f0"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.springframework.boot", "module": "spring-boot-starter", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-starter-json", "version": {"requires": "3.2.5"}}, {"group": "org.springframework.boot", "module": "spring-boot-starter-reactor-netty", "version": {"requires": "3.2.5"}}, {"group": "org.springframework", "module": "spring-web", "version": {"requires": "6.1.6"}}, {"group": "org.springframework", "module": "spring-webflux", "version": {"requires": "6.1.6"}}], "files": [{"name": "spring-boot-starter-webflux-3.2.5.jar", "url": "spring-boot-starter-webflux-3.2.5.jar", "size": 4788, "sha512": "97917d39b90c0e5809290ce1d99e66dd8ad1152cc8561dc4084042ef56a56e48b59705fd083da9bb3f08dc0d196c2ba6bc5147b9161162b6f080ffa65de716b6", "sha256": "13a277322206b85508bdfa95a3a52df5b7177729a6e90fb4e413e8b4cd0a2714", "sha1": "a555a210408e8c7dc085889cd3c7b2404a7137f7", "md5": "e11ce9825d9e8d1ccb77728d819978f0"}]}, {"name": "mavenOptionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "spring-boot-starter-webflux-3.2.5.jar", "url": "spring-boot-starter-webflux-3.2.5.jar", "size": 4788, "sha512": "97917d39b90c0e5809290ce1d99e66dd8ad1152cc8561dc4084042ef56a56e48b59705fd083da9bb3f08dc0d196c2ba6bc5147b9161162b6f080ffa65de716b6", "sha256": "13a277322206b85508bdfa95a3a52df5b7177729a6e90fb4e413e8b4cd0a2714", "sha1": "a555a210408e8c7dc085889cd3c7b2404a7137f7", "md5": "e11ce9825d9e8d1ccb77728d819978f0"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-webflux-maven-optional", "version": "3.2.5"}]}, {"name": "mavenOptionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-webflux-3.2.5.jar", "url": "spring-boot-starter-webflux-3.2.5.jar", "size": 4788, "sha512": "97917d39b90c0e5809290ce1d99e66dd8ad1152cc8561dc4084042ef56a56e48b59705fd083da9bb3f08dc0d196c2ba6bc5147b9161162b6f080ffa65de716b6", "sha256": "13a277322206b85508bdfa95a3a52df5b7177729a6e90fb4e413e8b4cd0a2714", "sha1": "a555a210408e8c7dc085889cd3c7b2404a7137f7", "md5": "e11ce9825d9e8d1ccb77728d819978f0"}], "capabilities": [{"group": "org.springframework.boot", "name": "spring-boot-starter-webflux-maven-optional", "version": "3.2.5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-webflux-3.2.5-javadoc.jar", "url": "spring-boot-starter-webflux-3.2.5-javadoc.jar", "size": 4745, "sha512": "a05d37707c388edac48bcd612c792b93616496e526cda6bf5700adc1ba78fd200dba316b0579b8ad1aff1e13b112f6f1dd0e1f0478654f4b9828e1791d1facf8", "sha256": "629958e236153c103db0bc73fb5d2c73e406953089303b480c2c546b5f5f0d29", "sha1": "1593046ca7fde049bf17b00a26332ff7e0b9328f", "md5": "7350033626cec0dfc5faf43ca600af84"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-boot-starter-webflux-3.2.5-sources.jar", "url": "spring-boot-starter-webflux-3.2.5-sources.jar", "size": 4744, "sha512": "38a76e3fc7ee84b4bae87ec87f9fa03c8218ecccc8830db823e60ab85d1cdc5054eef425e9307c674991051110faad24c4d73694363793679ada5d7a8042ac9e", "sha256": "94cca42497c329ed4a91866ecafe86b9d9b07fdd3bc0028881ad268a3b05862f", "sha1": "6dcda0af98ed77b5ccff4a316fc2096b9a1f59ed", "md5": "32c0c98fccb7dfe825b4e38d9545e022"}]}]}