<?xml version='1.0' encoding='UTF-8'?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.fasterxml.jackson.module</groupId>
    <artifactId>jackson-module-scala_2.13</artifactId>
    <packaging>bundle</packaging>
    <description>jackson-module-scala</description>
    <version>2.15.4</version>
    <name>jackson-module-scala</name>
    <organization>
        <name>com.fasterxml.jackson.module</name>
    </organization>
    <scm>
        <url>https://github.com/FasterXML/jackson-module-scala</url>
        <connection>scm:git:https://github.com/FasterXML/jackson-module-scala.git</connection>
        <developerConnection>scm:git:**************:FasterXML/jackson-module-scala.git</developerConnection>
    </scm>
    <url>https://github.com/FasterXML/jackson-module-scala</url>
    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <developers>
        <developer>
            <id>tatu</id>
            <name>Tatu Saloranta</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>christopher</id>
            <name>Christopher Currie</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>mbknor</id>
            <name>Morten Kjetland</name>
            <email><EMAIL></email>
        </developer>
        <developer>
            <id>pjfanning</id>
            <name>PJ Fanning</name>
            <url>https://github.com/pjfanning</url>
        </developer>
    </developers>
    <contributors>
        <contributor>
            <name>Nathaniel Bauernfeind</name>
        </contributor>
        <contributor>
            <name>Anton Panasenko</name>
        </contributor>
    </contributors>
    <properties>
        <info.apiURL>https://www.javadoc.io/doc/com.fasterxml.jackson.module/jackson-module-scala_2.13/2.15.4/</info.apiURL>
    </properties>
    <dependencies>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
            <version>2.13.10</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>2.15.4</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>2.15.4</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>2.15.4</version>
        </dependency>
        <dependency>
            <groupId>com.thoughtworks.paranamer</groupId>
            <artifactId>paranamer</artifactId>
            <version>2.8</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-joda</artifactId>
            <version>2.15.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-guava</artifactId>
            <version>2.15.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.datatype</groupId>
            <artifactId>jackson-datatype-jdk8</artifactId>
            <version>2.15.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.jaxrs</groupId>
            <artifactId>jackson-jaxrs-json-provider</artifactId>
            <version>2.15.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.module</groupId>
            <artifactId>jackson-module-jsonSchema</artifactId>
            <version>2.15.4</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>javax.ws.rs</groupId>
            <artifactId>javax.ws.rs-api</artifactId>
            <version>2.1.1</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>io.swagger</groupId>
            <artifactId>swagger-core</artifactId>
            <version>1.6.8</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_2.13</artifactId>
            <version>3.2.15</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>