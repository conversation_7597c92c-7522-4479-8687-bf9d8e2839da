<?xml version="1.0" encoding="UTF-8"?>
<!--
   ====================================================================
   Licensed to the Apache Software Foundation (ASF) under one
   or more contributor license agreements.  See the NOTICE file
   distributed with this work for additional information
   regarding copyright ownership.  The ASF licenses this file
   to you under the Apache License, Version 2.0 (the
   "License"); you may not use this file except in compliance
   with the License.  You may obtain a copy of the License at

     http://www.apache.org/licenses/LICENSE-2.0

   Unless required by applicable law or agreed to in writing,
   software distributed under the License is distributed on an
   "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY
   KIND, either express or implied.  See the License for the
   specific language governing permissions and limitations
   under the License.
   ====================================================================

   This software consists of voluntary contributions made by many
   individuals on behalf of the Apache Software Foundation.  For more
   information on the Apache Software Foundation, please see
   <http://www.apache.org />.
 --><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>org.apache.httpcomponents</groupId>
    <artifactId>httpcomponents-parent</artifactId>
    <version>13</version>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.apache.httpcomponents.core5</groupId>
  <artifactId>httpcore5-parent</artifactId>
  <name>Apache HttpComponents Core Parent</name>
  <version>5.2.4</version>
  <description>Apache HttpComponents Core is a library of components for building HTTP enabled services</description>
  <url>https://hc.apache.org/httpcomponents-core-5.2.x/${project.version}/</url>
  <inceptionYear>2005</inceptionYear>
  <packaging>pom</packaging>

  <issueManagement>
    <system>Jira</system>
    <url>https://issues.apache.org/jira/browse/HTTPCORE</url>
  </issueManagement>

  <scm>
    <connection>scm:git:https://gitbox.apache.org/repos/asf/httpcomponents-core.git</connection>
    <developerConnection>scm:git:https://gitbox.apache.org/repos/asf/httpcomponents-core.git</developerConnection>
    <url>https://github.com/apache/httpcomponents-core/tree/${project.scm.tag}</url>
    <tag>5.2.4</tag>
  </scm>

  <distributionManagement>
    <site>
      <id>apache.website</id>
      <name>Apache HttpComponents Website</name>
      <url>scm:svn:https://svn.apache.org/repos/asf/httpcomponents/site/components/httpcomponents-core-5.2.x/LATEST/</url>
    </site>
  </distributionManagement>

  <modules>
    <module>httpcore5</module>
    <module>httpcore5-h2</module>
    <module>httpcore5-reactive</module>
    <module>httpcore5-testing</module>
  </modules>

  <properties>
    <!-- Override parent 7 setting for deprecation (only - other settings stand)-->
    <maven.compiler.source>1.8</maven.compiler.source>
    <maven.compiler.target>1.8</maven.compiler.target>
    <maven.compiler.showDeprecation>true</maven.compiler.showDeprecation>
    <conscrypt.version>2.5.2</conscrypt.version>
    <junit.version>5.9.3</junit.version>
    <hamcrest.version>2.2</hamcrest.version>
    <junit.migrationsupport.version>5.0.0</junit.migrationsupport.version>
    <mockito.version>4.11.0</mockito.version>
    <slf4j.version>1.7.36</slf4j.version>
    <log4j.version>2.19.0</log4j.version>
    <rxjava.version>2.2.21</rxjava.version>
    <rxjava3.version>3.1.6</rxjava3.version>
    <api.comparison.version>5.2</api.comparison.version>
    <hc.animal-sniffer.signature.ignores>javax.net.ssl.SSLEngine,javax.net.ssl.SSLParameters,java.nio.ByteBuffer,java.nio.CharBuffer</hc.animal-sniffer.signature.ignores>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5-h2</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5-reactive</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.httpcomponents.core5</groupId>
        <artifactId>httpcore5-testing</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.conscrypt</groupId>
        <artifactId>conscrypt-openjdk-uber</artifactId>
        <version>${conscrypt.version}</version>
      </dependency>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${junit.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest</artifactId>
        <version>${hamcrest.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.mockito</groupId>
        <artifactId>mockito-core</artifactId>
        <version>${mockito.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>${slf4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-slf4j-impl</artifactId>
        <version>${log4j.version}</version>
      </dependency>
      <dependency>
        <groupId>org.apache.logging.log4j</groupId>
        <artifactId>log4j-core</artifactId>
        <version>${log4j.version}</version>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <defaultGoal>clean verify</defaultGoal>
    <plugins>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestEntries>
              <Automatic-Module-Name>${Automatic-Module-Name}</Automatic-Module-Name>
              <Implementation-URL>${project.url}</Implementation-URL>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <links>
            <link>${project.url}/httpcore5/apidocs/</link>
            <link>${project.url}/httpcore5-h2/apidocs/</link>
          </links>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <executions>
          <execution>
            <id>validate-main</id>
            <phase>validate</phase>
            <configuration>
              <configLocation>hc-stylecheck/default.xml</configLocation>
              <headerLocation>hc-stylecheck/asl2.header</headerLocation>
              <consoleOutput>true</consoleOutput>
              <failsOnError>true</failsOnError>
              <linkXRef>false</linkXRef>
              <sourceDirectories>
                <sourceDirectory>${basedir}/src/main</sourceDirectory>
                <sourceDirectory>${basedir}/src/test</sourceDirectory>
              </sourceDirectories>
            </configuration>
            <goals>
              <goal>checkstyle</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.github.siom79.japicmp</groupId>
        <artifactId>japicmp-maven-plugin</artifactId>
        <configuration>
          <oldVersion>
            <dependency>
              <groupId>${project.groupId}</groupId>
              <artifactId>${project.artifactId}</artifactId>
              <version>${api.comparison.version}</version>
              <type>jar</type>
            </dependency>
          </oldVersion>
          <newVersion>
            <file>
              <path>${project.build.directory}/${project.artifactId}-${project.version}.${project.packaging}</path>
            </file>
          </newVersion>
          <parameter>
            <breakBuildOnBinaryIncompatibleModifications>true</breakBuildOnBinaryIncompatibleModifications>
            <breakBuildOnSourceIncompatibleModifications>true</breakBuildOnSourceIncompatibleModifications>
            <overrideCompatibilityChangeParameters>
              <overrideCompatibilityChangeParameter>
                <compatibilityChange>METHOD_NEW_DEFAULT</compatibilityChange>
                <binaryCompatible>true</binaryCompatible>
                <sourceCompatible>true</sourceCompatible>
              </overrideCompatibilityChangeParameter>
            </overrideCompatibilityChangeParameters>
            <excludes>
              <exclude>@org.apache.hc.core5.annotation.Internal</exclude>
            </excludes>
          </parameter>
        </configuration>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>cmp</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.rat</groupId>
        <artifactId>apache-rat-plugin</artifactId>
        <executions>
          <execution>
            <phase>verify</phase>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <excludes>
            <exclude>**/.checkstyle</exclude>
            <exclude>**/.pmd</exclude>
            <exclude>**/*.iml</exclude>
            <exclude>**/.externalToolBuilders/**</exclude>
            <exclude>maven-eclipse.xml</exclude>
            <exclude>src/docbkx/resources/**</exclude>
            <exclude>src/test/resources/*.truststore</exclude>
            <exclude>src/test/resources/*.p12</exclude>
            <exclude>**/.dockerignore</exclude>
            <!-- Eclipse can leave some file in 'bin' when an imported project misconfigured. -->
            <exclude>bin/**</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>

      <plugin>
        <artifactId>maven-project-info-reports-plugin</artifactId>
        <inherited>false</inherited>
        <reportSets>
          <reportSet>
            <reports>
              <report>index</report>
              <report>dependency-info</report>
              <report>dependency-management</report>
              <report>issue-management</report>
              <report>licenses</report>
              <report>mailing-lists</report>
              <report>scm</report>
              <report>summary</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <quiet>true</quiet>
          <links>
            <link>${project.url}/httpcore5/apidocs/</link>
            <link>${project.url}/httpcore5-h2/apidocs/</link>
          </links>
          <notimestamp>true</notimestamp>
          <groups>
            <group>
              <title>Apache HttpCore HTTP/1.1</title>
              <packages>org.apache.hc.core5*</packages>
            </group>
            <group>
              <title>Apache HttpCore HTTP/2</title>
              <packages>org.apache.hc.core5.http2*</packages>
            </group>
            <group>
              <title>Apache HttpCore Reactive Streams Bindings</title>
              <packages>org.apache.hc.core5.reactive*</packages>
            </group>
            <group>
              <title>Apache HttpCore Testing</title>
              <packages>org.apache.hc.core5.testing*:org.apache.hc.core5.benchmark*</packages>
            </group>
          </groups>
        </configuration>
        <reportSets>
          <reportSet>
            <reports>
              <report>javadoc</report>
              <report>aggregate</report>
            </reports>
          </reportSet>
        </reportSets>
      </plugin>
      <plugin>
        <groupId>com.github.siom79.japicmp</groupId>
        <artifactId>japicmp-maven-plugin</artifactId>
        <reportSets>
          <reportSet>
            <reports>
              <report>cmp-report</report>
            </reports>
          </reportSet>
        </reportSets>
        <configuration>
          <oldVersion>
            <dependency>
              <groupId>${project.groupId}</groupId>
              <artifactId>${project.artifactId}</artifactId>
              <version>${api.comparison.version}</version>
              <type>jar</type>
            </dependency>
          </oldVersion>
          <newVersion>
            <file>
              <path>${project.build.directory}/${project.artifactId}-${project.version}.${project.packaging}</path>
            </file>
          </newVersion>
          <parameter>
            <excludes>
              <exclude>@org.apache.hc.core5.annotation.Internal</exclude>
            </excludes>
            <ignoreMissingClasses>true</ignoreMissingClasses>
          </parameter>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jxr-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-surefire-report-plugin</artifactId>
      </plugin>
    </plugins>
  </reporting>

</project>