<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!--
  ~ Copyright Amazon.com, Inc. or its affiliates. All Rights Reserved.
  ~
  ~ Licensed under the Apache License, Version 2.0 (the "License").
  ~ You may not use this file except in compliance with the License.
  ~ A copy of the License is located at
  ~
  ~  http://aws.amazon.com/apache2.0
  ~
  ~ or in the "license" file accompanying this file. This file is distributed
  ~ on an "AS IS" BASIS, WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either
  ~ express or implied. See the License for the specific language governing
  ~ permissions and limitations under the License.
  --><project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>software.amazon.awssdk</groupId>
        <artifactId>aws-sdk-java-pom</artifactId>
        <version>2.20.123</version>
        <relativePath>../pom.xml</relativePath>
    </parent>
    <artifactId>bom</artifactId>
    <packaging>pom</packaging>
    <name>AWS Java SDK :: Bill of Materials</name>
    <description>The AWS SDK for Java - BOM module holds the dependency managements for individual java clients.</description>
    <url>https://aws.amazon.com/sdkforjava</url>
    <properties>
        <sonar.skip>true</sonar.skip>
    </properties>
    <dependencyManagement>
        <dependencies>
            <!-- Codegen -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codegen-lite-maven-plugin</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codegen-lite</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codegen-maven-plugin</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codegen</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <!-- Non-Services -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>annotations</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>arns</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>json-utils</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>third-party-jackson-core</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>third-party-jackson-dataformat-cbor</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>auth</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>aws-core</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>profiles</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>aws-cbor-protocol</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>aws-json-protocol</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>aws-query-protocol</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>aws-xml-protocol</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>protocol-core</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>regions</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sdk-core</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>http-client-spi</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>apache-client</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>netty-nio-client</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>url-connection-client</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>utils</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudwatch-metric-publisher</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3-transfer-manager</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>aws-crt-client</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iam-policy-builder</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <!-- Services -->
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>acm</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>acmpca</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>alexaforbusiness</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>amplify</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>apigateway</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>apigatewaymanagementapi</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>apigatewayv2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>applicationautoscaling</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>applicationdiscovery</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>appmesh</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>appstream</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>appsync</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>athena</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>autoscaling</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>autoscalingplans</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>backup</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>batch</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>budgets</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>chime</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloud9</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>clouddirectory</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudformation</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudfront</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudhsm</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudhsmv2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudsearch</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudsearchdomain</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudtrail</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudwatch</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudwatchevents</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudwatchlogs</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codebuild</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codecommit</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codedeploy</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codepipeline</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codestar</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cognitoidentity</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cognitoidentityprovider</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cognitosync</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>comprehend</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>comprehendmedical</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>config</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>connect</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>costandusagereport</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>costexplorer</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>databasemigration</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>datapipeline</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>datasync</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>dax</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>devicefarm</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>directconnect</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>directory</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>dlm</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>docdb</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>dynamodb</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>dynamodb-enhanced</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ec2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ecr</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ecs</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>efs</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>eks</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>elasticache</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>elasticbeanstalk</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>elasticloadbalancing</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>elasticloadbalancingv2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>elasticsearch</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>elastictranscoder</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>emr</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>firehose</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>fms</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>fsx</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>gamelift</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>glacier</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>globalaccelerator</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>glue</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>greengrass</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>guardduty</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>health</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iam</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>inspector</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iot</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iot1clickdevices</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iot1clickprojects</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iotanalytics</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iotdataplane</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iotjobsdataplane</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kafka</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kinesis</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kinesisanalytics</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kinesisanalyticsv2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kinesisvideo</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kinesisvideoarchivedmedia</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kinesisvideomedia</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kms</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>lambda</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>lexmodelbuilding</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>lexruntime</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>licensemanager</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>lightsail</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>machinelearning</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>macie</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>managedblockchain</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>marketplacecommerceanalytics</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>marketplaceentitlement</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>marketplacemetering</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>mediaconnect</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>mediaconvert</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>medialive</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>mediapackage</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>mediapackagevod</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>mediastore</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>mediastoredata</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>mediatailor</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>migrationhub</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>mobile</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>mq</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>mturk</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>neptune</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>opsworks</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>opsworkscm</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>organizations</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>pi</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>pinpoint</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>pinpointemail</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>pinpointsmsvoice</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>polly</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>pricing</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>quicksight</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ram</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>rds</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>rdsdata</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>redshift</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>rekognition</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>resourcegroups</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>resourcegroupstaggingapi</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>robomaker</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>route53</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>route53domains</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>route53resolver</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3control</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sagemaker</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sagemakerruntime</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>secretsmanager</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>securityhub</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>serverlessapplicationrepository</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>servicecatalog</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>servicediscovery</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ses</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sfn</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>shield</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>signer</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sms</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>snowball</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sns</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sqs</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ssm</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>storagegateway</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sts</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>support</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>swf</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>textract</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>transcribe</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>transcribestreaming</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>transfer</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>translate</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>waf</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>workdocs</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>worklink</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>workmail</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>workspaces</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>xray</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>groundstation</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iotthingsgraph</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iotevents</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ioteventsdata</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>personalizeruntime</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>personalize</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>personalizeevents</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>servicequotas</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>applicationinsights</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ec2instanceconnect</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>eventbridge</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>lakeformation</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>forecast</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>forecastquery</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>qldb</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>qldbsession</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>workmailmessageflow</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codestarnotifications</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>savingsplans</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sso</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ssooidc</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>marketplacecatalog</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sesv2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>dataexchange</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>migrationhubconfig</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>connectparticipant</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>wafv2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>appconfig</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iotsecuretunneling</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>elasticinference</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>imagebuilder</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>schemas</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>accessanalyzer</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>computeoptimizer</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>networkmanager</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kendra</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>frauddetector</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codegurureviewer</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codeguruprofiler</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>outposts</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sagemakera2iruntime</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ebs</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kinesisvideosignaling</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>detective</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codestarconnections</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>synthetics</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iotsitewise</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>macie2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codeartifact</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>honeycode</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ivs</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>braket</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>identitystore</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>appflow</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>redshiftdata</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ssoadmin</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>timestreamwrite</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>timestreamquery</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>s3outposts</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>metrics-spi</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>databrew</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>servicecatalogappregistry</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>networkfirewall</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>mwaa</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>devopsguru</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sagemakerfeaturestoreruntime</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>appintegrations</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ecrpublic</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>amplifybackend</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>connectcontactlens</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>lookoutvision</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>customerprofiles</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>emrcontainers</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sagemakeredge</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>healthlake</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>auditmanager</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>amp</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>greengrassv2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iotwireless</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iotfleethub</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iotdeviceadvisor</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>location</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>wellarchitected</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>lexruntimev2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>lexmodelsv2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>fis</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>lookoutmetrics</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>mgn</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>lookoutequipment</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>nimble</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>finspacedata</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>finspace</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ssmincidents</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ssmcontacts</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>applicationcostprofiler</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>apprunner</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>proton</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>route53recoveryreadiness</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>route53recoverycontrolconfig</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>route53recoverycluster</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>chimesdkmessaging</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>chimesdkidentity</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>snowdevicemanagement</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>memorydb</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>opensearch</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kafkaconnect</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>wisdom</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>voiceid</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>account</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudcontrol</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>grafana</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>panorama</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>chimesdkmeetings</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>resiliencehub</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>migrationhubstrategy</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>drs</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>appconfigdata</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>migrationhubrefactorspaces</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>inspector2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>evidently</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>rum</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>rbin</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iottwinmaker</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>workspacesweb</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>backupgateway</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>amplifyuibuilder</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>keyspaces</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>billingconductor</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>gamesparks</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>pinpointsmsvoicev2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ivschat</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>chimesdkmediapipelines</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>emrserverless</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>m2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>connectcampaigns</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>redshiftserverless</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>rolesanywhere</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>licensemanagerusersubscriptions</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>backupstorage</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>privatenetworks</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>supportapp</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>controltower</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iotfleetwise</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>migrationhuborchestrator</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>connectcases</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>resourceexplorer2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>scheduler</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ssmsap</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>chimesdkvoice</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>iotroborunner</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>oam</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>arczonalshift</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>simspaceweaver</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>securitylake</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>opensearchserverless</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>omics</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>docdbelastic</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sagemakergeospatial</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>pipes</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codecatalyst</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>sagemakermetrics</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kinesisvideowebrtcstorage</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>licensemanagerlinuxsubscriptions</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>kendraranking</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cleanrooms</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>cloudtraildata</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>imds</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>tnb</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>internetmonitor</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>ivsrealtime</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>vpclattice</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>osis</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>mediapackagev2</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>paymentcryptographydata</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>paymentcryptography</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>codegurusecurity</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>verifiedpermissions</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>appfabric</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>medicalimaging</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>entityresolution</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>managedblockchainquery</artifactId>
                <version>${awsjavasdk.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
</project>
