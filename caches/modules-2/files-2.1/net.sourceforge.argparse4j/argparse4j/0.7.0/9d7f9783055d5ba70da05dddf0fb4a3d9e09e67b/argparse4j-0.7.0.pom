<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
	<modelVersion>4.0.0</modelVersion>

	<groupId>net.sourceforge.argparse4j</groupId>
	<artifactId>argparse4j</artifactId>
	<version>0.7.0</version>
	<packaging>jar</packaging>

	<name>argparse4j</name>
	<url>http://argparse4j.github.io</url>
	<description>The command-line parser library based on Python's argparse</description>
	<inceptionYear>2012</inceptionYear>

	<properties>
		<project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
	</properties>

	<licenses>
		<license>
			<name>MIT</name>
			<url>https://raw.github.com/tatsuhiro-t/argparse4j/master/LICENSE.txt</url>
		</license>
	</licenses>

	<developers>
		<developer>
			<id>tatsuhiro_t</id>
			<name>Tatsuhiro Tsujikawa</name>
			<timezone>+9</timezone>
		</developer>
	</developers>

	<issueManagement>
		<system>github.com</system>
		<url>https://github.com/tatsuhiro-t/argparse4j/issues</url>
	</issueManagement>

	<scm>
		<connection>scm:git:git://github.com/tatsuhiro-t/argparse4j.git</connection>
		<developerConnection>scm:git:**************:tatsuhiro-t/argparse4j.git</developerConnection>
		<url>https://github.com/tatsuhiro-t/argparse4j</url>
	</scm>

	<distributionManagement>
		<snapshotRepository>
			<id>ossrh</id>
			<url>https://oss.sonatype.org/content/repositories/snapshots</url>
		</snapshotRepository>
		<repository>
			<id>ossrh</id>
			<url>https://oss.sonatype.org/service/local/staging/deploy/maven2/</url>
		</repository>
	</distributionManagement>

	<dependencies>
		<dependency>
			<groupId>junit</groupId>
			<artifactId>junit</artifactId>
			<version>4.8.1</version>
			<scope>test</scope>
		</dependency>
	</dependencies>

	<reporting>
		<plugins>
			<plugin>
				<artifactId>maven-project-info-reports-plugin</artifactId>
				<version>2.4</version>
				<reportSets>
					<reportSet>
						<reports>
							<report>summary</report>
							<report>dependencies</report>
							<report>project-team</report>
							<report>issue-tracking</report>
							<report>license</report>
							<report>scm</report>
						</reports>
					</reportSet>
				</reportSets>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>2.10.3</version>
				<configuration>
					<locale>en</locale>
				</configuration>
				<reportSets>
					<reportSet>
						<id>default</id>
						<reports>
							<report>javadoc</report>
						</reports>
					</reportSet>
				</reportSets>
			</plugin>
		</plugins>
	</reporting>

	<build>
		<testResources>
			<testResource>
				<directory>src/test/resources</directory>
			</testResource>
		</testResources>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-site-plugin</artifactId>
				<version>3.3</version>
			</plugin>
			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>exec-maven-plugin</artifactId>
				<version>1.2.1</version>
				<executions>
					<execution>
						<id>sphinx-doc</id>
						<phase>site</phase>
						<goals>
							<goal>exec</goal>
						</goals>
						<configuration>
							<executable>sphinx-make-html</executable>
							<arguments>
								<argument>${project.version}</argument>
							</arguments>
						</configuration>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<artifactId>maven-assembly-plugin</artifactId>
				<version>2.2.1</version>
				<configuration>
					<descriptorRefs>
						<descriptorRef>bin</descriptorRef>
						<descriptorRef>src</descriptorRef>
					</descriptorRefs>
				</configuration>
			</plugin>
			<plugin>
				<artifactId>maven-javadoc-plugin</artifactId>
				<version>2.10.3</version>
				<configuration>
					<locale>en</locale>
					<stylesheet>maven</stylesheet>
				</configuration>
				<executions>
					<execution>
						<id>attach-javadocs</id>
						<goals>
							<goal>jar</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-source-plugin</artifactId>
				<version>2.2.1</version>
				<executions>
					<execution>
						<id>attach-sources</id>
						<goals>
							<goal>jar-no-fork</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-release-plugin</artifactId>
				<version>2.2.1</version>
				<configuration>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<version>3.0</version>
				<configuration>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.sonatype.plugins</groupId>
				<artifactId>nexus-staging-maven-plugin</artifactId>
				<version>1.6.3</version>
				<extensions>true</extensions>
				<configuration>
					<serverId>ossrh</serverId>
					<nexusUrl>https://oss.sonatype.org/</nexusUrl>
					<autoReleaseAfterClose>true</autoReleaseAfterClose>
				</configuration>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-gpg-plugin</artifactId>
				<version>1.5</version>
				<executions>
					<execution>
						<id>sign-artifacts</id>
						<phase>verify</phase>
						<goals>
						  <goal>sign</goal>
						</goals>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

</project>
