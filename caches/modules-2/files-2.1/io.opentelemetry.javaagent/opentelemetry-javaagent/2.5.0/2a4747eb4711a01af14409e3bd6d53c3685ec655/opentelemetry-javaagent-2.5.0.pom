<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.opentelemetry.javaagent</groupId>
  <artifactId>opentelemetry-javaagent</artifactId>
  <version>2.5.0</version>
  <name>OpenTelemetry Instrumentation for Java</name>
  <description>OpenTelemetry Javaagent</description>
  <url>https://github.com/open-telemetry/opentelemetry-java-instrumentation</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>opentelemetry</id>
      <name>OpenTelemetry</name>
      <url>https://github.com/open-telemetry/opentelemetry-java-instrumentation/discussions</url>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:**************:open-telemetry/opentelemetry-java-instrumentation.git</connection>
    <developerConnection>scm:git:**************:open-telemetry/opentelemetry-java-instrumentation.git</developerConnection>
    <url>**************:open-telemetry/opentelemetry-java-instrumentation.git</url>
  </scm>
</project>
