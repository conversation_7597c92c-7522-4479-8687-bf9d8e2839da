<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.thoughtworks.paranamer</groupId>
    <artifactId>paranamer-parent</artifactId>
    <version>2.8</version>
  </parent>
  <artifactId>paranamer</artifactId>
  <packaging>bundle</packaging>
  <name>ParaNamer Core</name>
  <dependencies>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-all</artifactId>
      <version>1.10.19</version>
      <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>javax.inject</groupId>
        <artifactId>javax.inject</artifactId>
        <version>1</version>
        <optional>true</optional>
    </dependency>
    <dependency>
        <groupId>net.sourceforge.f2j</groupId>
        <artifactId>arpack_combined_all</artifactId>
        <version>0.1</version>
        <scope>test</scope>
    </dependency>
    <dependency>
        <groupId>net.sourceforge.f2j</groupId>
        <artifactId>arpack_combined_all</artifactId>
        <version>0.1</version>
        <classifier>javadoc</classifier>
        <scope>test</scope>
    </dependency>
      <dependency>
          <groupId>${project.groupId}</groupId>
          <artifactId>${project.artifactId}</artifactId>
          <version>2.5.5</version>
          <classifier>javadoc</classifier>
          <scope>test</scope>
      </dependency>
  </dependencies>
  <build>
    <plugins>
		<plugin>
			<groupId>org.apache.maven.plugins</groupId>
			<artifactId>maven-source-plugin</artifactId>
			<executions>
				<execution>
					<id>attach-sources</id>
					<goals>
						<goal>jar</goal>
					</goals>
				</execution>
			</executions>
		</plugin>
		<plugin>
			<groupId>org.apache.maven.plugins</groupId>
			<artifactId>maven-javadoc-plugin</artifactId>
			<executions>
				<execution>
					<id>attach-javadocs</id>
					<goals>
						<goal>jar</goal>
					</goals>
				</execution>
			</executions>
		</plugin>
      <plugin>
        <groupId>com.thoughtworks.paranamer</groupId>
        <artifactId>paranamer-maven-plugin</artifactId>
        <version>${project.version}</version>
        <executions>
          <execution>
            <phase>compile</phase>
            <goals>
              <goal>generate</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <sourceDirectory>${project.build.sourceDirectory}</sourceDirectory>
          <outputDirectory>${project.build.outputDirectory}</outputDirectory>
        </configuration>
      </plugin>
        <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-dependency-plugin</artifactId>
            <executions>
                <execution>
                    <id>javadocs</id>
                    <phase>test-compile</phase>
                    <goals>
                        <goal>copy-dependencies</goal>
                    </goals>
                    <configuration>
                        <excludeTransitive>true</excludeTransitive>
                        <classifier>javadoc</classifier>
                        <includeGroupIds>net.sourceforge.f2j,${project.groupId}</includeGroupIds>
                        <includeArtifactIds>arpack_combined_all,${project.artifactId}</includeArtifactIds>
                        <failOnMissingClassifierArtifact>true</failOnMissingClassifierArtifact>
                        <outputDirectory>${project.build.directory}/test-data</outputDirectory>
                    </configuration>
                </execution>
            </executions>
        </plugin>

        <plugin>
          <groupId>org.apache.felix</groupId>
          <artifactId>maven-bundle-plugin</artifactId>
          <extensions>true</extensions>
          <configuration>
            <instructions>
              <Export-Package>com.thoughtworks.paranamer</Export-Package>
            </instructions>
          </configuration>
        </plugin>
        
    </plugins>
    <pluginManagement>
      <plugins>
        <!--This plugin's configuration is used to store Eclipse m2e settings only. It has no influence on the Maven build itself.-->
        <plugin>
          <groupId>org.eclipse.m2e</groupId>
          <artifactId>lifecycle-mapping</artifactId>
          <version>1.0.0</version>
          <configuration>
            <lifecycleMappingMetadata>
              <pluginExecutions>
                <pluginExecution>
                  <pluginExecutionFilter>
                    <groupId>com.thoughtworks.paranamer</groupId>
                    <artifactId>paranamer-maven-plugin</artifactId>
                    <versionRange>[2.4.1,)</versionRange>
                    <goals>
                      <goal>generate</goal>
                    </goals>
                  </pluginExecutionFilter>
                  <action>
                    <ignore />
                  </action>
                </pluginExecution>
              </pluginExecutions>
            </lifecycleMappingMetadata>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>
</project>
