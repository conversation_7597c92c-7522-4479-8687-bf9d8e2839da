<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.hibernate.common</groupId>
  <artifactId>hibernate-commons-annotations</artifactId>
  <version>6.0.6.Final</version>
  <name>Hibernate Commons Annotations</name>
  <description>Common reflection code used in support of annotation processing</description>
  <url>http://hibernate.org</url>
  <organization>
    <name>Hibernate.org</name>
    <url>http://hibernate.org</url>
  </organization>
  <licenses>
    <license>
      <name>GNU Library General Public License v2.1 or later</name>
      <url>http://www.opensource.org/licenses/LGPL-2.1</url>
      <distribution>repo</distribution>
      <comments>See discussion at http://hibernate.org/community/license/ for more details.</comments>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>hibernate-team</id>
      <name>The Hibernate Development Team</name>
      <organization>Hibernate.org</organization>
      <organizationUrl>http://hibernate.org</organizationUrl>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:http://github.com/hibernate/hibernate-commons-annotations.git</connection>
    <developerConnection>scm:git:**************:hibernate/hibernate-commons-annotations.git</developerConnection>
    <url>http://github.com/hibernate/hibernate-commons-annotations</url>
  </scm>
  <issueManagement>
    <system>jira</system>
    <url>https://hibernate.atlassian.net/browse/HCANN</url>
  </issueManagement>
</project>
