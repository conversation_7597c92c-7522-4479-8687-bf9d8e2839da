<?xml version="1.0" encoding="UTF-8"?>
  <!--
  vi:ts=2:sw=2:expandtab:
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">

  <!-- Model Version -->
  <modelVersion>4.0.0</modelVersion>

  <!-- Artifact Configuration -->
  <groupId>org.jboss.shrinkwrap.resolver</groupId>
  <artifactId>shrinkwrap-resolver-bom</artifactId>
  <version>3.1.4</version>
  <packaging>pom</packaging>
  <name>ShrinkWrap Resolver Bill of Materials</name>
  <description>Centralized dependencyManagement for the ShrinkWrap Resolver Project</description>
  <url>http://www.jboss.org/shrinkwrap</url>
  
  <!-- Licenses -->
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license> 
  </licenses>

  <!-- SCM -->
  <scm>
    <connection>scm:git:git://github.com/shrinkwrap/shrinkwrap.git</connection>
    <developerConnection>scm:git:**************:shrinkwrap/shrinkwrap.git</developerConnection>
    <url>https://github.com/shrinkwrap/shrinkwrap</url>
    <tag>3.1.4</tag>
  </scm>

  <developers>
    <developer>
      <id>jboss.org</id>
      <name>JBoss.org Community</name>
      <organization>JBoss.org</organization>
      <organizationUrl>http://www.jboss.org</organizationUrl>
    </developer>
  </developers>

  <!-- Properties -->
  <properties>

    <jboss.releases.repo.url>https://repository.jboss.org/nexus/service/local/staging/deploy/maven2/</jboss.releases.repo.url>
    <jboss.snapshots.repo.url>https://repository.jboss.org/nexus/content/repositories/snapshots/</jboss.snapshots.repo.url>
    <version.org.apache.maven>3.6.3</version.org.apache.maven>

  </properties>

  <!-- Dependency Management -->
  <dependencyManagement>
  
    <dependencies>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-api</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-spi</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-api-maven</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-spi-maven</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-impl-maven</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-api-maven-archive</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-impl-maven-archive</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-depchain</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-gradle-depchain</artifactId>
        <version>${project.version}</version>
        <type>pom</type>
      </dependency>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-maven-plugin</artifactId>
        <version>${project.version}</version>
        <scope>runtime</scope>
        <type>maven-plugin</type>
      </dependency>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-api-gradle-embedded-archive</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-impl-gradle-embedded-archive</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-api-maven-embedded</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.jboss.shrinkwrap.resolver</groupId>
        <artifactId>shrinkwrap-resolver-impl-maven-embedded</artifactId>
        <version>${project.version}</version>
      </dependency>

       <!-- Maven BOM -->
      <dependency>
        <groupId>org.apache.maven</groupId>
        <artifactId>maven</artifactId>
        <version>${version.org.apache.maven}</version>
        <scope>import</scope>
        <type>pom</type>
      </dependency>
    </dependencies>
  
  </dependencyManagement>
  
  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <artifactId>maven-release-plugin</artifactId>
          <configuration>
            <pushChanges>false</pushChanges>
            <localCheckout>true</localCheckout> 
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>
  </build>

  <profiles>
    <!--
        Taken from jboss-parent as we don't inherit from it  here.

        This profile can be activated to generate gpg signatures for all build
        artifacts.  This profile requires that the properties "gpg.keyname"
        and "gpg.passphrase" are available to the current build.
    -->
    <profile>
      <id>gpg-sign</id>
      <build>
        <plugins>
          <!-- This will sign the artifact, the POM, and all attached artifacts -->
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <configuration>
              <useAgent>true</useAgent>
            </configuration>
            <executions>
              <execution>
                <goals>
                  <goal>sign</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>

  <distributionManagement>
    <repository>
      <id>jboss-releases-repository</id>
      <name>JBoss Releases Repository</name>
      <url>${jboss.releases.repo.url}</url>
    </repository>
    <snapshotRepository>
      <id>jboss-snapshots-repository</id>
      <name>JBoss Snapshots Repository</name>
      <url>${jboss.snapshots.repo.url}</url>
    </snapshotRepository>
  </distributionManagement>

</project>
