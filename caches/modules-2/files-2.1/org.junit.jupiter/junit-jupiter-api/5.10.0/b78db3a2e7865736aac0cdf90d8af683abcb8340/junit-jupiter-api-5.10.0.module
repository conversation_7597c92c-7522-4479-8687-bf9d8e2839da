{"formatVersion": "1.1", "component": {"group": "org.junit.jupiter", "module": "junit-jupiter-api", "version": "5.10.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.2.1"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "org.junit", "module": "junit-bom", "version": {"requires": "5.10.0"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.opentest4j", "module": "opentest4j", "version": {"requires": "1.3.0"}}, {"group": "org.junit.platform", "module": "junit-platform-commons", "version": {"requires": "1.10.0"}}, {"group": "org.apiguardian", "module": "apiguardian-api", "version": {"requires": "1.1.2"}}], "files": [{"name": "junit-jupiter-api-5.10.0.jar", "url": "junit-jupiter-api-5.10.0.jar", "size": 210954, "sha512": "440d7376673ed8e8ea84ce9f10e089f417c93a4f7044d23443588fbfa96cf74492ab26597fd9955537ce0e3190448062cfeadd428cb0de8522f81ccf3faa6353", "sha256": "108088fd7ea46a8e65a0ce7f5d75ae3ff7865606770a078715f5a6e5709e17d8", "sha1": "2fe4ba3d31d5067878e468c96aa039005a9134d3", "md5": "d976b1d3b22eded7c2e496ea881ed77f"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "org.junit", "module": "junit-bom", "version": {"requires": "5.10.0"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}, {"group": "org.opentest4j", "module": "opentest4j", "version": {"requires": "1.3.0"}}, {"group": "org.junit.platform", "module": "junit-platform-commons", "version": {"requires": "1.10.0"}}], "files": [{"name": "junit-jupiter-api-5.10.0.jar", "url": "junit-jupiter-api-5.10.0.jar", "size": 210954, "sha512": "440d7376673ed8e8ea84ce9f10e089f417c93a4f7044d23443588fbfa96cf74492ab26597fd9955537ce0e3190448062cfeadd428cb0de8522f81ccf3faa6353", "sha256": "108088fd7ea46a8e65a0ce7f5d75ae3ff7865606770a078715f5a6e5709e17d8", "sha1": "2fe4ba3d31d5067878e468c96aa039005a9134d3", "md5": "d976b1d3b22eded7c2e496ea881ed77f"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "junit-jupiter-api-5.10.0-javadoc.jar", "url": "junit-jupiter-api-5.10.0-javadoc.jar", "size": 780780, "sha512": "34301c5833279d53f280bcb3419cfe2cb6d73142d3a3a5fed2a3fb44a8bad293cbea6719ddeb47b193ea37e23acffaed7b44df43a98317f24980c735706ffbce", "sha256": "f84b8a8befbc83c3af776bf39f3404c59ca20a0b9269557c61292906a0924f6c", "sha1": "721a9150a79ba80c1bfe09e09b71ab7b579ac2f2", "md5": "b77619c81f9ac3857c1400d5d6ba4439"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "junit-jupiter-api-5.10.0-sources.jar", "url": "junit-jupiter-api-5.10.0-sources.jar", "size": 204005, "sha512": "38d00fda49f1dc0be3bb61287b4cf6f519c0fc6b8cb88a07cde638c13b500361bd5b4460b003a20541af6dd494e63a5b6f357caa51f8b77b9ef42ef18e58deb4", "sha256": "34fbf1f58dac2db51ab44f22f547fcee185b342d046c3ac119ec00dd2ba98e75", "sha1": "551bc33910fee1c601da155a77f67f70f66f6a27", "md5": "1bcc93c6fb6214ee7cfcc8e888681c97"}, {"name": "junit-jupiter-api-5.10.0-sources.jar", "url": "junit-jupiter-api-5.10.0-sources.jar", "size": 204005, "sha512": "38d00fda49f1dc0be3bb61287b4cf6f519c0fc6b8cb88a07cde638c13b500361bd5b4460b003a20541af6dd494e63a5b6f357caa51f8b77b9ef42ef18e58deb4", "sha256": "34fbf1f58dac2db51ab44f22f547fcee185b342d046c3ac119ec00dd2ba98e75", "sha1": "551bc33910fee1c601da155a77f67f70f66f6a27", "md5": "1bcc93c6fb6214ee7cfcc8e888681c97"}]}]}