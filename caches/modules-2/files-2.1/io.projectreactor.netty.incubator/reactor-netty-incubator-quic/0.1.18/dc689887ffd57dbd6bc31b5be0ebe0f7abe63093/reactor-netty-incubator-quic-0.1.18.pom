<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.projectreactor.netty.incubator</groupId>
  <artifactId>reactor-netty-incubator-quic</artifactId>
  <version>0.1.18</version>
  <name>QUIC functionality for the Reactor Netty library</name>
  <description>QUIC functionality for the Reactor Netty library</description>
  <url>https://github.com/reactor/reactor-netty</url>
  <organization>
    <name>reactor</name>
    <url>https://github.com/reactor</url>
  </organization>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>simonbasle</id>
      <name>Simon Baslé</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>violetagg</id>
      <name>Violeta Georgieva</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/reactor/reactor-netty</connection>
    <developerConnection>scm:git:git://github.com/reactor/reactor-netty</developerConnection>
    <url>https://github.com/reactor/reactor-netty</url>
  </scm>
  <issueManagement>
    <system>GitHub Issues</system>
    <url>https://github.com/reactor/reactor-netty/issues</url>
  </issueManagement>
  <dependencies>
    <dependency>
      <groupId>io.netty.incubator</groupId>
      <artifactId>netty-incubator-codec-native-quic</artifactId>
      <version>0.0.62.Final</version>
      <classifier>linux-x86_64</classifier>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.projectreactor.netty</groupId>
      <artifactId>reactor-netty-core</artifactId>
      <version>1.1.18</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>commons-logging</groupId>
          <artifactId>commons-logging</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
</project>
