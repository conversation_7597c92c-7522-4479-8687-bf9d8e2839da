<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <parent>
    <groupId>com.azure</groupId>
    <artifactId>azure-client-sdk-parent</artifactId>
    <version>1.7.0</version> <!-- {x-version-update;com.azure:azure-client-sdk-parent;current} -->
    <relativePath>../../parents/azure-client-sdk-parent</relativePath>
  </parent>

  <modelVersion>4.0.0</modelVersion>

  <groupId>com.azure</groupId>
  <artifactId>azure-storage-internal-avro</artifactId>
  <version>12.10.4</version> <!-- {x-version-update;com.azure:azure-storage-internal-avro;current} -->

  <name>Microsoft Azure internal Avro module for Storage</name>
  <description>This module contains internal use only avro parser code based for Microsoft Azure Storage client libraries.</description>
  <url>https://github.com/Azure/azure-sdk-for-java</url>

  <distributionManagement>
    <site>
      <id>azure-java-build-docs</id>
      <url>${site.url}/site/${project.artifactId}</url>
    </site>
  </distributionManagement>

  <scm>
    <url>scm:git:https://github.com/Azure/azure-sdk-for-java</url>
    <connection>scm:git:**************:Azure/azure-sdk-for-java.git</connection>
    <tag>HEAD</tag>
  </scm>

  <properties>
    <jacoco.min.linecoverage>0.10</jacoco.min.linecoverage>
    <jacoco.min.branchcoverage>0.10</jacoco.min.branchcoverage>
    <!-- Configures the Java 9+ run to perform the required module exports, opens, and reads that are necessary for testing but shouldn't be part of the module-info. -->
    <javaModulesSurefireArgLine>
      --add-opens com.azure.storage.common/com.azure.storage.common.implementation=ALL-UNNAMED
      --add-opens com.azure.storage.internal.avro/com.azure.storage.internal.avro.implementation=ALL-UNNAMED
      --add-opens com.azure.storage.internal.avro/com.azure.storage.internal.avro.implementation.schema=ALL-UNNAMED
    </javaModulesSurefireArgLine>
    <parallelizeLiveTests>true</parallelizeLiveTests>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-core</artifactId>
      <version>1.48.0</version> <!-- {x-version-update;com.azure:azure-core;dependency} -->
    </dependency>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-storage-common</artifactId>
      <version>12.24.4</version> <!-- {x-version-update;com.azure:azure-storage-common;current} -->
    </dependency>

    <dependency>
      <groupId>com.fasterxml.jackson.dataformat</groupId>
      <artifactId>jackson-dataformat-xml</artifactId>
      <version>2.13.5</version> <!-- {x-version-update;com.fasterxml.jackson.dataformat:jackson-dataformat-xml;external_dependency} -->
    </dependency>

    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>5.9.3</version> <!-- {x-version-update;org.junit.jupiter:junit-jupiter-api;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>5.9.3</version> <!-- {x-version-update;org.junit.jupiter:junit-jupiter-engine;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <version>5.9.3</version> <!-- {x-version-update;org.junit.jupiter:junit-jupiter-params;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-core-test</artifactId>
      <version>1.24.1</version> <!-- {x-version-update;com.azure:azure-core-test;dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-test</artifactId>
      <version>3.4.36</version> <!-- {x-version-update;io.projectreactor:reactor-test;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-storage-common</artifactId>
      <version>12.24.4</version> <!-- {x-version-update;com.azure:azure-storage-common;current} -->
      <classifier>tests</classifier>
      <type>test-jar</type>
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <version>1.7.36</version> <!-- {x-version-update;org.slf4j:slf4j-simple;external_dependency} -->
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <!-- START: Empty Java Doc -->
      <!-- The following code will generate an empty javadoc with just a README.md. This is necessary
           to pass the required checks on Maven. The way this works is by setting the classesDirectory
           to a directory that only contains the README.md, which we need to copy. If the classesDirectory
           is set to the root, where the README.md lives, it still won't have javadocs but the jar file
           will contain a bunch of files that shouldn't be there. The faux sources directory is deleted
           and recreated with the README.md being copied every time to guarantee that, when building locally,
           it'll have the latest copy of the README.md file.
      -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.6.3</version> <!-- {x-version-update;org.apache.maven.plugins:maven-javadoc-plugin;external_dependency} -->
        <executions>
          <execution>
            <id>attach-javadocs</id>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <skip>true</skip>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.3.0</version> <!-- {x-version-update;org.apache.maven.plugins:maven-jar-plugin;external_dependency} -->
        <executions>
          <execution>
            <id>empty-javadoc-jar-with-readme</id>
            <phase>package</phase>
            <goals>
              <goal>jar</goal>
            </goals>
            <configuration>
              <classifier>javadoc</classifier>
              <classesDirectory>${project.basedir}/javadocTemp</classesDirectory>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <version>3.1.0</version> <!-- {x-version-update;org.apache.maven.plugins:maven-antrun-plugin;external_dependency} -->
        <executions>
          <execution>
            <id>copy-readme-to-javadocTemp</id>
            <phase>prepare-package</phase>
            <configuration>
              <target>
                <echo>Deleting existing ${project.basedir}/javadocTemp</echo>
                <delete includeEmptyDirs="true" quiet="true">
                  <fileset dir="${project.basedir}/javadocTemp"/>
                </delete>
                <echo>Copying ${project.basedir}/README.md to
                  ${project.basedir}/javadocTemp/README.md
                </echo>
                <copy file="${project.basedir}/README.md" tofile="${project.basedir}/javadocTemp/README.md"/>
              </target>
            </configuration>
            <goals>
              <goal>run</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <!-- END: Empty Java Doc -->

      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-enforcer-plugin</artifactId>
        <version>3.4.1</version> <!-- {x-version-update;org.apache.maven.plugins:maven-enforcer-plugin;external_dependency} -->
        <configuration>
          <rules>
            <bannedDependencies>
              <includes>
                <include>com.fasterxml.jackson.dataformat:jackson-dataformat-xml:[2.13.5]</include> <!-- {x-include-update;com.fasterxml.jackson.dataformat:jackson-dataformat-xml;external_dependency} -->
              </includes>
            </bannedDependencies>
          </rules>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
