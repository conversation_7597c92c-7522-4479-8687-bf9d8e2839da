<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.azure</groupId>
  <artifactId>azure-sdk-bom</artifactId>
  <version>1.2.23</version>
  <packaging>pom</packaging>
  <name>Azure Java SDK BOM (Bill of Materials)</name>
  <description>Azure Java SDK BOM (Bill of Materials)</description>
  <url>https://github.com/azure/azure-sdk-for-java</url>
  <licenses>
    <license>
      <name>The MIT License (MIT)</name>
      <url>http://opensource.org/licenses/MIT</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>microsoft</id>
      <name>Microsoft Corporation</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/azure/azure-sdk-for-java</connection>
    <developerConnection>scm:git:git://github.com/azure/azure-sdk-for-java</developerConnection>
    <url>https://github.com/azure/azure-sdk-for-java</url>
  </scm>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <packageOutputDirectory>${project.build.directory}</packageOutputDirectory>
  </properties>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/azure/azure-sdk-for-java/issues</url>
  </issueManagement>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-ai-contentsafety</artifactId>
        <version>1.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-ai-formrecognizer</artifactId>
        <version>4.1.7</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-ai-metricsadvisor</artifactId>
        <version>1.1.24</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-ai-textanalytics</artifactId>
        <version>5.4.5</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-communication-callautomation</artifactId>
        <version>1.1.4</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-communication-chat</artifactId>
        <version>1.5.0</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-communication-common</artifactId>
        <version>1.3.2</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-communication-email</artifactId>
        <version>1.0.12</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-communication-identity</artifactId>
        <version>1.5.4</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-communication-jobrouter</artifactId>
        <version>1.1.3</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-communication-messages</artifactId>
        <version>1.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-communication-phonenumbers</artifactId>
        <version>1.1.12</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-communication-rooms</artifactId>
        <version>1.1.1</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-communication-sms</artifactId>
        <version>1.1.23</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-containers-containerregistry</artifactId>
        <version>1.2.7</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-core</artifactId>
        <version>1.48.0</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-core-amqp</artifactId>
        <version>2.9.3</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-core-http-netty</artifactId>
        <version>1.14.2</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-core-http-okhttp</artifactId>
        <version>1.11.20</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-core-management</artifactId>
        <version>1.13.0</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-core-serializer-json-gson</artifactId>
        <version>1.2.11</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-core-serializer-json-jackson</artifactId>
        <version>1.4.11</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-cosmos</artifactId>
        <version>4.58.0</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-cosmos-encryption</artifactId>
        <version>2.10.0</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-data-appconfiguration</artifactId>
        <version>1.6.0</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-data-schemaregistry</artifactId>
        <version>1.4.5</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-data-schemaregistry-apacheavro</artifactId>
        <version>1.1.16</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-data-tables</artifactId>
        <version>12.4.0</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-developer-loadtesting</artifactId>
        <version>1.0.12</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-digitaltwins-core</artifactId>
        <version>1.3.19</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-identity</artifactId>
        <version>1.12.0</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-identity-broker</artifactId>
        <version>1.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-identity-extensions</artifactId>
        <version>1.1.15</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-iot-deviceupdate</artifactId>
        <version>1.0.17</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-json</artifactId>
        <version>1.1.0</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-messaging-eventgrid</artifactId>
        <version>4.22.1</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-messaging-eventhubs</artifactId>
        <version>5.18.3</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-messaging-eventhubs-checkpointstore-blob</artifactId>
        <version>1.19.3</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-messaging-servicebus</artifactId>
        <version>7.16.0</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-messaging-webpubsub</artifactId>
        <version>1.2.14</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-messaging-webpubsub-client</artifactId>
        <version>1.0.2</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-mixedreality-authentication</artifactId>
        <version>1.2.23</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-mixedreality-remoterendering</artifactId>
        <version>1.1.28</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-monitor-ingestion</artifactId>
        <version>1.2.0</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-monitor-query</artifactId>
        <version>1.3.1</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-search-documents</artifactId>
        <version>11.6.4</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-security-attestation</artifactId>
        <version>1.1.23</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-security-confidentialledger</artifactId>
        <version>1.0.19</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-security-keyvault-administration</artifactId>
        <version>4.5.2</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-security-keyvault-certificates</artifactId>
        <version>4.6.2</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-security-keyvault-jca</artifactId>
        <version>2.8.1</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-security-keyvault-keys</artifactId>
        <version>4.8.2</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-security-keyvault-secrets</artifactId>
        <version>4.8.2</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-storage-blob</artifactId>
        <version>12.25.4</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-storage-blob-batch</artifactId>
        <version>12.21.4</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-storage-blob-cryptography</artifactId>
        <version>12.24.4</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-storage-common</artifactId>
        <version>12.24.4</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-storage-file-datalake</artifactId>
        <version>12.18.4</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-storage-file-share</artifactId>
        <version>12.21.4</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-storage-internal-avro</artifactId>
        <version>12.10.4</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-storage-queue</artifactId>
        <version>12.20.4</version>
      </dependency>
      <dependency>
        <groupId>com.azure</groupId>
        <artifactId>azure-xml</artifactId>
        <version>1.0.0</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <profiles>
    <profile>
      <id>dependency-checker</id>
      <activation>
        <property>
          <name>dependency-checker</name>
        </property>
      </activation>
      <build>
        <plugins>
          <!-- This plugin validates that the artifacts in this BOM use a common dependency set. -->
          <plugin>
            <groupId>net.jonathangiles.tools</groupId>
            <artifactId>dependencyChecker-maven-plugin</artifactId>
            <version>1.0.6</version>
            <!-- {x-version-update;net.jonathangiles.tools:dependencyChecker-maven-plugin;external_dependency} -->
            <executions>
              <execution>
                <phase>package</phase>
                <goals>
                  <goal>check</goal>
                </goals>
                <configuration>
                  <reporters>html</reporters>
                  <showAll>true</showAll>
                  <analyseBom>true</analyseBom>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
  <build>
    <plugins>
      <!-- Copy the pom file to output -->
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-antrun-plugin</artifactId>
        <version>1.8
        </version>
        <!-- {x-version-update;org.apache.maven.plugins:maven-antrun-plugin;external_dependency} -->
        <executions>
          <execution>
            <id>copy</id>
            <phase>package</phase>
            <configuration>
              <target>
                <copy file="${project.pomFile}" tofile="${packageOutputDirectory}/${project.build.finalName}.pom" />
              </target>
            </configuration>
            <goals>
              <goal>run</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
