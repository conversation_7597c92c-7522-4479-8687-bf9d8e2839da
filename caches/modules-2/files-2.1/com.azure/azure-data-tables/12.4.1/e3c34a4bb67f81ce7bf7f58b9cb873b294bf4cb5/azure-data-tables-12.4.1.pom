<!--
Copyright (c) Microsoft Corporation. All rights reserved.
Licensed under the MIT License.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.azure</groupId>
    <artifactId>azure-client-sdk-parent</artifactId>
    <version>1.7.0</version> <!-- {x-version-update;com.azure:azure-client-sdk-parent;current} -->
    <relativePath>../../parents/azure-client-sdk-parent</relativePath>
  </parent>

  <groupId>com.azure</groupId>
  <artifactId>azure-data-tables</artifactId>
  <version>12.4.1</version> <!-- {x-version-update;com.azure:azure-data-tables;current} -->
  <name>Microsoft Azure client library for Azure Tables</name>
  <description>This package contains the client library for Microsoft Azure Tables.</description>
  <url>https://github.com/Azure/azure-sdk-for-java</url>

  <distributionManagement>
    <site>
      <id>azure-java-build-docs</id>
      <url>${site.url}/site/${project.artifactId}</url>
    </site>
  </distributionManagement>

  <scm>
    <url>scm:git:https://github.com/Azure/azure-sdk-for-java</url>
    <connection>scm:git:**************:Azure/azure-sdk-for-java.git</connection>
    <tag>HEAD</tag>
  </scm>

  <properties>
    <!-- Configures the Java 9+ run to perform the required module exports, opens, and reads that are necessary for testing but shouldn't be part of the module-info. -->
    <javaModulesSurefireArgLine>
      --add-opens com.azure.data.tables/com.azure.data.tables=ALL-UNNAMED
      --add-opens com.azure.data.tables/com.azure.data.tables.implementation=ALL-UNNAMED
      --add-opens com.azure.data.tables/com.azure.data.tables.implementation.models=ALL-UNNAMED
      --add-opens com.azure.data.tables/com.azure.data.tables.models=ALL-UNNAMED
    </javaModulesSurefireArgLine>
    <javadoc.excludePackageNames>com.azure.json:com.azure.core.*</javadoc.excludePackageNames>
  </properties>

  <dependencies>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-core</artifactId>
      <version>1.49.0</version> <!-- {x-version-update;com.azure:azure-core;dependency} -->
    </dependency>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-core-http-netty</artifactId>
      <version>1.15.0</version> <!-- {x-version-update;com.azure:azure-core-http-netty;dependency} -->
    </dependency>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-json</artifactId>
      <version>1.1.0</version> <!-- {x-version-update;com.azure:azure-json;dependency} -->
    </dependency>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-xml</artifactId>
      <version>1.0.0</version> <!-- {x-version-update;com.azure:azure-xml;dependency} -->
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>5.9.3</version> <!-- {x-version-update;org.junit.jupiter:junit-jupiter-api;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>5.9.3</version> <!-- {x-version-update;org.junit.jupiter:junit-jupiter-engine;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-params</artifactId>
      <version>5.9.3</version> <!-- {x-version-update;org.junit.jupiter:junit-jupiter-params;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-test</artifactId>
      <version>3.4.36</version> <!-- {x-version-update;io.projectreactor:reactor-test;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-core-test</artifactId>
      <version>1.25.0</version> <!-- {x-version-update;com.azure:azure-core-test;dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-identity</artifactId>
      <version>1.12.1</version> <!-- {x-version-update;com.azure:azure-identity;dependency} -->
      <scope>test</scope>
    </dependency>
  </dependencies>
</project>
