<project xmlns="http://maven.apache.org/POM/4.0.0" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.azure</groupId>
    <artifactId>azure-client-sdk-parent</artifactId>
    <version>1.7.0</version> <!-- {x-version-update;com.azure:azure-client-sdk-parent;current} -->
    <relativePath>../../parents/azure-client-sdk-parent</relativePath>
  </parent>

  <groupId>com.azure.resourcemanager</groupId>
  <artifactId>azure-resourcemanager-applicationinsights</artifactId>
  <version>1.0.0-beta.5</version> <!-- {x-version-update;com.azure.resourcemanager:azure-resourcemanager-applicationinsights;current} -->
  <packaging>jar</packaging>

  <name>Microsoft Azure SDK for ApplicationInsights Management</name>
  <description>This package contains Microsoft Azure SDK for ApplicationInsights Management SDK. For documentation on how to use this package, please see https://aka.ms/azsdk/java/mgmt. Composite Swagger for Application Insights Management Client. Package tag package-2022-04-01.</description>
  <url>https://github.com/Azure/azure-sdk-for-java</url>

  <licenses>
    <license>
      <name>The MIT License (MIT)</name>
      <url>http://opensource.org/licenses/MIT</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <scm>
    <url>https://github.com/Azure/azure-sdk-for-java</url>
    <connection>scm:git:**************:Azure/azure-sdk-for-java.git</connection>
    <developerConnection>scm:git:**************:Azure/azure-sdk-for-java.git</developerConnection>
    <tag>HEAD</tag>
  </scm>
  <developers>
    <developer>
      <id>microsoft</id>
      <name>Microsoft</name>
    </developer>
  </developers>
  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <jacoco.skip>true</jacoco.skip>
  </properties>
  <dependencies>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-core</artifactId>
      <version>1.29.1</version> <!-- {x-version-update;com.azure:azure-core;dependency} -->
    </dependency>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-core-management</artifactId>
      <version>1.6.2</version> <!-- {x-version-update;com.azure:azure-core-management;dependency} -->
    </dependency>
  </dependencies>
</project>
