<!--
 Copyright (c) Microsoft Corporation. All rights reserved.
 Licensed under the MIT License. See License.txt in the project root for
 license information.
-->
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.azure</groupId>
    <artifactId>azure-client-sdk-parent</artifactId>
    <version>1.7.0</version> <!-- {x-version-update;com.azure:azure-client-sdk-parent;current} -->
    <relativePath>../../parents/azure-client-sdk-parent</relativePath>
  </parent>

  <groupId>com.azure.resourcemanager</groupId>
  <artifactId>azure-resourcemanager-dns</artifactId>
  <version>2.17.0</version> <!-- {x-version-update;com.azure.resourcemanager:azure-resourcemanager-dns;current} -->
  <packaging>jar</packaging>

  <name>Microsoft Azure SDK for DNS Management</name>
  <description>This package contains Microsoft Azure DNS Management SDK. For documentation on how to use this package, please see https://aka.ms/azsdk/java/mgmt</description>
  <url>https://github.com/Azure/azure-sdk-for-java</url>

  <licenses>
    <license>
      <name>The MIT License (MIT)</name>
      <url>http://opensource.org/licenses/MIT</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <scm>
    <url>https://github.com/Azure/azure-sdk-for-java</url>
    <connection>scm:git:**************:Azure/azure-sdk-for-java.git</connection>
    <tag>HEAD</tag>
  </scm>

  <properties>
    <!-- RMJacoco -->
    <jacoco.min.linecoverage>0.10</jacoco.min.linecoverage>
    <jacoco.min.branchcoverage>0.05</jacoco.min.branchcoverage>
    <!-- Configures the Java 9+ run to perform the required module exports, opens, and reads that are necessary for testing but shouldn't be part of the module-info. -->
    <javaModulesSurefireArgLine>
      --add-opens com.azure.resourcemanager.dns/com.azure.resourcemanager.dns=ALL-UNNAMED
    </javaModulesSurefireArgLine>
  </properties>

  <developers>
    <developer>
      <id>microsoft</id>
      <name>Microsoft</name>
    </developer>
  </developers>

  <dependencies>
    <dependency>
      <groupId>com.azure.resourcemanager</groupId>
      <artifactId>azure-resourcemanager-resources</artifactId>
      <version>2.17.0</version> <!-- {x-version-update;com.azure.resourcemanager:azure-resourcemanager-resources;current} -->
    </dependency>
    <dependency>
      <groupId>commons-io</groupId>
      <artifactId>commons-io</artifactId>
      <version>2.11.0</version> <!-- {x-version-update;commons-io:commons-io;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-simple</artifactId>
      <version>1.7.36</version> <!-- {x-version-update;org.slf4j:slf4j-simple;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-engine</artifactId>
      <version>5.8.2</version> <!-- {x-version-update;org.junit.jupiter:junit-jupiter-engine;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>org.junit.jupiter</groupId>
      <artifactId>junit-jupiter-api</artifactId>
      <version>5.8.2</version> <!-- {x-version-update;org.junit.jupiter:junit-jupiter-api;external_dependency} -->
      <scope>test</scope>
    </dependency>
    <dependency>
      <groupId>com.azure</groupId>
      <artifactId>azure-core-http-netty</artifactId>
      <version>1.12.3</version> <!-- {x-version-update;com.azure:azure-core-http-netty;dependency} -->
      <scope>test</scope>
    </dependency>
  </dependencies>
  <profiles>
    <profile>
      <id>azure-mgmt-sdk-test-jar</id>
      <activation>
        <property>
          <name>!maven.test.skip</name>
        </property>
      </activation>
      <dependencies>
        <dependency>
          <groupId>com.azure.resourcemanager</groupId>
          <artifactId>azure-resourcemanager-test</artifactId>
          <version>2.0.0-beta.1</version> <!-- {x-version-update;com.azure.resourcemanager:azure-resourcemanager-test;current} -->
          <scope>test</scope>
        </dependency>
      </dependencies>
    </profile>
  </profiles>
  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-jar-plugin</artifactId>
        <version>3.1.2</version> <!-- {x-version-update;org.apache.maven.plugins:maven-jar-plugin;external_dependency} -->
        <configuration>
          <archive>
            <manifest>
              <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
              <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
            </manifest>
          </archive>
        </configuration>
      </plugin>

      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.8</version> <!-- {x-version-update;org.jacoco:jacoco-maven-plugin;external_dependency} -->
        <configuration>
          <excludes>
            <exclude>com/azure/resourcemanager/**/fluent/**/*</exclude>
            <exclude>com/azure/resourcemanager/**/models/**/*</exclude>
            <exclude>com/azure/resourcemanager/**/implementation/*ClientImpl*</exclude>
            <exclude>com/azure/resourcemanager/**/implementation/*ClientBuilder*</exclude>
          </excludes>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
