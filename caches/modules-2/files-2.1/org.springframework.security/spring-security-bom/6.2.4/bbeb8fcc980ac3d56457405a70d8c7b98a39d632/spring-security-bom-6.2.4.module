{"formatVersion": "1.1", "component": {"group": "org.springframework.security", "module": "spring-security-bom", "version": "6.2.4", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.7"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "platform", "org.gradle.usage": "java-api"}, "dependencyConstraints": [{"group": "org.springframework.security", "module": "spring-security-acl", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-aspects", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-cas", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-config", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-core", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-crypto", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-data", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-ldap", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-messaging", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-oauth2-client", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-oauth2-core", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-oauth2-jose", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-oauth2-resource-server", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-rsocket", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-saml2-service-provider", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-taglibs", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-test", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-web", "version": {"requires": "6.2.4"}}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "platform", "org.gradle.usage": "java-runtime"}, "dependencyConstraints": [{"group": "org.springframework.security", "module": "spring-security-acl", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-aspects", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-cas", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-config", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-core", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-crypto", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-data", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-ldap", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-messaging", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-oauth2-client", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-oauth2-core", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-oauth2-jose", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-oauth2-resource-server", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-rsocket", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-saml2-service-provider", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-taglibs", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-test", "version": {"requires": "6.2.4"}}, {"group": "org.springframework.security", "module": "spring-security-web", "version": {"requires": "6.2.4"}}]}]}