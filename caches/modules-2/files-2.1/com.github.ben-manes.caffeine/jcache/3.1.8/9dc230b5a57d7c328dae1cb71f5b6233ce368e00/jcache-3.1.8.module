{"formatVersion": "1.1", "component": {"group": "com.github.ben-manes.caffeine", "module": "jcache", "version": "3.1.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.3-rc-3"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 11, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.github.ben-manes.caffeine", "module": "caffeine", "version": {"requires": "3.1.8"}}, {"group": "org.osgi", "module": "org.osgi.service.component.annotations", "version": {"requires": "1.5.1"}}, {"group": "jakarta.inject", "module": "jakarta.inject-api", "version": {"requires": "2.0.1"}}, {"group": "javax.cache", "module": "cache-api", "version": {"requires": "1.1.1"}}, {"group": "com.typesafe", "module": "config", "version": {"requires": "1.4.2"}}], "files": [{"name": "jcache-3.1.8.jar", "url": "jcache-3.1.8.jar", "size": 115786, "sha512": "e21b4b79bceba32f814fa30e7bbce729b7263051b4ea6861f86fdb2cd5decdc34ead744f320c88ff6edfcaa7ca27245fe4d0b51ccd9ac1da09516b3eaa4cb7fc", "sha256": "c240d76b7491d610e25f24662486ffd19493468743349305550027f8ba171379", "sha1": "e9df7e6e95325b2bf5457127ea723f359ad190f6", "md5": "e974e3b33debbbb0577d589414a1dc6a"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 11, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.github.ben-manes.caffeine", "module": "caffeine", "version": {"requires": "3.1.8"}}, {"group": "org.osgi", "module": "org.osgi.service.component.annotations", "version": {"requires": "1.5.1"}}, {"group": "jakarta.inject", "module": "jakarta.inject-api", "version": {"requires": "2.0.1"}}, {"group": "javax.cache", "module": "cache-api", "version": {"requires": "1.1.1"}}, {"group": "com.typesafe", "module": "config", "version": {"requires": "1.4.2"}}], "files": [{"name": "jcache-3.1.8.jar", "url": "jcache-3.1.8.jar", "size": 115786, "sha512": "e21b4b79bceba32f814fa30e7bbce729b7263051b4ea6861f86fdb2cd5decdc34ead744f320c88ff6edfcaa7ca27245fe4d0b51ccd9ac1da09516b3eaa4cb7fc", "sha256": "c240d76b7491d610e25f24662486ffd19493468743349305550027f8ba171379", "sha1": "e9df7e6e95325b2bf5457127ea723f359ad190f6", "md5": "e974e3b33debbbb0577d589414a1dc6a"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "jcache-3.1.8-javadoc.jar", "url": "jcache-3.1.8-javadoc.jar", "size": 447597, "sha512": "d0e9b36d0653ce39eb8e5d06653ccaf7705f04555a970aeed9bc1009c51a17b04d0cfb25d412ee8f1ec779d8d7897f6b5b8e85fab9891a51255e4d13dd24ef0b", "sha256": "43a788c714cde17abe9befb928e443e98d712f8e373e718b0baf1ea52dcacebe", "sha1": "ef39c893c7875267c44e7c9a30821c089dd14a38", "md5": "6d7fb0889a8c3e4c8009560692d52c01"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "jcache-3.1.8-sources.jar", "url": "jcache-3.1.8-sources.jar", "size": 63091, "sha512": "e50d151d4435e386d104b931c94309789ade54468d0a8a2ccbecbd29bc86c3edc9443fe1741b62120238a3ced3419aec632194e0c2f3e3661ccbf40192a9da60", "sha256": "c42449f0bddcddf33688e8bb693c624451cd4dab46a482af3e8f7d4f044521c6", "sha1": "73d1cdb798c723470904320d4194d4bfa0199ff8", "md5": "583cdced0e235f1b1cd3d1fc8ba53f9d"}]}]}