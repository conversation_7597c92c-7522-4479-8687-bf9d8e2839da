{"formatVersion": "1.1", "component": {"group": "com.github.ben-manes.caffeine", "module": "guava", "version": "3.1.8", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.3-rc-3"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 11, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.github.ben-manes.caffeine", "module": "caffeine", "version": {"requires": "3.1.8"}}, {"group": "com.google.guava", "module": "guava", "version": {"requires": "32.1.2-jre"}}], "files": [{"name": "guava-3.1.8.jar", "url": "guava-3.1.8.jar", "size": 27161, "sha512": "d3a94b9f4a6a1fd8707f5f1ac8c338876f821a78bbd742a90b139a66b98b64103c0a0090e77b7a78daa98d2ba75cfe09670d9d26d5909aa5363be9dd12db91ea", "sha256": "e45c7c2db18810644c12bb3396cd38dbf4efaa1fa2402f27aaef6e662d8a0af5", "sha1": "b31d9027ac6f6793aaea27ef6dc6fed1b4120ccd", "md5": "fc88ab1004cc4736c2b0f606c255fc6d"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 11, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.github.ben-manes.caffeine", "module": "caffeine", "version": {"requires": "3.1.8"}}, {"group": "com.google.guava", "module": "guava", "version": {"requires": "32.1.2-jre"}}], "files": [{"name": "guava-3.1.8.jar", "url": "guava-3.1.8.jar", "size": 27161, "sha512": "d3a94b9f4a6a1fd8707f5f1ac8c338876f821a78bbd742a90b139a66b98b64103c0a0090e77b7a78daa98d2ba75cfe09670d9d26d5909aa5363be9dd12db91ea", "sha256": "e45c7c2db18810644c12bb3396cd38dbf4efaa1fa2402f27aaef6e662d8a0af5", "sha1": "b31d9027ac6f6793aaea27ef6dc6fed1b4120ccd", "md5": "fc88ab1004cc4736c2b0f606c255fc6d"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "guava-3.1.8-javadoc.jar", "url": "guava-3.1.8-javadoc.jar", "size": 286473, "sha512": "b7e73fd3e7bd9f30dd37c45f5662a03acaaa9efee4b94b9c11ed7b8cd538f47922c69fc44df7816a6d19e0e22b2355cffa1071627402039061025d9c57f503f2", "sha256": "7d7841de246a65d180285c1ad9f2f466abc371e65447e5c782c3206e558a13de", "sha1": "9dd532d00f48d75f68a1f335bb40e69b02d7bdd9", "md5": "9d3b563a6302c28cee33564be1a226c7"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "guava-3.1.8-sources.jar", "url": "guava-3.1.8-sources.jar", "size": 7695, "sha512": "e185c37090e76b6ceffbdd3ce4c5492c6c5ea7948e53b8f2b0d8ec0d044bbef8e8ea7a080cfce32e495c8ebfcebbf28153a1a58a3ac51e12bd825db32386aba1", "sha256": "990a975c90d7070fc11c035474577ebd0a9ebf93e30bcb17abd413804f60f14b", "sha1": "e2a144d58c38e6613dc3359d55d918d1eed600ca", "md5": "78c7f2637fdc6d9514b42c5bfa7ef427"}]}]}