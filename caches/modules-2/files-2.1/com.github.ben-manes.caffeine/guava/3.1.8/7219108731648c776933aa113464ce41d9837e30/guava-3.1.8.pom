<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Grad<PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.github.ben-manes.caffeine</groupId>
  <artifactId>guava</artifactId>
  <version>3.1.8</version>
  <name>Caffeine cache</name>
  <description>A high performance caching library</description>
  <url>https://github.com/ben-manes/caffeine</url>
  <inceptionYear>2014</inceptionYear>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>ben-manes</id>
      <name>Ben Manes</name>
      <email><EMAIL></email>
      <roles>
        <role>owner</role>
        <role>developer</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:https://github.com/ben-manes/caffeine.git</connection>
    <developerConnection>scm:git:ssh://**************/ben-manes/caffeine.git</developerConnection>
    <url>https://github.com/ben-manes/caffeine</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>com.github.ben-manes.caffeine</groupId>
      <artifactId>caffeine</artifactId>
      <version>3.1.8</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>guava</artifactId>
      <version>32.1.2-jre</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
