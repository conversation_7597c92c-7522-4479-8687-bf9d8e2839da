{"formatVersion": "1.1", "component": {"group": "org.springframework", "module": "spring-test", "version": "6.1.6", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.7"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "org.springframework", "module": "spring-core", "version": {"requires": "6.1.6"}}], "files": [{"name": "spring-test-6.1.6.jar", "url": "spring-test-6.1.6.jar", "size": 854520, "sha512": "a3ca2d22d7357ee38d90d5ee16b3872a5ec116b78ce1eb95d755f9fb9223ff679369577664719234822d0034e6ca1a5ae12ff5f3c55c4a86fb58ca2fe2d8adc9", "sha256": "ea50a5bb76e8a01eb80e089562a9b7c84f935f4cafb4ae45cad153196e27afac", "sha1": "7c2662da187db28f7721ff05c9718f1e3181c847", "md5": "4336ffe17484ace2a19c8164229b7531"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "org.springframework", "module": "spring-core", "version": {"requires": "6.1.6"}}], "files": [{"name": "spring-test-6.1.6.jar", "url": "spring-test-6.1.6.jar", "size": 854520, "sha512": "a3ca2d22d7357ee38d90d5ee16b3872a5ec116b78ce1eb95d755f9fb9223ff679369577664719234822d0034e6ca1a5ae12ff5f3c55c4a86fb58ca2fe2d8adc9", "sha256": "ea50a5bb76e8a01eb80e089562a9b7c84f935f4cafb4ae45cad153196e27afac", "sha1": "7c2662da187db28f7721ff05c9718f1e3181c847", "md5": "4336ffe17484ace2a19c8164229b7531"}]}]}