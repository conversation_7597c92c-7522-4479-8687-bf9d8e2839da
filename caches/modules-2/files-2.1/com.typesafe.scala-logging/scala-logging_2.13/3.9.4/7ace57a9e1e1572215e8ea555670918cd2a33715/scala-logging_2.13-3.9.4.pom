<?xml version='1.0' encoding='UTF-8'?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns="http://maven.apache.org/POM/4.0.0">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.typesafe.scala-logging</groupId>
    <artifactId>scala-logging_2.13</artifactId>
    <packaging>bundle</packaging>
    <description>scala-logging</description>
    <url>https://github.com/lightbend/scala-logging</url>
    <version>3.9.4</version>
    <licenses>
        <license>
            <name>Apache 2.0 License</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.html</url>
            <distribution>repo</distribution>
        </license>
    </licenses>
    <name>scala-logging</name>
    <organization>
        <name>com.typesafe.scala-logging</name>
        <url>https://github.com/lightbend/scala-logging</url>
    </organization>
    <scm>
        <url>https://github.com/lightbend/scala-logging</url>
        <connection>scm:git:**************:lightbend/scala-logging.git</connection>
    </scm>
    <developers>
        <developer>
            <id>hseeberger</id>
            <name>Heiko Seeberger</name>
            <url>http://heikoseeberger.de</url>
        </developer>
        <developer>
            <id>analytically</id>
            <name>Mathias Bogaert</name>
            <url>http://twitter.com/analytically</url>
        </developer>
    </developers>
    <dependencies>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-library</artifactId>
            <version>2.13.5</version>
        </dependency>
        <dependency>
            <groupId>org.scala-lang</groupId>
            <artifactId>scala-reflect</artifactId>
            <version>2.13.5</version>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.30</version>
        </dependency>
        <dependency>
            <groupId>ch.qos.logback</groupId>
            <artifactId>logback-classic</artifactId>
            <version>1.2.3</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalatestplus</groupId>
            <artifactId>mockito-3-4_2.13</artifactId>
            <version>3.2.9.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.scalatest</groupId>
            <artifactId>scalatest_2.13</artifactId>
            <version>3.2.9</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
</project>