<?xml version="1.0"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.amazonaws</groupId>
    <artifactId>aws-java-sdk-pom</artifactId>
    <version>1.12.781</version>
  </parent>
  <groupId>com.amazonaws</groupId>
  <artifactId>jmespath-java</artifactId>
  <name>JMES Path Query library</name>
  <description>Implementation of the JMES Path JSON Query langauge for Java.</description>
  <url>https://aws.amazon.com/sdkforjava</url>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://aws.amazon.com/apache2.0</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>amazonwebservices</id>
      <organization>Amazon Web Services</organization>
      <organizationUrl>https://aws.amazon.com</organizationUrl>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <url>https://github.com/aws/aws-sdk-java.git</url>
  </scm>

  <dependencies>
      <dependency>
          <groupId>com.fasterxml.jackson.core</groupId>
          <artifactId>jackson-databind</artifactId>
          <version>${jackson.databind.version}</version>
      </dependency>
      <dependency>
          <groupId>junit</groupId>
          <artifactId>junit</artifactId>
          <scope>test</scope>
          <version>4.13.1</version>
      </dependency>
  </dependencies>

  <build>
      <plugins>
          <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-compiler-plugin</artifactId>
          </plugin>

          <plugin>
              <groupId>org.apache.maven.plugins</groupId>
              <artifactId>maven-javadoc-plugin</artifactId>
          </plugin>
      </plugins>
      <pluginManagement>
          <plugins>
              <plugin>
                  <groupId>org.apache.maven.plugins</groupId>
                  <artifactId>maven-compiler-plugin</artifactId>
                  <version>3.6.0</version>
                  <configuration>
                      <source>1.8</source>
                      <target>1.8</target>
                      <encoding>UTF-8</encoding>
                      <forceJavacCompilerUse>true</forceJavacCompilerUse>
                  </configuration>
              </plugin>
          </plugins>
      </pluginManagement>
  </build>

  <profiles>
      <profile>
	  <id>publishing</id>
	  <build>
	      <plugins>
		  <plugin>
		      <groupId>org.apache.maven.plugins</groupId>
		      <artifactId>maven-gpg-plugin</artifactId>
		      <executions>
			  <execution>
			      <id>sign-artifacts</id>
			      <phase>verify</phase>
			      <goals>
				  <goal>sign</goal>
			      </goals>
			  </execution>
		      </executions>
		  </plugin>
		  <plugin>
		      <groupId>org.sonatype.plugins</groupId>
		      <artifactId>nexus-staging-maven-plugin</artifactId>
		      <version>1.5.1</version>
		      <extensions>true</extensions>
		      <configuration>
			  <serverId>sonatype-nexus-staging</serverId>
			  <nexusUrl>https://aws.oss.sonatype.org</nexusUrl>
		      </configuration>
		  </plugin>
	      </plugins>
	  </build>
      </profile>
  </profiles>

</project>
