<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson.dataformat</groupId>
    <artifactId>jackson-dataformats-binary</artifactId>
    <version>2.15.4</version>
  </parent>
  <artifactId>jackson-dataformat-cbor</artifactId>
  <name>Jackson dataformat: CBOR</name>
  <packaging>jar</packaging>
  <description>Support for reading and writing Concise Binary Object Representation
([CBOR](https://www.rfc-editor.org/info/rfc7049)
encoded data using Jackson abstractions (streaming API, data binding, tree model)
  </description>
  <url>https://github.com/FasterXML/jackson-dataformats-binary</url>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <properties>
    <!-- Generate PackageVersion.java into this directory. -->
    <packageVersion.dir>com/fasterxml/jackson/dataformat/cbor</packageVersion.dir>
    <packageVersion.package>${project.groupId}.cbor</packageVersion.package>
    <!--
     | Default onfiguration properties for the OSGi maven-bundle-plugin work ok
    -->
  </properties>

  <dependencies>
    <!-- 23-Mar-2020, tatu: for 2.10 we have `CBORMapper` which needs databind, too;
        added as full dependency in 2.11
      -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>
    <!-- for testing we need annotations -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
      <scope>provided</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>generate-sources</phase>
          </execution>
        </executions>
      </plugin>
      <!--  19-Mar-2019, tatu: Add rudimentary JDK9+ module info. To build with JDK 8
             will have to use `moduleInfoFile` as anything else requires JDK 9+
        -->
      <plugin>
	<groupId>org.moditect</groupId>
	<artifactId>moditect-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>
