<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson</groupId>
    <artifactId>jackson-base</artifactId>
    <version>2.15.4</version>
  </parent>
  <groupId>com.fasterxml.jackson.dataformat</groupId>
  <artifactId>jackson-dataformats-text</artifactId>
  <name>Jackson dataformats: Text</name>
  <version>2.15.4</version>
  <packaging>pom</packaging>
  <description>Parent pom for Jackson text-based dataformats (as opposed to binary).
  </description>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <modules>
    <module>csv</module>
    <module>properties</module>
    <module>yaml</module>
    <module>toml</module>
  </modules>

  <url>https://github.com/FasterXML/jackson-dataformats-text</url>
  <scm>
    <connection>scm:git:**************:FasterXML/jackson-dataformats-text.git</connection>
    <developerConnection>scm:git:**************:FasterXML/jackson-dataformats-text.git</developerConnection>
    <url>https://github.com/FasterXML/jackson-dataformats-text</url>
    <tag>jackson-dataformats-text-2.15.4</tag>
  </scm>
  <issueManagement>
    <url>https://github.com/FasterXML/jackson-dataformats-text/issues</url>
  </issueManagement>

  <properties>
    <!-- for Reproducible Builds -->
    <project.build.outputTimestamp>2024-02-15T18:00:30Z</project.build.outputTimestamp>
  </properties>

  <dependencies>
    <!-- all dataformats extend core so just include here -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
    </dependency>
  </dependencies>

  <!-- Alas, need to include snapshot reference since otherwise can not find
       snapshot of parent... -->
  <repositories>
    <repository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
      <releases><enabled>false</enabled></releases>
      <snapshots><enabled>true</enabled></snapshots>
    </repository>
  </repositories>

  <build>
    <pluginManagement>
      <plugins>
        <plugin>
          <!-- Inherited from oss-base. Generate PackageVersion.java.-->
          <groupId>com.google.code.maven-replacer-plugin</groupId>
          <artifactId>replacer</artifactId>
          <executions>
            <execution>
              <id>process-packageVersion</id>
              <phase>generate-sources</phase>
            </execution>
          </executions>
        </plugin>

        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <excludes>
              <exclude>com/fasterxml/jackson/**/failing/*.java</exclude>
            </excludes>
          </configuration>
        </plugin>
      </plugins>
    </pluginManagement>

    <!-- 05-Jul-2020, tatu: Add generation of Gradle Module Metadata -->
    <plugins>
      <plugin>
        <groupId>de.jjohannes</groupId>
        <artifactId>gradle-module-metadata-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

</project>
