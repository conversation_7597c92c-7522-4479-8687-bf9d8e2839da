{"formatVersion": "1.1", "component": {"group": "com.fasterxml.jackson.dataformat", "module": "jackson-dataformat-xml", "version": "2.15.4", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"maven": {"version": "3.6.3"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.fasterxml.jackson.core", "module": "jackson-core", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-annotations", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.15.4"}}, {"group": "org.codehaus.woodstox", "module": "stax2-api", "version": {"requires": "4.2.1"}, "excludes": [{"group": "javax.xml.stream", "module": "stax-api"}]}, {"group": "com.fasterxml.woodstox", "module": "woodstox-core", "version": {"requires": "6.5.1"}, "excludes": [{"group": "javax.xml.stream", "module": "stax-api"}]}, {"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.15.4"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-dataformat-xml-2.15.4.jar", "url": "jackson-dataformat-xml-2.15.4.jar", "size": 126136, "sha512": "2aabec4d0abd425dabeda5f3a668b4f8d1844ee82e600274fbba20b8cebac039df877424ecca56972993ad9c1450b8ee6402b6ddc1a4489beb5124fcd1d873a2", "sha256": "90d8109cda7b90c494a7bfde44e96e2fa25021191b67a5924dfa5cbd698025c3", "sha1": "d302ab26ceaf51ac7b5c8097a94e13da09c5983f", "md5": "634b13c9577a22ca187de9ff683d4406"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.fasterxml.jackson.core", "module": "jackson-core", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-annotations", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.15.4"}}, {"group": "org.codehaus.woodstox", "module": "stax2-api", "version": {"requires": "4.2.1"}, "excludes": [{"group": "javax.xml.stream", "module": "stax-api"}]}, {"group": "com.fasterxml.woodstox", "module": "woodstox-core", "version": {"requires": "6.5.1"}, "excludes": [{"group": "javax.xml.stream", "module": "stax-api"}]}, {"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.15.4"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-dataformat-xml-2.15.4.jar", "url": "jackson-dataformat-xml-2.15.4.jar", "size": 126136, "sha512": "2aabec4d0abd425dabeda5f3a668b4f8d1844ee82e600274fbba20b8cebac039df877424ecca56972993ad9c1450b8ee6402b6ddc1a4489beb5124fcd1d873a2", "sha256": "90d8109cda7b90c494a7bfde44e96e2fa25021191b67a5924dfa5cbd698025c3", "sha1": "d302ab26ceaf51ac7b5c8097a94e13da09c5983f", "md5": "634b13c9577a22ca187de9ff683d4406"}]}]}