<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion> 
  <parent>
    <groupId>com.fasterxml.jackson</groupId>
    <artifactId>jackson-base</artifactId>
    <version>2.13.3</version>
  </parent>
  <groupId>com.fasterxml.jackson.dataformat</groupId>
  <artifactId>jackson-dataformat-xml</artifactId>
  <version>2.13.3</version>
  <name>Jackson-dataformat-XML</name>
  <packaging>bundle</packaging>
  <description>Data format extension for Jackson to offer
alternative support for serializing POJOs as XML and deserializing XML as pojos.
  </description>
  <url>https://github.com/FasterXML/jackson-dataformat-xml</url>
  <scm>
    <connection>scm:git:**************:FasterXML/jackson-dataformat-xml.git</connection>
    <developerConnection>scm:git:**************:FasterXML/jackson-dataformat-xml.git</developerConnection>
    <url>http://github.com/FasterXML/jackson-dataformat-xml</url>    
    <tag>jackson-dataformat-xml-2.13.3</tag>
  </scm>
  <properties>
    <packageVersion.dir>com/fasterxml/jackson/dataformat/xml</packageVersion.dir>
    <packageVersion.package>${project.groupId}.xml</packageVersion.package>

    <!-- Default export should work fine... -->
    <!-- And presumably import too? -->
  </properties>

  <dependencies>
    <!-- Extends Jackson (jackson-mapper); requires Stax API (and implementation on
         deploy time), Stax2 API.
      -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-databind</artifactId>
    </dependency>

    <!-- 20-Mar-2019, tatu: Stax-api been part of JDK since Java 6, so let's drop
        dependency as it causes issues with JDK9+ module info
      -->
    <!--
    <dependency>
      <groupId>javax.xml.stream</groupId>
      <artifactId>stax-api</artifactId>
      <version>1.0-2</version>
      <scope>provided</scope>
    </dependency>
    -->

    <!--  But Stax2 API must be included -->
    <dependency>
      <groupId>org.codehaus.woodstox</groupId>
      <artifactId>stax2-api</artifactId>
      <version>4.2.1</version>
      <exclusions>
        <exclusion>
	  <groupId>javax.xml.stream</groupId>
	  <artifactId>stax-api</artifactId>
        </exclusion>
      </exclusions> 
    </dependency>
    <!--  and a Stax impl is needed: SJSXP (from JDK 1.6) might work, but always   
          has odd issues. Let's default to Woodstox: caller can upgrade to Aalto
         (needs to block this dep)
      -->
    <!-- 09-May-2016, tatu: With Jackson 2.8, let's make this compile-dep to make it
        less likely users accidentally try to use Sjsxp from JDK, which leads to probs
      -->
    <!-- 16-Jul-2016, tatu: For 2.10, need Woodstox 6.x to get module info -->
    <dependency>
      <groupId>com.fasterxml.woodstox</groupId>
      <artifactId>woodstox-core</artifactId>
      <version>6.2.7</version>
      <exclusions>
        <exclusion>
	  <groupId>javax.xml.stream</groupId>
	  <artifactId>stax-api</artifactId>
        </exclusion>
      </exclusions> 
    </dependency>

    <!--  Jakarta XMLBind (nee javax/jaxb( annotation introspector is needed BUT ONLY
          for tests (starting 2.13: previously compile/runtime)
       -->
    <dependency>
      <groupId>com.fasterxml.jackson.module</groupId>
      <artifactId>jackson-module-jakarta-xmlbind-annotations</artifactId>
      <scope>test</scope>
    </dependency>

    <!-- 03-Jul-2021, tatu: We may or may not need this, but only for tests
      -->
    <dependency>
      <groupId>jakarta.xml.bind</groupId>
      <artifactId>jakarta.xml.bind-api</artifactId>
      <version>3.0.1</version>
      <scope>test</scope>
    </dependency>
    <!-- 03-Jul-2021, tatu: For sanity checking of non-Woodstox impl usage
        need, say, Sjsxp
      -->
    <dependency>
      <groupId>com.sun.xml.stream</groupId>
      <artifactId>sjsxp</artifactId>
      <version>1.0.2</version>
      <scope>test</scope>
    </dependency>

  </dependencies>

  <build>
    <plugins>
      <plugin>
	<groupId>org.jacoco</groupId>
	<artifactId>jacoco-maven-plugin</artifactId>
	<executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <!-- attached to Maven test phase -->
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>

      <plugin>
        <!-- Inherited from oss-base. Generate PackageVersion.java.-->
        <groupId>com.google.code.maven-replacer-plugin</groupId>
        <artifactId>replacer</artifactId>
        <executions>
          <execution>
            <id>process-packageVersion</id>
            <phase>generate-sources</phase>
          </execution>
        </executions>
      </plugin>
      <!--  Need to skip known-failing tests for build... -->
      <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>${version.plugin.surefire}</version>
          <configuration>
            <excludes>
              <exclude>com/fasterxml/jackson/dataformat/xml/failing/*.java</exclude>
            </excludes>
            <includes>
              <include>**/Test*.java</include>
              <include>**/*Test.java</include>
            </includes>
          </configuration>
        </plugin>
      <!-- 20-Mar-2019, tatu: use Moditect for JDK9+ module info inclusion -->
      <plugin>
	<groupId>org.moditect</groupId>
	<artifactId>moditect-maven-plugin</artifactId>
      </plugin>
      <!-- 05-Jul-2020, tatu: Add gradle module metadata -->
      <plugin>
        <groupId>de.jjohannes</groupId>
        <artifactId>gradle-module-metadata-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

  <!-- Alas, need to include snapshot reference since otherwise can not find
       snapshot of parent... -->
  <repositories>
    <repository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
      <releases><enabled>false</enabled></releases>
      <snapshots><enabled>true</enabled></snapshots>
    </repository>
  </repositories>

</project>
