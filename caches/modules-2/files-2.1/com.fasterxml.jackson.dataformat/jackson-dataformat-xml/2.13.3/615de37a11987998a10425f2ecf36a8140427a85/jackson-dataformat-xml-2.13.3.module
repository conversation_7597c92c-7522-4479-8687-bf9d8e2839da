{"formatVersion": "1.1", "component": {"group": "com.fasterxml.jackson.dataformat", "module": "jackson-dataformat-xml", "version": "2.13.3", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"maven": {"version": "3.8.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.fasterxml.jackson.core", "module": "jackson-core", "version": {"requires": "2.13.3"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-annotations", "version": {"requires": "2.13.3"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.13.3"}}, {"group": "org.codehaus.woodstox", "module": "stax2-api", "version": {"requires": "4.2.1"}, "excludes": [{"group": "javax.xml.stream", "module": "stax-api"}]}, {"group": "com.fasterxml.woodstox", "module": "woodstox-core", "version": {"requires": "6.2.7"}, "excludes": [{"group": "javax.xml.stream", "module": "stax-api"}]}, {"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.13.3"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-dataformat-xml-2.13.3.jar", "url": "jackson-dataformat-xml-2.13.3.jar", "size": 122296, "sha512": "778b2358401997b03416998b534a99545c26d1256478e1992977cadea250a7796d09e63fd908ddd68362a11de563fe9ef6d6d064fd9f95c83c673080599f58c0", "sha256": "ab7aa994b1c514ca37e9721f0e79bb20d9aa31df9f9d8286cbd2b9e71235e44e", "sha1": "ec52dc41977a927a6ff175042576d716cd55c7c5", "md5": "d2215f4b13497b9cced1f981ada9d6f6"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.fasterxml.jackson.core", "module": "jackson-core", "version": {"requires": "2.13.3"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-annotations", "version": {"requires": "2.13.3"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.13.3"}}, {"group": "org.codehaus.woodstox", "module": "stax2-api", "version": {"requires": "4.2.1"}, "excludes": [{"group": "javax.xml.stream", "module": "stax-api"}]}, {"group": "com.fasterxml.woodstox", "module": "woodstox-core", "version": {"requires": "6.2.7"}, "excludes": [{"group": "javax.xml.stream", "module": "stax-api"}]}, {"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.13.3"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-dataformat-xml-2.13.3.jar", "url": "jackson-dataformat-xml-2.13.3.jar", "size": 122296, "sha512": "778b2358401997b03416998b534a99545c26d1256478e1992977cadea250a7796d09e63fd908ddd68362a11de563fe9ef6d6d064fd9f95c83c673080599f58c0", "sha256": "ab7aa994b1c514ca37e9721f0e79bb20d9aa31df9f9d8286cbd2b9e71235e44e", "sha1": "ec52dc41977a927a6ff175042576d716cd55c7c5", "md5": "d2215f4b13497b9cced1f981ada9d6f6"}]}]}