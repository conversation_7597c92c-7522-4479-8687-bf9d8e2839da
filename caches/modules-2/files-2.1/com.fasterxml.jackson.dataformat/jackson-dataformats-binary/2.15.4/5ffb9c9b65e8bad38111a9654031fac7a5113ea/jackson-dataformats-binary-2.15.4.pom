<?xml version="1.0" encoding="utf-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.fasterxml.jackson</groupId>
    <artifactId>jackson-base</artifactId>
    <version>2.15.4</version>
  </parent>
  <groupId>com.fasterxml.jackson.dataformat</groupId>
  <artifactId>jackson-dataformats-binary</artifactId>
  <name>Jackson dataformats: Binary</name>
  <version>2.15.4</version>
  <packaging>pom</packaging>
  <description>Parent pom for Jackson binary dataformats.
  </description>

  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <modules>
    <module>cbor</module>
    <module>smile</module>
    <module>avro</module>
    <module>protobuf</module>
    <module>ion</module>
  </modules>

  <url>https://github.com/FasterXML/jackson-dataformats-binary</url>
  <scm>
    <connection>scm:git:**************:FasterXML/jackson-dataformats-binary.git</connection>
    <developerConnection>scm:git:**************:FasterXML/jackson-dataformats-binary.git</developerConnection>
    <url>https://github.com/FasterXML/jackson-dataformats-binary</url>
    <tag>jackson-dataformats-binary-2.15.4</tag>
  </scm>
  <issueManagement>
    <url>https://github.com/FasterXML/jackson-dataformats-binary/issues</url>
  </issueManagement>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>

    <!-- for Reproducible Builds -->
    <project.build.outputTimestamp>2024-02-15T18:00:18Z</project.build.outputTimestamp>

    <!-- 20-Mar-2021, tatu: limit heap for tests to catch some
	 of "too big allocation" cases
         SEE: https://www.jacoco.org/jacoco/trunk/doc/prepare-agent-mojo.html
	 for explanation on "argLine" property
    -->
    <argLine>-Xmx1024m</argLine>
  </properties>

  <dependencies>
    <!-- all dataformats extend core so just include here -->
    <dependency>
      <groupId>com.fasterxml.jackson.core</groupId>
      <artifactId>jackson-core</artifactId>
    </dependency>
  </dependencies>

  <!-- Alas, need to include snapshot reference since otherwise can not find
       snapshot of parent... -->
  <repositories>
    <repository>
      <id>sonatype-nexus-snapshots</id>
      <name>Sonatype Nexus Snapshots</name>
      <url>https://oss.sonatype.org/content/repositories/snapshots</url>
      <releases><enabled>false</enabled></releases>
      <snapshots><enabled>true</enabled></snapshots>
    </repository>
  </repositories>

  <build>
    <pluginManagement>
      <plugins>
	<plugin>
	  <!-- Inherited from oss-base. Generate PackageVersion.java.-->
          <groupId>com.google.code.maven-replacer-plugin</groupId>
          <artifactId>replacer</artifactId>
	  <executions>
            <execution>
              <id>process-packageVersion</id>
              <phase>generate-sources</phase>
            </execution>
          </executions>
	</plugin>

	<plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <configuration>
            <excludes>
              <exclude>com/fasterxml/jackson/**/failing/*.java</exclude>
            </excludes>
          </configuration>
	</plugin>
      </plugins>
    </pluginManagement>

    <!-- 05-Jul-2020, tatu: Add generation of Gradle Module Metadata -->
    <plugins>
      <plugin>
        <groupId>de.jjohannes</groupId>
        <artifactId>gradle-module-metadata-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>

</project>
