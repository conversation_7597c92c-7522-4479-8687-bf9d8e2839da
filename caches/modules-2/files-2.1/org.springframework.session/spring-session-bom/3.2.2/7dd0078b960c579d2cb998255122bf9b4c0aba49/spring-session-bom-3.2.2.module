{"formatVersion": "1.1", "component": {"group": "org.springframework.session", "module": "spring-session-bom", "version": "3.2.2", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.2.1"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "platform", "org.gradle.usage": "java-api"}, "dependencyConstraints": [{"group": "org.springframework.session", "module": "spring-session-core", "version": {"requires": "3.2.2"}}, {"group": "org.springframework.session", "module": "spring-session-data-mongodb", "version": {"requires": "3.2.2"}}, {"group": "org.springframework.session", "module": "spring-session-data-redis", "version": {"requires": "3.2.2"}}, {"group": "org.springframework.session", "module": "spring-session-hazelcast", "version": {"requires": "3.2.2"}}, {"group": "org.springframework.session", "module": "spring-session-jdbc", "version": {"requires": "3.2.2"}}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "platform", "org.gradle.usage": "java-runtime"}, "dependencyConstraints": [{"group": "org.springframework.session", "module": "spring-session-core", "version": {"requires": "3.2.2"}}, {"group": "org.springframework.session", "module": "spring-session-data-mongodb", "version": {"requires": "3.2.2"}}, {"group": "org.springframework.session", "module": "spring-session-data-redis", "version": {"requires": "3.2.2"}}, {"group": "org.springframework.session", "module": "spring-session-hazelcast", "version": {"requires": "3.2.2"}}, {"group": "org.springframework.session", "module": "spring-session-jdbc", "version": {"requires": "3.2.2"}}]}]}