<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<groupId>org.springframework.data</groupId>
	<artifactId>spring-data-jpa-parent</artifactId>
	<version>3.2.5</version>
	<packaging>pom</packaging>

	<name>Spring Data JPA Parent</name>
	<description>Parent module for Spring Data JPA repositories.</description>
	<url>https://spring.io/projects/spring-data-jpa</url>
	<scm>
		<connection>scm:git:git://github.com:spring-projects/spring-data-jpa.git</connection>
		<developerConnection>scm:git:**************:spring-projects/spring-data-jpa.git</developerConnection>
		<url>https://github.com/spring-projects/spring-data-jpa</url>
	</scm>
	<issueManagement>
		<url>https://github.com/spring-projects/spring-data-jpa/issues</url>
	</issueManagement>

	<parent>
		<groupId>org.springframework.data.build</groupId>
		<artifactId>spring-data-parent</artifactId>
		<version>3.2.5</version>
	</parent>

	<properties>
		<antlr>4.13.0</antlr> <!-- align with Hibernate's parser -->
		<eclipselink>3.0.4</eclipselink>
		<eclipselink-next>4.0.2</eclipselink-next>
		<hibernate>6.4.4.Final</hibernate>
		<hibernate-62>6.2.24.Final</hibernate-62>
		<hibernate-64-snapshots>6.4.5-SNAPSHOT</hibernate-64-snapshots>
		<hibernate-65>6.5.0.CR1</hibernate-65>
		<hibernate-65-snapshots>6.5.0-SNAPSHOT</hibernate-65-snapshots>
		<hibernate-66-snapshots>6.6.0-SNAPSHOT</hibernate-66-snapshots>
		<hsqldb>2.7.1</hsqldb>
		<h2>2.2.220</h2>
		<jakarta-persistence-api>3.1.0</jakarta-persistence-api>
		<jsqlparser>4.5</jsqlparser>
		<mysql-connector-java>8.0.33</mysql-connector-java>
		<postgresql>42.6.0</postgresql>
		<springdata.commons>3.2.5</springdata.commons>
		<vavr>0.10.3</vavr>

		<hibernate.groupId>org.hibernate</hibernate.groupId>

		<sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>

	</properties>

	<modules>
		<module>spring-data-envers</module>
		<module>spring-data-jpa</module>
		<module>spring-data-jpa-distribution</module>
	</modules>

	<profiles>
		<profile>
			<id>hibernate-62</id>
			<properties>
				<hibernate>${hibernate-62}</hibernate>
			</properties>
		</profile>
		<profile>
			<id>hibernate-64-snapshots</id>
			<properties>
				<hibernate>${hibernate-64-snapshots}</hibernate>
			</properties>
			<repositories>
				<repository>
					<id>sonatype-oss</id>
					<url>https://oss.sonatype.org/content/repositories/snapshots</url>
					<releases>
						<enabled>false</enabled>
					</releases>
				</repository>
			</repositories>
		</profile>
		<profile>
			<id>hibernate-65</id>
			<properties>
				<hibernate>${hibernate-65}</hibernate>
			</properties>
		</profile>
		<profile>
			<id>hibernate-65-snapshots</id>
			<properties>
				<hibernate>${hibernate-65-snapshots}</hibernate>
			</properties>
			<repositories>
				<repository>
					<id>sonatype-oss</id>
					<url>https://oss.sonatype.org/content/repositories/snapshots</url>
					<releases>
						<enabled>false</enabled>
					</releases>
				</repository>
			</repositories>
		</profile>
		<profile>
			<id>hibernate-66-snapshots</id>
			<properties>
				<hibernate>${hibernate-66-snapshots}</hibernate>
			</properties>
			<repositories>
				<repository>
					<id>sonatype-oss</id>
					<url>https://oss.sonatype.org/content/repositories/snapshots</url>
					<releases>
						<enabled>false</enabled>
					</releases>
				</repository>
			</repositories>
		</profile>
		<profile>
			<id>all-dbs</id>
			<build>
				<plugins>
					<plugin>
						<groupId>org.apache.maven.plugins</groupId>
						<artifactId>maven-surefire-plugin</artifactId>
						<executions>
							<execution>
								<id>mysql-test</id>
								<phase>test</phase>
								<goals>
									<goal>test</goal>
								</goals>
								<configuration>
									<includes>
										<include>**/MySql*IntegrationTests.java</include>
									</includes>
								</configuration>
							</execution>
							<execution>
								<id>postgres-test</id>
								<phase>test</phase>
								<goals>
									<goal>test</goal>
								</goals>
								<configuration>
									<includes>
										<include>**/Postgres*IntegrationTests.java</include>
									</includes>
								</configuration>
							</execution>
						</executions>
					</plugin>
				</plugins>
			</build>
		</profile>
		<profile>
			<id>eclipselink-next</id>
			<properties>
				<eclipselink>${eclipselink-next}</eclipselink>
			</properties>
		</profile>
	</profiles>

	<dependencyManagement>
		<dependencies>
			<dependency>
				<groupId>org.testcontainers</groupId>
				<artifactId>testcontainers-bom</artifactId>
				<version>${testcontainers}</version>
				<type>pom</type>
				<scope>import</scope>
			</dependency>
		</dependencies>
	</dependencyManagement>

	<dependencies>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-instrument</artifactId>
			<version>${spring}</version>
			<scope>provided</scope>
		</dependency>
	</dependencies>

	<build>
		<plugins>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<dependencies>
					<dependency>
						<groupId>org.springframework</groupId>
						<artifactId>spring-instrument</artifactId>
						<version>${spring}</version>
						<scope>runtime</scope>
					</dependency>
				</dependencies>
				<executions>
					<execution>
						<!-- override the default-test execution and exclude everything -->
						<id>default-test</id>
						<configuration>
							<excludes>
								<exclude>**/*</exclude>
							</excludes>
						</configuration>
					</execution>
					<execution>
						<id>unit-test</id>
						<goals>
							<goal>test</goal>
						</goals>
						<phase>test</phase>
						<configuration>
							<includes>
								<include>**/*UnitTests.java</include>
							</includes>
						</configuration>
					</execution>
					<execution>
						<id>integration-test</id>
						<goals>
							<goal>test</goal>
						</goals>
						<phase>test</phase>
						<configuration>
							<includes>
								<include>**/*IntegrationTests.java</include>
								<include>**/*Tests.java</include>
							</includes>
							<excludes>
								<exclude>**/*UnitTests.java</exclude>
								<exclude>**/OpenJpa*</exclude>
								<exclude>**/EclipseLink*</exclude>
								<exclude>**/MySql*</exclude>
								<exclude>**/Postgres*</exclude>
							</excludes>
							<argLine>
								-javaagent:${settings.localRepository}/org/springframework/spring-instrument/${spring}/spring-instrument-${spring}.jar
							</argLine>
						</configuration>
					</execution>
					<execution>
						<id>eclipselink-test</id>
						<goals>
							<goal>test</goal>
						</goals>
						<phase>test</phase>
						<configuration>
							<includes>
								<include>**/EclipseLink*Tests.java</include>
							</includes>
							<argLine>
								-javaagent:${settings.localRepository}/org/eclipse/persistence/org.eclipse.persistence.jpa/${eclipselink}/org.eclipse.persistence.jpa-${eclipselink}.jar
								-javaagent:${settings.localRepository}/org/springframework/spring-instrument/${spring}/spring-instrument-${spring}.jar
							</argLine>
						</configuration>
					</execution>
				</executions>
			</plugin>

		</plugins>

		<pluginManagement>
			<plugins>
				<plugin>
					<groupId>com.gradle</groupId>
					<artifactId>gradle-enterprise-maven-extension</artifactId>
					<configuration>
						<gradleEnterprise>
							<normalization>
								<runtimeClassPath>
									<ignoredFiles>
										<ignoredFile>builddef.lst</ignoredFile>
									</ignoredFiles>
								</runtimeClassPath>
							</normalization>
						</gradleEnterprise>
					</configuration>
				</plugin>
			</plugins>
		</pluginManagement>
	</build>

	<repositories>
		
		
	</repositories>

	<pluginRepositories>
		<pluginRepository>
			<id>spring-milestone</id>
			<url>https://repo.spring.io/milestone</url>
		</pluginRepository>
	</pluginRepositories>

</project>
