<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<groupId>org.springframework.data</groupId>
	<artifactId>spring-data-keyvalue</artifactId>
	<version>3.2.5</version>

	<name>Spring Data KeyValue</name>

	<parent>
		<groupId>org.springframework.data.build</groupId>
		<artifactId>spring-data-parent</artifactId>
		<version>3.2.5</version>
		<relativePath/>
	</parent>

	<properties>
		<springdata.commons>3.2.5</springdata.commons>
		<java-module-name>spring.data.keyvalue</java-module-name>
	</properties>

	<dependencies>

		<dependency>
			<groupId>org.springframework.data</groupId>
			<artifactId>spring-data-commons</artifactId>
			<version>${springdata.commons}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
		</dependency>

		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-collections</artifactId>
			<version>${querydsl}</version>
			<optional>true</optional>
		</dependency>

	</dependencies>

	<build>
		<plugins>

			<plugin>
				<groupId>com.mysema.maven</groupId>
				<artifactId>apt-maven-plugin</artifactId>
				<version>${apt}</version>
				<executions>
					<execution>
						<phase>generate-test-sources</phase>
						<goals>
							<goal>test-process</goal>
						</goals>
						<configuration>
							<outputDirectory>target/generated-test-sources
							</outputDirectory>
							<processor>com.querydsl.apt.QuerydslAnnotationProcessor</processor>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
			</plugin>

		</plugins>
	</build>

	<profiles>
		<profile>
			<!-- placeholder for no profile -->
			<id>none</id>
		</profile>
		<profile>
			<id>antora-process-resources</id>
			<build>
				<resources>
					<resource>
						<directory>src/main/antora/resources/antora-resources</directory>
						<filtering>true</filtering>
					</resource>
				</resources>
			</build>
		</profile>
		<profile>
			<id>antora</id>
			<build>
				<plugins>
					<plugin>
						<groupId>io.spring.maven.antora</groupId>
						<artifactId>antora-maven-plugin</artifactId>
					</plugin>
				</plugins>
			</build>
		</profile>
	</profiles>

	<repositories>
		
		
	</repositories>

</project>
