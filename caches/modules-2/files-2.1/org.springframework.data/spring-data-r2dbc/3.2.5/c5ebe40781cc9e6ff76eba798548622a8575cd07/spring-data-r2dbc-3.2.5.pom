<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.data</groupId>
    <artifactId>spring-data-relational-parent</artifactId>
    <version>3.2.5</version>
  </parent>
  <groupId>org.springframework.data</groupId>
  <artifactId>spring-data-r2dbc</artifactId>
  <version>3.2.5</version>
  <name>Spring Data R2DBC</name>
  <description>Spring Data module for R2DBC</description>
  <url>https://projects.spring.io/spring-data-r2dbc</url>
  <inceptionYear>2018</inceptionYear>
  <organization>
    <name>Pivotal Software, Inc.</name>
    <url>https://www.spring.io</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
      <comments>Copyright 2008-2020 the original author or authors.

				Licensed under the Apache License, Version 2.0 (the "License");
				you may not use this file except in compliance with the License.
				You may obtain a copy of the License at

				https://www.apache.org/licenses/LICENSE-2.0

				Unless required by applicable law or agreed to in writing, software
				distributed under the License is distributed on an "AS IS" BASIS,
				WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
				implied.
				See the License for the specific language governing permissions and
				limitations under the License.</comments>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>schauder</id>
      <name>Jens Schauder</name>
      <email>jschauder(at)pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://pivotal.io</organizationUrl>
      <roles>
        <role>Project Lead</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>mpaluch</id>
      <name>Mark Paluch</name>
      <email>mpaluch(at)pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://pivotal.io</organizationUrl>
      <roles>
        <role>Project Lead</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>gregturn</id>
      <name>Greg L. Turnquist</name>
      <email>gturnquist(at)pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://pivotal.io</organizationUrl>
      <roles>
        <role>Project Contributor</role>
      </roles>
      <timezone>-6</timezone>
    </developer>
    <developer>
      <id>ogierke</id>
      <name>Oliver Gierke</name>
      <email>ogierke(at)pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://pivotal.io</organizationUrl>
      <roles>
        <role>Project Contributor</role>
      </roles>
      <timezone>+1</timezone>
    </developer>
    <developer>
      <id>kurtn718</id>
      <name>Kurt Niemi</name>
      <email>kniemi(at)vmware.com</email>
      <organization>VMware.</organization>
      <organizationUrl>https://vmware.com</organizationUrl>
      <roles>
        <role>Project Contributor</role>
      </roles>
      <timezone>-5</timezone>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/spring-projects/spring-data-build.git/spring-data-parent/spring-data-relational-parent/spring-data-r2dbc</connection>
    <developerConnection>scm:git:ssh://**************:spring-projects/spring-data-build.git/spring-data-parent/spring-data-relational-parent/spring-data-r2dbc</developerConnection>
    <url>https://github.com/spring-projects/spring-data-build/spring-data-parent/spring-data-relational-parent/spring-data-r2dbc</url>
  </scm>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/spring-projects/spring-data-build/issues</url>
  </issueManagement>
  <properties>
    <reactive-streams.version>1.0.4</reactive-streams.version>
    <r2dbc-h2.version>1.0.0.RELEASE</r2dbc-h2.version>
    <r2dbc-mariadb.version>1.1.4</r2dbc-mariadb.version>
    <r2dbc-mssql.version>1.0.2.RELEASE</r2dbc-mssql.version>
    <dist.id>spring-data-r2dbc</dist.id>
    <java-module-name>spring.data.r2dbc</java-module-name>
    <sonar.dynamicAnalysis>reuseReports</sonar.dynamicAnalysis>
    <r2dbc-spi.version>1.0.0.RELEASE</r2dbc-spi.version>
    <r2dbc-postgresql.version>1.0.5.RELEASE</r2dbc-postgresql.version>
    <degraph-check.version>0.1.4</degraph-check.version>
    <oracle-r2dbc.version>1.1.1</oracle-r2dbc.version>
    <r2dbc-mysql.version>1.0.2</r2dbc-mysql.version>
    <netty>4.1.107.Final</netty>
  </properties>
  <dependencies>
    <dependency>
      <groupId>org.springframework.data</groupId>
      <artifactId>spring-data-relational</artifactId>
      <version>3.2.5</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.data</groupId>
      <artifactId>spring-data-commons</artifactId>
      <version>3.2.5</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-tx</artifactId>
      <version>6.1.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-context</artifactId>
      <version>6.1.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-beans</artifactId>
      <version>6.1.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-jdbc</artifactId>
      <version>6.1.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-core</artifactId>
      <version>6.1.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-r2dbc</artifactId>
      <version>6.1.6</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.r2dbc</groupId>
      <artifactId>r2dbc-spi</artifactId>
      <version>1.0.0.RELEASE</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.projectreactor</groupId>
      <artifactId>reactor-core</artifactId>
      <version>3.6.5</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-stdlib</artifactId>
      <version>1.9.23</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlin</groupId>
      <artifactId>kotlin-reflect</artifactId>
      <version>1.9.23</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlinx</groupId>
      <artifactId>kotlinx-coroutines-core</artifactId>
      <version>1.7.3</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.jetbrains.kotlinx</groupId>
      <artifactId>kotlinx-coroutines-reactor</artifactId>
      <version>1.7.3</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.postgresql</groupId>
      <artifactId>r2dbc-postgresql</artifactId>
      <version>1.0.5.RELEASE</version>
      <scope>compile</scope>
      <optional>true</optional>
    </dependency>
    <dependency>
      <groupId>org.slf4j</groupId>
      <artifactId>slf4j-api</artifactId>
      <version>2.0.2</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
