<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
		 xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<artifactId>spring-data-relational</artifactId>
	<version>3.2.5</version>

	<name>Spring Data Relational</name>
	<description>Spring Data Relational support</description>

	<parent>
		<groupId>org.springframework.data</groupId>
		<artifactId>spring-data-relational-parent</artifactId>
		<version>3.2.5</version>
	</parent>

	<properties>
		<java-module-name>spring.data.relational</java-module-name>
		<project.root>${basedir}/..</project.root>
	</properties>

	<dependencies>

		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>spring-data-commons</artifactId>
			<version>${springdata.commons}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
		</dependency>

		<dependency>
			<groupId>com.google.code.findbugs</groupId>
			<artifactId>jsr305</artifactId>
			<version>${jsr305.version}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.assertj</groupId>
			<artifactId>assertj-core</artifactId>
			<version>${assertj}</version>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>net.bytebuddy</groupId>
					<artifactId>byte-buddy</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>com.tngtech.archunit</groupId>
			<artifactId>archunit</artifactId>
			<version>${archunit.version}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.jetbrains.kotlin</groupId>
			<artifactId>kotlin-stdlib</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>io.mockk</groupId>
			<artifactId>mockk-jvm</artifactId>
			<version>${mockk}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>testcontainers</artifactId>
			<version>${testcontainers}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.github.jsqlparser</groupId>
			<artifactId>jsqlparser</artifactId>
			<version>4.6</version>
		</dependency>

	</dependencies>

</project>
