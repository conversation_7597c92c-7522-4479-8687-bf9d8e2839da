<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<groupId>org.springframework.data</groupId>
	<artifactId>spring-data-commons</artifactId>
	<version>3.2.5</version>

	<name>Spring Data Core</name>
	<description>Core Spring concepts underpinning every Spring Data module.</description>
	<url>https://spring.io/projects/spring-data</url>
	<scm>
		<connection>scm:git:https://github.com/spring-projects/spring-data-commons.git
		</connection>
		<developerConnection>
			scm:git:**************:spring-projects/spring-data-commons.git
		</developerConnection>
		<url>https://github.com/spring-projects/spring-data-commons</url>
	</scm>
	<issueManagement>
		<url>https://github.com/spring-projects/spring-data-commons/issues</url>
	</issueManagement>

	<parent>
		<groupId>org.springframework.data.build</groupId>
		<artifactId>spring-data-parent</artifactId>
		<version>3.2.5</version>
	</parent>

	<properties>

		<scala>2.11.7</scala>
		<xmlbeam>1.4.24</xmlbeam>
		<java-module-name>spring.data.commons</java-module-name>
		<kotlin.api.target>1.8</kotlin.api.target>
	</properties>

	<dependencies>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-expression</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-oxm</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>com.fasterxml.jackson.core</groupId>
			<artifactId>jackson-databind</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-web</artifactId>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webflux</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core-test</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>jakarta.servlet</groupId>
			<artifactId>jakarta.servlet-api</artifactId>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>jakarta.xml.bind</groupId>
			<artifactId>jakarta.xml.bind-api</artifactId>
			<version>${jaxb}</version>
			<scope>provided</scope>
		</dependency>
		<dependency>
			<groupId>jakarta.annotation</groupId>
			<artifactId>jakarta.annotation-api</artifactId>
			<version>${jakarta-annotation-api}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.google.code.findbugs</groupId>
			<artifactId>jsr305</artifactId>
			<version>3.0.2</version>
			<optional>true</optional>
		</dependency>

		<!--  Project Reactor -->

		<dependency>
			<groupId>io.projectreactor</groupId>
			<artifactId>reactor-core</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>io.projectreactor</groupId>
			<artifactId>reactor-test</artifactId>
			<scope>test</scope>
		</dependency>

		<!--  RxJava -->

		<dependency>
			<groupId>io.reactivex.rxjava3</groupId>
			<artifactId>rxjava</artifactId>
			<version>${rxjava3}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>io.smallrye.reactive</groupId>
			<artifactId>mutiny</artifactId>
			<version>${smallrye-mutiny}</version>
			<optional>true</optional>
		</dependency>

		<!--  Querydsl -->

		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-core</artifactId>
			<version>${querydsl}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-collections</artifactId>
			<version>${querydsl}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.google.guava</groupId>
			<artifactId>guava</artifactId>
			<version>${guava}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>io.vavr</groupId>
			<artifactId>vavr</artifactId>
			<version>${vavr}</version>
			<optional>true</optional>
		</dependency>

		<!-- Eclipse Collections -->

		<dependency>
			<groupId>org.eclipse.collections</groupId>
			<artifactId>eclipse-collections-api</artifactId>
			<version>${eclipse-collections}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.eclipse.collections</groupId>
			<artifactId>eclipse-collections</artifactId>
			<version>${eclipse-collections}</version>
			<scope>test</scope>
		</dependency>

		<!-- CDI -->

		<dependency>
			<groupId>jakarta.enterprise</groupId>
			<artifactId>jakarta.enterprise.cdi-api</artifactId>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.openwebbeans</groupId>
			<artifactId>openwebbeans-se</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework.hateoas</groupId>
			<artifactId>spring-hateoas</artifactId>
			<version>${spring-hateoas}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-webmvc</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.sun.xml.bind</groupId>
			<artifactId>jaxb-impl</artifactId>
			<version>3.0.2</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>xmlunit</groupId>
			<artifactId>xmlunit</artifactId>
			<version>1.6</version>
			<scope>test</scope>
		</dependency>

		<!-- Groovy -->
		<dependency>
			<groupId>org.codehaus.groovy</groupId>
			<artifactId>groovy-all</artifactId>
			<version>2.4.4</version>
			<scope>test</scope>
		</dependency>

		<!-- Kotlin extension -->
		<dependency>
			<groupId>org.jetbrains.kotlin</groupId>
			<artifactId>kotlin-stdlib</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.jetbrains.kotlin</groupId>
			<artifactId>kotlin-reflect</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.jetbrains.kotlinx</groupId>
			<artifactId>kotlinx-coroutines-core</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.jetbrains.kotlinx</groupId>
			<artifactId>kotlinx-coroutines-reactive</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.jetbrains.kotlinx</groupId>
			<artifactId>kotlinx-coroutines-reactor</artifactId>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.jetbrains.kotlin</groupId>
			<artifactId>kotlin-test-junit5</artifactId>
			<version>${kotlin}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>io.mockk</groupId>
			<artifactId>mockk-jvm</artifactId>
			<version>${mockk}</version>
			<scope>test</scope>
		</dependency>

		<!-- Scala -->
		<dependency>
			<groupId>org.scala-lang</groupId>
			<artifactId>scala-library</artifactId>
			<version>${scala}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>jakarta.transaction</groupId>
			<artifactId>jakarta.transaction-api</artifactId>
			<version>2.0.0</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.jayway.jsonpath</groupId>
			<artifactId>json-path</artifactId>
			<version>${jsonpath}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.xmlbeam</groupId>
			<artifactId>xmlprojector</artifactId>
			<version>${xmlbeam}</version>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>com.tngtech.archunit</groupId>
			<artifactId>archunit</artifactId>
			<version>${archunit}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.jmolecules.integrations</groupId>
			<artifactId>jmolecules-spring</artifactId>
			<version>${jmolecules-integration}</version>
			<optional>true</optional>
		</dependency>
		<dependency>
			<groupId>org.junit.platform</groupId>
			<artifactId>junit-platform-launcher</artifactId>
			<scope>test</scope>
		</dependency>

	</dependencies>

	<build>
		<plugins>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-assembly-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>io.spring.maven.antora</groupId>
				<artifactId>antora-maven-plugin</artifactId>
			</plugin>
			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<executions>
					<execution>
						<id>java-test-compile</id>
						<configuration>
							<annotationProcessorPaths>
								<path>
									<groupId>com.querydsl</groupId>
									<artifactId>querydsl-apt</artifactId>
									<version>${querydsl}</version>
									<classifier>general</classifier>
								</path>
							</annotationProcessorPaths>
						</configuration>
					</execution>
				</executions>
			</plugin>
		</plugins>
	</build>

	<profiles>
		<profile>
			<id>antora-process-resources</id>
			<build>
				<resources>
					<resource>
						<directory>src/main/antora/resources/antora-resources</directory>
						<filtering>true</filtering>
					</resource>
				</resources>
			</build>
		</profile>
	</profiles>

	<repositories>
		
		
	</repositories>

</project>
