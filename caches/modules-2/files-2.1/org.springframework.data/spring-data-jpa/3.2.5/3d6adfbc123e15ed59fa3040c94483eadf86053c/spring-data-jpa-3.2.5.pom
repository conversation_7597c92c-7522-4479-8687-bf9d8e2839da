<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
		 xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd">

	<modelVersion>4.0.0</modelVersion>

	<groupId>org.springframework.data</groupId>
	<artifactId>spring-data-jpa</artifactId>
	<version>3.2.5</version>

	<name>Spring Data JPA</name>
	<description>Spring Data module for JPA repositories.</description>
	<url>https://projects.spring.io/spring-data-jpa</url>

	<parent>
		<groupId>org.springframework.data</groupId>
		<artifactId>spring-data-jpa-parent</artifactId>
		<version>3.2.5</version>
		<relativePath>../pom.xml</relativePath>
	</parent>

	<properties>
		<java-module-name>spring.data.jpa</java-module-name>
	</properties>

	<dependencies>

		<dependency>
			<groupId>${project.groupId}</groupId>
			<artifactId>spring-data-commons</artifactId>
			<version>${springdata.commons}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-orm</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-context</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aop</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-tx</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-beans</artifactId>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-instrument</artifactId>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-core</artifactId>
			<exclusions>
				<exclusion>
					<groupId>commons-logging</groupId>
					<artifactId>commons-logging</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>org.antlr</groupId>
			<artifactId>antlr4-runtime</artifactId>
			<version>${antlr}</version>
		</dependency>

		<dependency>
			<groupId>org.springframework</groupId>
			<artifactId>spring-aspects</artifactId>
			<scope>compile</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.hsqldb</groupId>
			<artifactId>hsqldb</artifactId>
			<version>${hsqldb}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.h2database</groupId>
			<artifactId>h2</artifactId>
			<version>${h2}</version>
			<scope>test</scope>
		</dependency>

		<!-- MySQL testing support -->
		<dependency>
			<groupId>com.mysql</groupId>
			<artifactId>mysql-connector-j</artifactId>
			<version>${mysql-connector-java}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>mysql</artifactId>
			<scope>test</scope>
			<exclusions>
				<exclusion>
					<groupId>org.slf4j</groupId>
					<artifactId>jcl-over-slf4j</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<!-- Postgres testing support -->
		<dependency>
			<groupId>org.postgresql</groupId>
			<artifactId>postgresql</artifactId>
			<version>${postgresql}</version>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>org.testcontainers</groupId>
			<artifactId>postgresql</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>io.vavr</groupId>
			<artifactId>vavr</artifactId>
			<version>${vavr}</version>
			<scope>test</scope>
		</dependency>

		<!-- Persistence providers -->

		<dependency>
			<groupId>${hibernate.groupId}.orm</groupId>
			<artifactId>hibernate-core</artifactId>
			<version>${hibernate}</version>
			<optional>true</optional>
			<exclusions>
				<exclusion>
					<groupId>net.bytebuddy</groupId>
					<artifactId>byte-buddy</artifactId>
				</exclusion>
			</exclusions>
		</dependency>

		<dependency>
			<groupId>${hibernate.groupId}.orm</groupId>
			<artifactId>hibernate-jpamodelgen</artifactId>
			<version>${hibernate}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>jakarta.xml.bind</groupId>
			<artifactId>jakarta.xml.bind-api</artifactId>
			<version>${jaxb}</version>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>jakarta.annotation</groupId>
			<artifactId>jakarta.annotation-api</artifactId>
			<version>${jakarta-annotation-api}</version>
		</dependency>

		<dependency>
			<groupId>org.eclipse.persistence</groupId>
			<artifactId>org.eclipse.persistence.jpa</artifactId>
			<version>${eclipselink}</version>
			<optional>true</optional>
		</dependency>

		<!-- QueryDsl -->
		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-apt</artifactId>
			<version>${querydsl}</version>
			<classifier>jakarta</classifier>
			<scope>provided</scope>
		</dependency>

		<dependency>
			<groupId>com.querydsl</groupId>
			<artifactId>querydsl-jpa</artifactId>
			<classifier>jakarta</classifier>
			<version>${querydsl}</version>
			<optional>true</optional>
		</dependency>

		<!-- jMolecules -->
		<dependency>
			<groupId>org.jmolecules.integrations</groupId>
			<artifactId>jmolecules-spring</artifactId>
			<version>${jmolecules-integration}</version>
			<scope>test</scope>
		</dependency>

		<!-- CDI -->
		<dependency>
			<groupId>jakarta.enterprise</groupId>
			<artifactId>jakarta.enterprise.cdi-api</artifactId>
			<version>${cdi}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

		<dependency>
			<groupId>org.apache.openwebbeans</groupId>
			<artifactId>openwebbeans-se</artifactId>
			<scope>test</scope>
		</dependency>

		<dependency>
			<groupId>com.github.jsqlparser</groupId>
			<artifactId>jsqlparser</artifactId>
			<version>${jsqlparser}</version>
			<scope>provided</scope>
			<optional>true</optional>
		</dependency>

	</dependencies>

	<build>
		<plugins>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-surefire-plugin</artifactId>
				<dependencies>
					<dependency>
						<groupId>org.springframework</groupId>
						<artifactId>spring-instrument</artifactId>
						<version>${spring}</version>
						<scope>runtime</scope>
					</dependency>
				</dependencies>
				<executions>
					<execution>
						<!-- override the default-test execution and exclude everything -->
						<id>default-test</id>
						<configuration>
							<excludes>
								<exclude>**/*</exclude>
							</excludes>
						</configuration>
					</execution>
					<execution>
						<id>unit-test</id>
						<goals>
							<goal>test</goal>
						</goals>
						<phase>test</phase>
						<configuration>
							<includes>
								<include>**/*UnitTests.java</include>
							</includes>
						</configuration>
					</execution>
					<execution>
						<id>integration-test</id>
						<goals>
							<goal>test</goal>
						</goals>
						<phase>test</phase>
						<configuration>
							<includes>
								<include>**/*IntegrationTests.java</include>
								<include>**/*Tests.java</include>
							</includes>
							<excludes>
								<exclude>**/*UnitTests.java</exclude>
								<exclude>**/OpenJpa*</exclude>
								<exclude>**/EclipseLink*</exclude>
								<exclude>**/MySql*</exclude>
								<exclude>**/Postgres*</exclude>
							</excludes>
							<argLine>
								-javaagent:${settings.localRepository}/org/springframework/spring-instrument/${spring}/spring-instrument-${spring}.jar
							</argLine>
						</configuration>
					</execution>
					<execution>
						<id>eclipselink-test</id>
						<goals>
							<goal>test</goal>
						</goals>
						<phase>test</phase>
						<configuration>
							<includes>
								<include>**/EclipseLink*Tests.java</include>
							</includes>
							<argLine>
								-javaagent:${settings.localRepository}/org/eclipse/persistence/org.eclipse.persistence.jpa/${eclipselink}/org.eclipse.persistence.jpa-${eclipselink}.jar
								-javaagent:${settings.localRepository}/org/springframework/spring-instrument/${spring}/spring-instrument-${spring}.jar
							</argLine>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.antlr</groupId>
				<artifactId>antlr4-maven-plugin</artifactId>
				<version>${antlr}</version>
				<executions>
					<execution>
						<goals>
							<goal>antlr4</goal>
						</goals>
						<phase>generate-sources</phase>
						<configuration>
							<visitor>true</visitor>
							<sourceDirectory>${project.basedir}/src/main/antlr4</sourceDirectory>
						</configuration>
					</execution>
				</executions>
			</plugin>

			<plugin>
				<groupId>org.apache.maven.plugins</groupId>
				<artifactId>maven-compiler-plugin</artifactId>
				<configuration>
					<annotationProcessorPaths>
						<path>
							<groupId>com.querydsl</groupId>
							<artifactId>querydsl-apt</artifactId>
							<version>${querydsl}</version>
							<classifier>jakarta</classifier>
						</path>
						<path>
							<groupId>org.hibernate.orm</groupId>
							<artifactId>hibernate-jpamodelgen</artifactId>
							<version>${hibernate}</version>
						</path>
						<path>
							<groupId>jakarta.persistence</groupId>
							<artifactId>jakarta.persistence-api</artifactId>
							<version>${jakarta-persistence-api}</version>
						</path>
					</annotationProcessorPaths>
				</configuration>
			</plugin>

			<plugin>
				<groupId>org.codehaus.mojo</groupId>
				<artifactId>aspectj-maven-plugin</artifactId>
				<version>1.14.0</version>
				<dependencies>
					<dependency>
						<groupId>org.aspectj</groupId>
						<artifactId>aspectjtools</artifactId>
						<version>${aspectj}</version>
					</dependency>
				</dependencies>
				<executions>
					<execution>
						<id>aspectj-compile</id>
						<goals>
							<goal>compile</goal>
						</goals>
						<phase>process-classes</phase>
					</execution>
					<execution>
						<id>aspectj-test-compile</id>
						<goals>
							<goal>test-compile</goal>
						</goals>
						<phase>process-test-classes</phase>
					</execution>
				</executions>
				<configuration>
					<!-- Annotation processing is done by Maven Compiler (hard to configure for AJ Maven) -->
					<proc>none</proc>
					<!-- Generate metadata for reflection on method parameters -->
					<parameters>true</parameters>
					<verbose>true</verbose>
					<showWeaveInfo>true</showWeaveInfo>
					<aspectLibraries>
						<aspectLibrary>
							<groupId>org.springframework</groupId>
							<artifactId>spring-aspects</artifactId>
						</aspectLibrary>
					</aspectLibraries>
					<complianceLevel>${source.level}</complianceLevel>
					<source>${source.level}</source>
					<target>${source.level}</target>
					<xmlConfigured>aop.xml</xmlConfigured>
				</configuration>
			</plugin>

		</plugins>
	</build>

</project>
