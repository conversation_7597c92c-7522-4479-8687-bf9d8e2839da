<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.springframework.restdocs</groupId>
  <artifactId>spring-restdocs-bom</artifactId>
  <version>3.0.1</version>
  <packaging>pom</packaging>
  <name>Spring REST Docs Bill of Materials</name>
  <description>Spring REST Docs Bill of Materials</description>
  <url>https://github.com/spring-projects/spring-restdocs</url>
  <organization>
    <name>Spring IO</name>
    <url>https://projects.spring.io/spring-restdocs</url>
  </organization>
  <licenses>
    <license>
      <name>The Apache Software License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>awilkinson</id>
      <name>Andy Wilkinson</name>
      <email><EMAIL></email>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/spring-projects/spring-restdocs</connection>
    <developerConnection>scm:git:git://github.com/spring-projects/spring-restdocs</developerConnection>
    <url>https://github.com/spring-projects/spring-restdocs</url>
  </scm>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/spring-projects/spring-restdocs/issues</url>
  </issueManagement>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-asciidoctor</artifactId>
        <version>3.0.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-core</artifactId>
        <version>3.0.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-mockmvc</artifactId>
        <version>3.0.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-restassured</artifactId>
        <version>3.0.1</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.restdocs</groupId>
        <artifactId>spring-restdocs-webtestclient</artifactId>
        <version>3.0.1</version>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
