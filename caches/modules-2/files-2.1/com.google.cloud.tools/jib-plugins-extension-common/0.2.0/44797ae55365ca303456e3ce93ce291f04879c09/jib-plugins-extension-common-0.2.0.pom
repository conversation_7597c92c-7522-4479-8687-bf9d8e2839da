<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud.tools</groupId>
  <artifactId>jib-plugins-extension-common</artifactId>
  <version>0.2.0</version>
  <name>Common Base for Jib Plugin Extension APIs</name>
  <description>Internal common base for Jib Plugin Extension APIs.</description>
  <url>https://github.com/GoogleContainerTools/jib</url>
  <inceptionYear>2018</inceptionYear>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>chanseokoh</id>
      <name>Chanseok Oh</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>loosebazooka</id>
      <name>Appu Goundan</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>TadCordle</id>
      <name>Tad Cordle</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>briandealwis</id>
      <name>Brian de Alwis</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>coollog</id>
      <name>Qingyang Chen</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:https://github.com/GoogleContainerTools/jib.git</connection>
    <developerConnection>scm:git://github.com/GoogleContainerTools/jib.git</developerConnection>
    <url>https://github.com/GoogleContainerTools/jib</url>
  </scm>
</project>
