<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.cloud.tools</groupId>
  <artifactId>jib-build-plan</artifactId>
  <version>0.4.0</version>
  <name>Jib Container Build Plan API</name>
  <description>Jib Container Build Plan API</description>
  <url>https://github.com/GoogleContainerTools/jib</url>
  <inceptionYear>2018</inceptionYear>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
      <distribution>repo</distribution>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>chanseokoh</id>
      <name>Chan<PERSON>ok Oh</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>loosebazooka</id>
      <name>Appu Goundan</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>TadCordle</id>
      <name>Tad Cordle</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>briandealwis</id>
      <name>Brian de Alwis</name>
      <email><EMAIL></email>
    </developer>
    <developer>
      <id>coollog</id>
      <name>Qingyang Chen</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:https://github.com/GoogleContainerTools/jib.git</connection>
    <developerConnection>scm:git://github.com/GoogleContainerTools/jib.git</developerConnection>
    <url>https://github.com/GoogleContainerTools/jib</url>
  </scm>
</project>
