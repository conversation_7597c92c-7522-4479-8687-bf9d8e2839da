<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <groupId>com.google.auth</groupId>
  <artifactId>google-auth-library-parent</artifactId>
  <version>1.10.0</version><!-- {x-version-update:google-auth-library-parent:current} -->
  <packaging>pom</packaging>
  <name>Google Auth Library for Java</name>
  <description>Client libraries providing authentication and
    authorization to enable calling Google APIs.</description>
  <url>https://github.com/googleapis/google-auth-library-java</url>

  <distributionManagement>
    <snapshotRepository>
      <id>sonatype-nexus-snapshots</id>
      <url>https://google.oss.sonatype.org/content/repositories/snapshots</url>
    </snapshotRepository>
    <site>
      <id>googleapis.dev</id>
      <name>Google self-hosted documentation site</name>
      <url>https://googleapis.dev/java/google-auth-library/${project.version}</url>
    </site>
  </distributionManagement>

  <licenses>
    <license>
      <name>BSD New license</name>
      <url>http://opensource.org/licenses/BSD-3-Clause</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <organization>
    <name>Google</name>
    <url>http://www.google.com/</url>
  </organization>


  <developers>
    <developer>
      <name>Jeff Ching</name>
      <email><EMAIL></email>
      <organization>Google</organization>
    </developer>
  </developers>

  <modules>
    <module>credentials</module>
    <module>oauth2_http</module>
    <module>appengine</module>
    <module>bom</module>
  </modules>

  <scm>
    <connection>scm:git:https://github.com/googleapis/google-auth-library-java.git</connection>
    <developerConnection>scm:git:https://github.com/googleapis/google-auth-library-java.git</developerConnection>
    <url>https://github.com/googleapis/google-auth-library-java</url>
  </scm>

  <properties>
    <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    <project.google.http.version>1.42.2</project.google.http.version>
    <project.junit.bom.version>5.9.0</project.junit.bom.version>
    <project.guava.version>31.0.1-android</project.guava.version>
    <project.appengine.version>2.0.5</project.appengine.version>
    <project.findbugs.version>3.0.2</project.findbugs.version>
    <deploy.autorelease>false</deploy.autorelease>
    <project.autovalue.version>1.8.2</project.autovalue.version>
    <auto-value-annotation.version>1.9</auto-value-annotation.version>
  </properties>

  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.junit</groupId>
        <artifactId>junit-bom</artifactId>
        <version>${project.junit.bom.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.auth</groupId>
        <artifactId>google-auth-library-credentials</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.auth</groupId>
        <artifactId>google-auth-library-oauth2-http</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.auth</groupId>
        <artifactId>google-auth-library-cloudshell</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.http-client</groupId>
        <artifactId>google-http-client-bom</artifactId>
        <version>${project.google.http.version}</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine-api-1.0-sdk</artifactId>
        <version>${project.appengine.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.guava</groupId>
        <artifactId>guava</artifactId>
        <version>${project.guava.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.code.findbugs</groupId>
        <artifactId>jsr305</artifactId>
        <version>${project.findbugs.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.auto.value</groupId>
        <artifactId>auto-value-annotations</artifactId>
        <version>${auto-value-annotation.version}</version>
      </dependency>
      <dependency>
        <groupId>com.google.appengine</groupId>
        <artifactId>appengine</artifactId>
        <version>${project.appengine.version}</version>
        <scope>test</scope>
      </dependency>
      <dependency>
        <groupId>com.google.auth</groupId>
        <artifactId>google-auth-library-oauth2-http</artifactId>
        <version>${project.version}</version>
        <type>test-jar</type>
      </dependency>
    </dependencies>
  </dependencyManagement>

  <build>
    <!-- This is the parent, so only define pluginManagement, not plugins. -->
    <pluginManagement>
      <plugins>
        <plugin>
          <groupId>org.sonatype.plugins</groupId>
          <artifactId>nexus-staging-maven-plugin</artifactId>
          <version>1.6.13</version>
          <extensions>true</extensions>
          <configuration>
            <serverId>ossrh</serverId>
            <nexusUrl>https://google.oss.sonatype.org/</nexusUrl>
            <autoReleaseAfterClose>${deploy.autorelease}</autoReleaseAfterClose>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-source-plugin</artifactId>
          <version>3.2.1</version>
          <executions>
            <execution>
              <id>attach-sources</id>
              <goals>
                <goal>jar-no-fork</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-javadoc-plugin</artifactId>
          <version>3.4.0</version>
          <configuration>
            <source>7</source>
            <failOnError>false</failOnError>
          </configuration>
          <executions>
            <execution>
              <id>attach-javadocs</id>
              <goals>
                <goal>jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-jar-plugin</artifactId>
          <version>3.2.2</version>
          <executions>
            <execution>
              <goals>
                <goal>test-jar</goal>
              </goals>
            </execution>
          </executions>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-surefire-plugin</artifactId>
          <version>3.0.0-M7</version>
          <configuration>
            <reportNameSuffix>sponge_log</reportNameSuffix>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.apache.maven.plugins</groupId>
          <artifactId>maven-dependency-plugin</artifactId>
          <version>3.1.2</version>
        </plugin>
        <plugin>
          <groupId>com.coveo</groupId>
          <artifactId>fmt-maven-plugin</artifactId>
          <version>2.13</version>
          <configuration>
            <verbose>true</verbose>
          </configuration>
        </plugin>
        <plugin>
          <groupId>org.codehaus.mojo</groupId>
          <artifactId>clirr-maven-plugin</artifactId>
          <version>2.8</version>
        </plugin>
      </plugins>
    </pluginManagement>
    <plugins>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.10.1</version>
        <configuration>
          <source>1.8</source>
          <target>1.8</target>
          <encoding>UTF-8</encoding>
          <compilerArgument>-Xlint:unchecked</compilerArgument>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.sonatype.plugins</groupId>
        <artifactId>nexus-staging-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <version>3.1.2</version>
        <executions>
          <execution>
            <id>checkstyle</id>
            <phase>validate</phase>
            <goals>
              <goal>check</goal>
            </goals>
            <configuration>
              <headerLocation>java.header</headerLocation>
              <configLocation>license-checks.xml</configLocation>
              <consoleOutput>true</consoleOutput>
              <failOnViolation>true</failOnViolation>
              <violationSeverity>error</violationSeverity>
              <failsOnError>true</failsOnError>
              <includeTestSourceDirectory>true</includeTestSourceDirectory>
              <!-- Explicitly set the source directory to avoid running checkstyle on generated sources. -->
              <sourceDirectories>
                <sourceDirectory>src/main</sourceDirectory>
              </sourceDirectories>
              <testSourceDirectories>
                <testSourceDirectory>src/test</testSourceDirectory>
              </testSourceDirectories>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-site-plugin</artifactId>
        <version>3.12.1</version>
        <configuration>
          <skipDeploy>true</skipDeploy>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>0.8.8</version>
        <executions>
          <execution>
            <goals>
              <goal>prepare-agent</goal>
            </goals>
          </execution>
          <execution>
            <id>report</id>
            <phase>test</phase>
            <goals>
              <goal>report</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>clirr-maven-plugin</artifactId>
        <configuration>
          <ignoredDifferencesFile>clirr-ignored-differences.xml</ignoredDifferencesFile>
          <logResults>true</logResults>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>check</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>

  <reporting>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>3.4.0</version>
        <reportSets>
          <reportSet>
            <id>html</id>
            <reports>
              <report>aggregate</report>
              <report>javadoc</report>
            </reports>
          </reportSet>
        </reportSets>
        <configuration>
          <failOnError>false</failOnError>
          <doclint>none</doclint>
          <source>7</source>
          <outputDirectory>${project.build.directory}/javadoc</outputDirectory>
          <doctitle>Google Auth Library for Java ${project.version}</doctitle>
          <overview>${basedir}/overview.html</overview>
          <groups>
            <group>
              <title>google-auth-library-credentials</title>
              <packages>com.google.auth*</packages>
            </group>
            <group>
              <title>google-auth-library-oauth2-http</title>
              <packages>com.google.auth.http*:com.google.auth.oauth2*</packages>
            </group>
            <group>
              <title>google-auth-library-appengine</title>
              <packages>com.google.auth.appengine*</packages>
            </group>
          </groups>
          <links>
            <link>http://download.oracle.com/javase/7/docs/api/</link>
            <link>http://cloud.google.com/appengine/docs/java/javadoc</link>
          </links>
          <windowtitle>google-auth-library ${project.version}</windowtitle>
        </configuration>
      </plugin>
    </plugins>
  </reporting>

  <profiles>
    <profile>
      <id>release-sign-artifacts</id>
      <activation>
        <property>
          <name>performRelease</name>
          <value>true</value>
        </property>
      </activation>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-source-plugin</artifactId>
            <version>3.2.1</version>
            <executions>
              <execution>
                <id>attach-sources</id>
                <goals>
                  <goal>jar-no-fork</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.4.0</version>
            <executions>
              <execution>
                <id>attach-javadocs</id>
                <goals>
                  <goal>jar</goal>
                </goals>
              </execution>
            </executions>
          </plugin>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-gpg-plugin</artifactId>
            <version>3.0.1</version>
            <executions>
              <execution>
                <id>sign-artifacts</id>
                <phase>verify</phase>
                <goals>
                  <goal>sign</goal>
                </goals>
                <configuration>
                  <gpgArguments>
                    <arg>--pinentry-mode</arg>
                    <arg>loopback</arg>
                  </gpgArguments>
                </configuration>
              </execution>
            </executions>
          </plugin>
        </plugins>
      </build>
    </profile>
    <profile>
      <id>autovalue-java8</id>
      <activation>
        <jdk>[1.8,)</jdk>
        <file>
          <exists>${basedir}/EnableAutoValue.txt</exists>
        </file>
      </activation>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-compiler-plugin</artifactId>
            <configuration>
              <annotationProcessorPaths>
                <path>
                  <groupId>com.google.auto.value</groupId>
                  <artifactId>auto-value</artifactId>
                  <version>${auto-value-annotation.version}</version>
                </path>
                <!--
                Manually pull in auto-service-annotations so that it is part of the
                processor path because auto-value has it set to provided scope.
                This dependency is needed due to the retention change in
                https://github.com/google/auto/commit/628df548685b4fc0f2a9af856f97cc2a68da246b
                where the RetentionPolicy changed from SOURCE to CLASS.
                Due to the RetentionPolicy change to CLASS we must have the
                annotations available on the processor path otherwise the following
                error will be thrown. (This is a particular problem with the
                annotation processor configuration in IntelliJ)
                Error:java: java.lang.NoClassDefFoundError: com/google/auto/service/AutoService
                  com.google.auto.service.AutoService
                -->
                <path>
                  <groupId>com.google.auto.service</groupId>
                  <artifactId>auto-service-annotations</artifactId>
                  <version>1.0.1</version>
                </path>
              </annotationProcessorPaths>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>

    <profile>
      <id>docFX</id>
      <activation>
        <property>
          <!-- Activate with -P docFX -->
          <name>docFX</name>
        </property>
      </activation>
      <properties>
        <!-- default config values -->
        <docletName>java-docfx-doclet-1.5.0</docletName>
        <outputpath>${project.build.directory}/docfx-yml</outputpath>
        <projectname>${project.artifactId}</projectname>
        <excludeclasses></excludeclasses>
        <excludePackages></excludePackages>
        <source>7</source>            
        <sourceFileExclude></sourceFileExclude>
      </properties>
      <build>
        <plugins>
          <plugin>
            <groupId>org.apache.maven.plugins</groupId>
            <artifactId>maven-javadoc-plugin</artifactId>
            <version>3.4.0</version>
            <configuration>
              <doclet>com.microsoft.doclet.DocFxDoclet</doclet>
              <useStandardDocletOptions>false</useStandardDocletOptions>
               <!-- custom config with -Dproperty=value -->
              <docletPath>${env.KOKORO_GFILE_DIR}/${docletName}.jar</docletPath>
              <additionalOptions>
                -outputpath ${outputpath} 
                -projectname ${projectname} 
                -excludeclasses ${excludeclasses}: 
                -excludepackages ${excludePackages}:
              </additionalOptions>
              <doclint>none</doclint>
              <show>protected</show>
              <nohelp>true</nohelp>
              <source>${source}</source>
              <sourceFileExcludes>
                <exclude>${sourceFileExclude}</exclude>
              </sourceFileExcludes>
              <failOnError>false</failOnError>
            </configuration>
          </plugin>  
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
