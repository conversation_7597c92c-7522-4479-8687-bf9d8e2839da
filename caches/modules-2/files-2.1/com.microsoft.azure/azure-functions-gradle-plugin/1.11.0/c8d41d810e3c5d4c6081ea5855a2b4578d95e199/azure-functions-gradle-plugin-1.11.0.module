{"formatVersion": "1.1", "component": {"group": "com.microsoft.azure", "module": "azure-functions-gradle-plugin", "version": "1.11.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "6.8", "buildId": "loc5zrm3nrcvrmhlhbne4wntd4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "azure-functions-gradle-plugin-1.11.0.jar", "url": "azure-functions-gradle-plugin-1.11.0.jar", "size": 52495, "sha512": "ff5d9b7a8d071f927327ee83625a46ca31cf9063fb3d321c8cabb44dfcd77867871b7d8a602485e5babd97943ad7ad9eafbf061be6a435e00168891798a6221b", "sha256": "ee5ef73df351d3d1157562daf89beb79216c7af319e78a2f8c716578b899f598", "sha1": "dae83d00f954c5b9f70f85e4d5b3807142e530eb", "md5": "4a53f82cbd2e06df8d053dadfb37a740"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "commons-io", "module": "commons-io", "version": {"requires": "2.10.0"}}, {"group": "org.apache.commons", "module": "commons-exec", "version": {"requires": "1.3"}}, {"group": "org.apache.commons", "module": "commons-lang3", "version": {"requires": "3.12.0"}}, {"group": "org.reflections", "module": "reflections", "version": {"requires": "0.9.12"}}, {"group": "org.atteo.classindex", "module": "classindex", "version": {"requires": "3.11"}}, {"group": "com.google.code.gson", "module": "gson", "version": {"requires": "2.8.7"}}, {"group": "com.google.guava", "module": "guava", "version": {"requires": "30.1.1-jre"}}, {"group": "org.apache.maven", "module": "maven-artifact", "version": {"requires": "3.8.1"}}, {"group": "org.fusesource.jansi", "module": "jansi", "version": {"requires": "1.18"}}, {"group": "com.microsoft.azure", "module": "azure-toolkit-common-lib", "version": {"requires": "0.24.0"}}, {"group": "com.microsoft.azure", "module": "azure-toolkit-appservice-lib", "version": {"requires": "0.24.0"}}, {"group": "com.microsoft.azure", "module": "azure-toolkit-applicationinsights-lib", "version": {"requires": "0.24.0"}}, {"group": "com.microsoft.azure", "module": "azure-gradle-plugins-common", "version": {"requires": "1.5.0"}}, {"group": "org.aspectj", "module": "aspectjrt", "version": {"requires": "1.9.6"}}], "files": [{"name": "azure-functions-gradle-plugin-1.11.0.jar", "url": "azure-functions-gradle-plugin-1.11.0.jar", "size": 52495, "sha512": "ff5d9b7a8d071f927327ee83625a46ca31cf9063fb3d321c8cabb44dfcd77867871b7d8a602485e5babd97943ad7ad9eafbf061be6a435e00168891798a6221b", "sha256": "ee5ef73df351d3d1157562daf89beb79216c7af319e78a2f8c716578b899f598", "sha1": "dae83d00f954c5b9f70f85e4d5b3807142e530eb", "md5": "4a53f82cbd2e06df8d053dadfb37a740"}]}]}