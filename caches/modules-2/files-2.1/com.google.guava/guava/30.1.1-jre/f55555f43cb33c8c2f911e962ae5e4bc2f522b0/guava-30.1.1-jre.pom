<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>com.google.guava</groupId>
    <artifactId>guava-parent</artifactId>
    <version>30.1.1-jre</version>
  </parent>
  <artifactId>guava</artifactId>
  <packaging>bundle</packaging>
  <name>Guava: Google Core Libraries for Java</name>
  <description>
    Guava is a suite of core and expanded libraries that include
    utility classes, Google's collections, I/O classes, and
    much more.
  </description>
  <dependencies>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>failureaccess</artifactId>
      <version>1.0.1</version>
    </dependency>
    <dependency>
      <groupId>com.google.guava</groupId>
      <artifactId>listenablefuture</artifactId>
      <version>9999.0-empty-to-avoid-conflict-with-guava</version>
    </dependency>
    <dependency>
      <groupId>com.google.code.findbugs</groupId>
      <artifactId>jsr305</artifactId>
    </dependency>
    <dependency>
      <groupId>org.checkerframework</groupId>
      <artifactId>checker-qual</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.errorprone</groupId>
      <artifactId>error_prone_annotations</artifactId>
    </dependency>
    <dependency>
      <groupId>com.google.j2objc</groupId>
      <artifactId>j2objc-annotations</artifactId>
    </dependency>
    <!-- TODO(cpovirk): does this comment belong on the <dependency> in <profiles>? -->
    <!-- TODO(cpovirk): want this only for dependency plugin but seems not to work there? Maven runs without failure, but the resulting Javadoc is missing the hoped-for inherited text -->
  </dependencies>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <configuration>
          <archive>
            <manifestEntries>
              <Automatic-Module-Name>com.google.common</Automatic-Module-Name>
            </manifestEntries>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <extensions>true</extensions>
        <groupId>org.apache.felix</groupId>
        <artifactId>maven-bundle-plugin</artifactId>
        <version>2.5.0</version>
        <executions>
          <execution>
            <id>bundle-manifest</id>
            <phase>process-classes</phase>
            <goals>
              <goal>manifest</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <instructions>
            <Export-Package>
              !com.google.common.base.internal,
              !com.google.common.util.concurrent.internal,
              com.google.common.*
            </Export-Package>
            <Import-Package>
              com.google.common.util.concurrent.internal,
              javax.annotation;resolution:=optional,
              javax.crypto.*;resolution:=optional,
              sun.misc.*;resolution:=optional
            </Import-Package>
            <Bundle-DocURL>https://github.com/google/guava/</Bundle-DocURL>
          </instructions>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-compiler-plugin</artifactId>
        <executions>
          <execution>
            <!--
                 The execution named default-compile happens first, regardless
                 of the order of the executions in the source file. So, because
                 Java8Usage is a dependency of the main sources, we need to call
                 its compilation "default-compile," even though it's the special
                 case.
            -->
            <id>default-compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <includes>
                <include>**/Java8Usage.java</include>
              </includes>
              <!-- -source 8 -target 8 is a no-op in the mainline but matters in the backport. -->
              <source>8</source>
              <target>8</target>
            </configuration>
          </execution>
          <execution>
            <id>main-compile</id>
            <phase>compile</phase>
            <goals>
              <goal>compile</goal>
            </goals>
            <configuration>
              <excludes>
                <exclude>**/Java8Usage.java</exclude>
              </excludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <artifactId>maven-source-plugin</artifactId>
      </plugin>
      <!-- TODO(cpovirk): include JDK sources when building testlib doc, too -->
      <plugin>
        <artifactId>maven-dependency-plugin</artifactId>
        <executions>
          <execution>
            <id>unpack-jdk-sources</id>
            <phase>generate-sources</phase>
            <goals><goal>unpack-dependencies</goal></goals>
            <configuration>
              <includeArtifactIds>srczip</includeArtifactIds>
              <outputDirectory>${project.build.directory}/jdk-sources</outputDirectory>
              <silent>false</silent>
              <!-- Exclude module-info files (since we're using -source 8 to avoid other modules problems) and FileDescriptor (which uses a language feature not available in Java 8). -->
              <excludes>**/module-info.java,**/java/io/FileDescriptor.java</excludes>
            </configuration>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>org.codehaus.mojo</groupId>
        <artifactId>animal-sniffer-maven-plugin</artifactId>
      </plugin>
      <plugin>
        <artifactId>maven-javadoc-plugin</artifactId>
        <configuration>
          <!-- TODO(cpovirk): Move this to the parent after making jdk-sources available there. -->
          <!-- TODO(cpovirk): can we use includeDependencySources and a local com.oracle.java:jdk-lib:noversion:sources instead of all this unzipping and manual sourcepath modification? -->
          <!-- (We need JDK *sources*, not just -link, so that {@inheritDoc} works.) -->
          <sourcepath>${project.build.sourceDirectory}:${project.build.directory}/jdk-sources</sourcepath>

          <!-- Passing `-subpackages com.google.common` breaks things, so we explicitly exclude everything else instead. -->
          <!-- excludePackageNames requires specification of packages separately from "all subpackages".
               https://issues.apache.org/jira/browse/MJAVADOC-584 -->
          <excludePackageNames>
            com.google.common.base.internal,com.google.common.base.internal.*,com.google.thirdparty.publicsuffix,com.google.thirdparty.publicsuffix.*,com.oracle.*,com.sun.*,java.*,javax.*,jdk,jdk.*,org.*,sun.*
          </excludePackageNames>
          <!-- Ignore some tags that are found in Java 11 sources but not recognized... under -source 8, I think it was? I can no longer reproduce the failure. -->
          <tags>
            <tag>
              <name>apiNote</name>
              <placement>X</placement>
            </tag>
            <tag>
              <name>implNote</name>
              <placement>X</placement>
            </tag>
            <tag>
              <name>implSpec</name>
              <placement>X</placement>
            </tag>
            <tag>
              <name>jls</name>
              <placement>X</placement>
            </tag>
            <tag>
              <name>revised</name>
              <placement>X</placement>
            </tag>
            <tag>
              <name>spec</name>
              <placement>X</placement>
            </tag>
          </tags>

          <!-- TODO(cpovirk): Move this to the parent after making the package-list files available there. -->
          <!-- We add the link ourselves, both so that we can choose Java 9 over the version that -source suggests and so that we can solve the JSR305 problem described below. -->
          <detectJavaApiLink>false</detectJavaApiLink>
          <offlineLinks>
            <!-- We need local copies of some of these for 2 reasons: a User-Agent problem (https://stackoverflow.com/a/47891403/28465) and an SSL problem (https://issues.apache.org/jira/browse/MJAVADOC-507). If we choose to work around the User-Agent problem, we can go back to <links>, sidestepping the SSL problem. -->
            <!-- Even after we stop using JSR305 annotations in our own code, we'll want this link so that NullPointerTester's docs can link to @CheckForNull and friends... at least once we start using this config for guava-testlib. -->
            <offlineLink>
              <url>https://static.javadoc.io/com.google.code.findbugs/jsr305/3.0.1/</url>
              <location>${project.basedir}/javadoc-link/jsr305</location>
            </offlineLink>
            <offlineLink>
              <url>https://static.javadoc.io/com.google.j2objc/j2objc-annotations/1.1/</url>
              <location>${project.basedir}/javadoc-link/j2objc-annotations</location>
            </offlineLink>
            <!-- The JDK doc must be listed after JSR305 (and as an <offlineLink>, not a <link>) so that JSR305 "claims" javax.annotation. -->
            <offlineLink>
              <url>https://docs.oracle.com/javase/9/docs/api/</url>
              <location>https://docs.oracle.com/javase/9/docs/api/</location>
            </offlineLink>
            <!-- The Checker Framework likewise would claim javax.annotations, despite providing only a subset of the JSR305 annotations, so it must likewise come after JSR305. -->
            <offlineLink>
              <url>https://checkerframework.org/api/</url>
              <location>${project.basedir}/javadoc-link/checker-framework</location>
            </offlineLink>
          </offlineLinks>
          <links>
            <link>https://errorprone.info/api/latest/</link>
          </links>
        </configuration>
        <executions>
          <execution>
            <id>attach-docs</id>
          </execution>
          <execution>
            <id>generate-javadoc-site-report</id>
            <phase>site</phase>
            <goals><goal>javadoc</goal></goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
  <profiles>
    <profile>
      <id>srczip-parent</id>
      <activation>
        <file>
          <exists>${java.home}/../src.zip</exists>
        </file>
      </activation>
      <dependencies>
        <dependency>
          <groupId>jdk</groupId>
          <artifactId>srczip</artifactId>
          <version>999</version>
          <scope>system</scope>
          <systemPath>${java.home}/../src.zip</systemPath>
          <optional>true</optional>
        </dependency>
      </dependencies>
    </profile>
    <profile>
      <id>srczip-lib</id>
      <activation>
        <file>
          <exists>${java.home}/lib/src.zip</exists>
        </file>
      </activation>
      <dependencies>
        <dependency>
          <groupId>jdk</groupId>
          <artifactId>srczip</artifactId>
          <version>999</version>
          <scope>system</scope>
          <systemPath>${java.home}/lib/src.zip</systemPath>
          <optional>true</optional>
        </dependency>
      </dependencies>
      <build>
        <plugins>
          <plugin>
            <artifactId>maven-javadoc-plugin</artifactId>
            <configuration>
              <!-- We need to point at the java.base subdirectory because Maven appears to assume that package foo.bar is located in foo/bar and not java.base/foo/bar when translating excludePackageNames into filenames to pass to javadoc. (Note that manually passing -exclude to javadoc appears to possibly not work at all for java.* types??) Also, referring only to java.base avoids a lot of other sources. -->
              <sourcepath>${project.build.sourceDirectory}:${project.build.directory}/jdk-sources/java.base</sourcepath>
            </configuration>
          </plugin>
        </plugins>
      </build>
    </profile>
  </profiles>
</project>
