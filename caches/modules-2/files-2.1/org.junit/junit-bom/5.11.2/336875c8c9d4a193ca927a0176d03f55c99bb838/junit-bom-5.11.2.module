{"formatVersion": "1.1", "component": {"group": "org.junit", "module": "junit-bom", "version": "5.11.2", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.9"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "platform", "org.gradle.usage": "java-api"}, "dependencyConstraints": [{"group": "org.junit.jupiter", "module": "junit-jupiter", "version": {"requires": "5.11.2"}}, {"group": "org.junit.jupiter", "module": "junit-jupiter-api", "version": {"requires": "5.11.2"}}, {"group": "org.junit.jupiter", "module": "junit-jupiter-engine", "version": {"requires": "5.11.2"}}, {"group": "org.junit.jupiter", "module": "junit-jupiter-migrationsupport", "version": {"requires": "5.11.2"}}, {"group": "org.junit.jupiter", "module": "junit-jupiter-params", "version": {"requires": "5.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-commons", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-console", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-engine", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-jfr", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-launcher", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-reporting", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-runner", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-suite", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-suite-api", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-suite-commons", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-suite-engine", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-testkit", "version": {"requires": "1.11.2"}}, {"group": "org.junit.vintage", "module": "junit-vintage-engine", "version": {"requires": "5.11.2"}}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "platform", "org.gradle.usage": "java-runtime"}, "dependencyConstraints": [{"group": "org.junit.jupiter", "module": "junit-jupiter", "version": {"requires": "5.11.2"}}, {"group": "org.junit.jupiter", "module": "junit-jupiter-api", "version": {"requires": "5.11.2"}}, {"group": "org.junit.jupiter", "module": "junit-jupiter-engine", "version": {"requires": "5.11.2"}}, {"group": "org.junit.jupiter", "module": "junit-jupiter-migrationsupport", "version": {"requires": "5.11.2"}}, {"group": "org.junit.jupiter", "module": "junit-jupiter-params", "version": {"requires": "5.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-commons", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-console", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-engine", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-jfr", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-launcher", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-reporting", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-runner", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-suite", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-suite-api", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-suite-commons", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-suite-engine", "version": {"requires": "1.11.2"}}, {"group": "org.junit.platform", "module": "junit-platform-testkit", "version": {"requires": "1.11.2"}}, {"group": "org.junit.vintage", "module": "junit-vintage-engine", "version": {"requires": "5.11.2"}}]}]}