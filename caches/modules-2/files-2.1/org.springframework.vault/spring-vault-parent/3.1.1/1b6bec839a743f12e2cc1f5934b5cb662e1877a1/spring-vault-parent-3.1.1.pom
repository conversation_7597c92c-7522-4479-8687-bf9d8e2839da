<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <groupId>org.springframework.vault</groupId>
  <artifactId>spring-vault-parent</artifactId>
  <version>3.1.1</version>
  <packaging>pom</packaging>
  <name>Spring Vault</name>
  <description>Parent project for Spring Vault</description>
  <url>https://projects.spring.io/spring-vault/</url>
  <inceptionYear>2016</inceptionYear>
  <organization>
    <name>Pivotal Software, Inc.</name>
    <url>https://www.spring.io</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
      <comments>Copyright 2016-2023 the original author or authors.

				Licensed under the Apache License, Version 2.0 (the "License");
				you may not use this file except in compliance with the License.
				You may obtain a copy of the License at

				https://www.apache.org/licenses/LICENSE-2.0

				Unless required by applicable law or agreed to in writing, software
				distributed under the License is distributed on an "AS IS" BASIS,
				WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
				implied.
				See the License for the specific language governing permissions and
				limitations under the License.</comments>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>sgibb</id>
      <name>Spencer Gibb</name>
    </developer>
    <developer>
      <id>mpaluch</id>
      <name>Mark Paluch</name>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/spring-projects/spring-vault.git</connection>
    <developerConnection>scm:git:ssh://**************/spring-projects/spring-vault.git</developerConnection>
    <url>https://github.com/spring-projects/spring-vault</url>
  </scm>
  <issueManagement>
    <system>GitHub</system>
    <url>https://github.com/spring-projects/spring-vault/issues</url>
  </issueManagement>
  <profiles>
    <profile>
      <id>springNext</id>
      <repositories>
        <repository>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
          <id>spring-snapshot</id>
          <url>https://repo.spring.io/snapshot</url>
        </repository>
      </repositories>
    </profile>
  </profiles>
</project>
