<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>org.rnorth.duct-tape</groupId>
    <artifactId>duct-tape</artifactId>
    <version>1.0.8</version>



    <name>Duct Tape</name>
    <description>
        General purpose resilience utilities for Java 8 (circuit breakers, timeouts, rate limiters, and handlers for unreliable or inconsistent results)
    </description>
    <url>https://github.com/rnorth/${project.artifactId}</url>
    <licenses>
        <license>
            <name>MIT</name>
            <url>http://opensource.org/licenses/MIT</url>
        </license>
    </licenses>
    <developers>
        <developer>
            <id>rnorth</id>
            <name><PERSON></name>
            <email><EMAIL></email>
        </developer>
    </developers>

    <dependencies>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.7</version>
            <scope>provided</scope>
        </dependency>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.12</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-all</artifactId>
            <version>1.9.5</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-simple</artifactId>
            <version>1.7.7</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.rnorth.visible-assertions</groupId>
            <artifactId>visible-assertions</artifactId>
            <version>1.0.5</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.jetbrains</groupId>
            <artifactId>annotations</artifactId>
            <version>17.0.0</version>
        </dependency>
    </dependencies>

    <build>
        <plugins>
            <plugin>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.1</version>
                <configuration>
                    <source>1.8</source>
                    <target>1.8</target>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-scm-plugin</artifactId>
                <version>1.9.4</version>
                <configuration>
                    <tag>${project.version}</tag>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-release-plugin</artifactId>
                <version>2.5.1</version>
                <configuration>
                    <useReleaseProfile>false</useReleaseProfile>
                    <autoVersionSubmodules>true</autoVersionSubmodules>
                </configuration>
            </plugin>
            <plugin>
                <artifactId>maven-source-plugin</artifactId>
                <version>2.4</version>
                <executions>
                    <execution>
                        <id>attach-sources</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <artifactId>maven-javadoc-plugin</artifactId>
                <version>2.10.3</version>
                <executions>
                    <execution>
                        <id>attach-javadocs</id>
                        <goals>
                            <goal>jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <aggregate>true</aggregate>
                    <show>public</show>
                    <nohelp>true</nohelp>
                    <header>${project.name}, ${project.version}</header>
                    <footer>${project.name}, ${project.version}</footer>
                    <doctitle>${project.name}, ${project.version}</doctitle>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-scm-publish-plugin</artifactId>
                <version>1.0-beta-2</version>
                <executions>
                    <execution>
                        <id>publish-javadocs</id>
                        <phase>site-deploy</phase>
                        <goals>
                            <goal>publish-scm</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <checkoutDirectory>${project.build.directory}/scmpublish</checkoutDirectory>
                    <checkinComment>Publishing javadoc for ${project.artifactId}:${project.version}</checkinComment>
                    <content>${project.reporting.outputDirectory}/apidocs</content>
                    <skipDeletedFiles>true</skipDeletedFiles>
                    <pubScmUrl>scm:git:**************:rnorth/${project.artifactId}.git</pubScmUrl>
                    <scmBranch>gh-pages</scmBranch> <!-- branch with static site -->
                </configuration>
            </plugin>
        </plugins>
    </build>

    <scm>
        <connection>scm:git:https://github.com/rnorth/${project.artifactId}.git</connection>
        <developerConnection>scm:git:**************:rnorth/${project.artifactId}.git</developerConnection>
        <url>https://github.com/rnorth/${project.artifactId}</url>
        <tag>HEAD</tag>
    </scm>

    <distributionManagement>
        <repository>
            <id>bintray</id>
            <url>https://api.bintray.com/maven/richnorth/maven/${project.artifactId}</url>
        </repository>
    </distributionManagement>
</project>
