{"formatVersion": "1.1", "component": {"group": "com.fasterxml.jackson.core", "module": "jackson-annotations", "version": "2.15.4", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"maven": {"version": "3.8.5"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.15.4"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-annotations-2.15.4.jar", "url": "jackson-annotations-2.15.4.jar", "size": 75569, "sha512": "6c485088024d028975c36f12740de5e70dcf2ac0f1943aad9ad7179e25436c9a433cd1ed2a8f8893e95ed7c18d74743a0a2281c8b2677c5c5b8d189ac96ae89", "sha256": "f204ebbd552614a22b8531ffe350d47f8fd42c45bb60517c07974dc27a5a1dd3", "sha1": "5223ea5a9bf52cdc9c5e537a0e52f2432eaf208b", "md5": "46ca5a02c1ec283236770c7e7714969a"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.15.4"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-annotations-2.15.4.jar", "url": "jackson-annotations-2.15.4.jar", "size": 75569, "sha512": "6c485088024d028975c36f12740de5e70dcf2ac0f1943aad9ad7179e25436c9a433cd1ed2a8f8893e95ed7c18d74743a0a2281c8b2677c5c5b8d189ac96ae89", "sha256": "f204ebbd552614a22b8531ffe350d47f8fd42c45bb60517c07974dc27a5a1dd3", "sha1": "5223ea5a9bf52cdc9c5e537a0e52f2432eaf208b", "md5": "46ca5a02c1ec283236770c7e7714969a"}]}]}