{"formatVersion": "1.1", "component": {"group": "com.fasterxml.jackson.core", "module": "jackson-annotations", "version": "2.13.3", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"maven": {"version": "3.8.4"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.13.3"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-annotations-2.13.3.jar", "url": "jackson-annotations-2.13.3.jar", "size": 75714, "sha512": "7f89b142068879b5fd96e4cb947313b3b39c1dbead43480307360a212fdb5347046b14fbc9b94c480034a4826fdd2a821686ebd121d774d55795326eaa1c95fd", "sha256": "5326a6fbcde7cf8817f36c254101cd45f6acea4258518cd3c80ee5b89f4e4b9b", "sha1": "7198b3aac15285a49e218e08441c5f70af00fc51", "md5": "3fb8ee542a62a113fa7474fe88bb97e8"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.13.3"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-annotations-2.13.3.jar", "url": "jackson-annotations-2.13.3.jar", "size": 75714, "sha512": "7f89b142068879b5fd96e4cb947313b3b39c1dbead43480307360a212fdb5347046b14fbc9b94c480034a4826fdd2a821686ebd121d774d55795326eaa1c95fd", "sha256": "5326a6fbcde7cf8817f36c254101cd45f6acea4258518cd3c80ee5b89f4e4b9b", "sha1": "7198b3aac15285a49e218e08441c5f70af00fc51", "md5": "3fb8ee542a62a113fa7474fe88bb97e8"}]}]}