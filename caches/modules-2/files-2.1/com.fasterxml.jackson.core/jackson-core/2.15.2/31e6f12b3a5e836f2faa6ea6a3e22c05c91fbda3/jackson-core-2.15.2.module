{"formatVersion": "1.1", "component": {"group": "com.fasterxml.jackson.core", "module": "jackson-core", "version": "2.15.2", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"maven": {"version": "3.9.2"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.15.2"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-core-2.15.2.jar", "url": "jackson-core-2.15.2.jar", "size": 549207, "sha512": "a8a3ddf5c8a732fc3810f9c113d88fd59bf613d15dbf9d3e24dd196b2b8c2195f4088375e3d03906f2629e62983fef3267b5478abd5ab1df733ec58cd00efae6", "sha256": "303c99e82b1faa91a0bae5d8fbeb56f7e2adf9b526a900dd723bf140d62bd4b4", "sha1": "a6fe1836469a69b3ff66037c324d75fc66ef137c", "md5": "e51fdee85b48e6637ad9e85ee76b58df"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "com.fasterxml.jackson", "module": "jackson-bom", "version": {"requires": "2.15.2"}, "attributes": {"org.gradle.category": "platform"}, "endorseStrictVersions": true}], "files": [{"name": "jackson-core-2.15.2.jar", "url": "jackson-core-2.15.2.jar", "size": 549207, "sha512": "a8a3ddf5c8a732fc3810f9c113d88fd59bf613d15dbf9d3e24dd196b2b8c2195f4088375e3d03906f2629e62983fef3267b5478abd5ab1df733ec58cd00efae6", "sha256": "303c99e82b1faa91a0bae5d8fbeb56f7e2adf9b526a900dd723bf140d62bd4b4", "sha1": "a6fe1836469a69b3ff66037c324d75fc66ef137c", "md5": "e51fdee85b48e6637ad9e85ee76b58df"}]}]}