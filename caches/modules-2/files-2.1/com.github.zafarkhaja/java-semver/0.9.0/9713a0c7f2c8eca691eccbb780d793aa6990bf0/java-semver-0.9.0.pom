<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
  xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>org.sonatype.oss</groupId>
    <artifactId>oss-parent</artifactId>
    <version>9</version>
  </parent>

  <groupId>com.github.zafarkhaja</groupId>
  <artifactId>java-semver</artifactId>
  <version>0.9.0</version>
  <packaging>jar</packaging>

  <name>Java SemVer</name>
  <description>Java implementation of the SemVer Specification</description>
  <url>https://github.com/zafarkhaja/jsemver</url>

  <licenses>
    <license>
      <name>The MIT License</name>
      <url>http://www.opensource.org/licenses/mit-license.php</url>
      <distribution>repo</distribution>
    </license>
  </licenses>

  <developers>
    <developer>
      <id>zafarkhaja</id>
      <name>Zafar Khaja</name>
      <email><EMAIL></email>
      <timezone>+3</timezone>
    </developer>
  </developers>

  <scm>
    <url>https://github.com/zafarkhaja/jsemver</url>
    <connection>scm:git:git://github.com/zafarkhaja/jsemver.git</connection>
    <developerConnection>scm:git:ssh://**************/zafarkhaja/jsemver.git</developerConnection>
  </scm>

  <dependencies>
    <dependency>
      <groupId>junit</groupId>
      <artifactId>junit</artifactId>
      <version>4.12</version>
      <scope>test</scope>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-compiler-plugin</artifactId>
        <version>3.2</version>
        <configuration>
          <source>1.6</source>
          <target>1.6</target>
          <encoding>UTF-8</encoding>
          <compilerArgument>-Xlint:all</compilerArgument>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-javadoc-plugin</artifactId>
        <version>2.10.2</version>
        <configuration>
          <additionalparam>-Xdoclint:none</additionalparam>
        </configuration>
      </plugin>
    </plugins>
  </build>
</project>
