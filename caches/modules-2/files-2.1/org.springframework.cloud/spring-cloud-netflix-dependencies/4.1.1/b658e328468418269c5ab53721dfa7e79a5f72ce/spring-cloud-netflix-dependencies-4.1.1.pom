<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-dependencies-parent</artifactId>
    <version>4.1.1</version>
    <relativePath></relativePath>
  </parent>
  <groupId>org.springframework.cloud</groupId>
  <artifactId>spring-cloud-netflix-dependencies</artifactId>
  <version>4.1.1</version>
  <packaging>pom</packaging>
  <name>spring-cloud-netflix-dependencies</name>
  <description>Spring Cloud Netflix Dependencies</description>
  <url>https://projects.spring.io/spring-cloud/spring-cloud-netflix-dependencies/</url>
  <organization>
    <name>Pivotal Software, Inc.</name>
    <url>https://www.spring.io</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
      <comments>Copyright 2014-2021 the original author or authors.

				Licensed under the Apache License, Version 2.0 (the "License");
				you may not use this file except in compliance with the License.
				You may obtain a copy of the License at

				https://www.apache.org/licenses/LICENSE-2.0

				Unless required by applicable law or agreed to in writing, software
				distributed under the License is distributed on an "AS IS" BASIS,
				WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
				implied.

				See the License for the specific language governing permissions and
				limitations under the License.</comments>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>dsyer</id>
      <name>Dave Syer</name>
      <email>dsyer at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>Project lead</role>
      </roles>
    </developer>
    <developer>
      <id>sgibb</id>
      <name>Spencer Gibb</name>
      <email>sgibb at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>Project lead</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/spring-cloud/spring-cloud-build.git/spring-cloud-netflix-dependencies</connection>
    <developerConnection>scm:git:ssh://**************/spring-cloud/spring-cloud-build.git/spring-cloud-netflix-dependencies</developerConnection>
    <url>https://github.com/spring-cloud/spring-cloud-build/spring-cloud-netflix-dependencies</url>
  </scm>
  <distributionManagement>
    <repository>
      <id>repo.spring.io</id>
      <name>Spring Release Repository</name>
      <url>https://repo.spring.io/libs-release-local</url>
    </repository>
    <snapshotRepository>
      <id>repo.spring.io</id>
      <name>Spring Snapshot Repository</name>
      <url>https://repo.spring.io/libs-snapshot-local</url>
    </snapshotRepository>
    <site>
      <id>spring-docs</id>
      <url>https:/docs.spring.io/spring-cloud-dependencies-parent/docs/4.1.1/reference/html/spring-cloud-netflix-dependencies/</url>
    </site>
    <downloadUrl>https://github.com/spring-cloud</downloadUrl>
  </distributionManagement>
  <properties>
    <eureka.version>2.0.2</eureka.version>
  </properties>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-eureka-client</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-eureka-client</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-starter-netflix-eureka-server</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>org.springframework.cloud</groupId>
        <artifactId>spring-cloud-netflix-eureka-server</artifactId>
        <version>${project.version}</version>
      </dependency>
      <dependency>
        <groupId>com.netflix.eureka</groupId>
        <artifactId>eureka-client</artifactId>
        <version>${eureka.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.netflix.archaius</groupId>
            <artifactId>archaius-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.github.vlsi.compactmap</groupId>
            <artifactId>compactmap</artifactId>
          </exclusion>
          <exclusion>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
          <exclusion>
            <groupId>org.glassfish.jersey.core</groupId>
            <artifactId>jersey-client</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.eureka</groupId>
        <artifactId>eureka-core</artifactId>
        <version>${eureka.version}</version>
        <exclusions>
          <exclusion>
            <groupId>com.netflix.archaius</groupId>
            <artifactId>archaius-core</artifactId>
          </exclusion>
          <exclusion>
            <groupId>jakarta.servlet</groupId>
            <artifactId>jakarta.servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>javax.servlet</groupId>
            <artifactId>servlet-api</artifactId>
          </exclusion>
          <exclusion>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
          </exclusion>
          <exclusion>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>blitz4j</groupId>
            <artifactId>com.netflix.blitz4j</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>annotations</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.google.code.findbugs</groupId>
            <artifactId>jsr305</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.fasterxml.jackson.dataformat</groupId>
            <artifactId>jackson-dataformat-xml</artifactId>
          </exclusion>
          <exclusion>
            <groupId>com.amazonaws</groupId>
            <artifactId>*</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
      <dependency>
        <groupId>com.netflix.eureka</groupId>
        <artifactId>eureka-core-jersey3</artifactId>
        <version>${eureka.version}</version>
      </dependency>
      <dependency>
        <groupId>com.netflix.eureka</groupId>
        <artifactId>eureka-client-jersey3</artifactId>
        <version>${eureka.version}</version>
        <exclusions>
          <exclusion>
            <groupId>aopalliance</groupId>
            <artifactId>aopalliance</artifactId>
          </exclusion>
        </exclusions>
      </dependency>
    </dependencies>
  </dependencyManagement>
  <profiles>
    <profile>
      <id>spring</id>
      <repositories>
        <repository>
          <releases>
            <enabled>false</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
          <id>spring-snapshots</id>
          <name>Spring Snapshots</name>
          <url>https://repo.spring.io/libs-snapshot-local</url>
        </repository>
        <repository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>spring-milestones</id>
          <name>Spring Milestones</name>
          <url>https://repo.spring.io/libs-milestone-local</url>
        </repository>
        <repository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>spring-releases</id>
          <name>Spring Releases</name>
          <url>https://repo.spring.io/release</url>
        </repository>
      </repositories>
      <pluginRepositories>
        <pluginRepository>
          <releases>
            <enabled>false</enabled>
          </releases>
          <snapshots>
            <enabled>true</enabled>
          </snapshots>
          <id>spring-snapshots</id>
          <name>Spring Snapshots</name>
          <url>https://repo.spring.io/libs-snapshot-local</url>
        </pluginRepository>
        <pluginRepository>
          <snapshots>
            <enabled>false</enabled>
          </snapshots>
          <id>spring-milestones</id>
          <name>Spring Milestones</name>
          <url>https://repo.spring.io/libs-milestone-local</url>
        </pluginRepository>
      </pluginRepositories>
    </profile>
  </profiles>
</project>
