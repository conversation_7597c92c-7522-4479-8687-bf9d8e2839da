<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <modelVersion>4.0.0</modelVersion>
  <parent>
    <groupId>org.springframework.cloud</groupId>
    <artifactId>spring-cloud-starter-circuitbreaker</artifactId>
    <version>3.1.1</version>
  </parent>
  <groupId>org.springframework.cloud</groupId>
  <artifactId>spring-cloud-starter-circuitbreaker-reactor-resilience4j</artifactId>
  <version>3.1.1</version>
  <description>Spring Cloud parent pom, managing plugins and dependencies for Spring
		Cloud projects</description>
  <url>https://spring.io/spring-cloud/spring-cloud-circuitbreaker/spring-cloud-starter-circuitbreaker/spring-cloud-starter-circuitbreaker-reactor-resilience4j</url>
  <organization>
    <name>Pivotal Software, Inc.</name>
    <url>https://www.spring.io</url>
  </organization>
  <licenses>
    <license>
      <name>Apache License, Version 2.0</name>
      <url>https://www.apache.org/licenses/LICENSE-2.0</url>
      <comments>Copyright 2014-2021 the original author or authors.

				Licensed under the Apache License, Version 2.0 (the "License");
				you may not use this file except in compliance with the License.
				You may obtain a copy of the License at

				https://www.apache.org/licenses/LICENSE-2.0

				Unless required by applicable law or agreed to in writing, software
				distributed under the License is distributed on an "AS IS" BASIS,
				WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or
				implied.

				See the License for the specific language governing permissions and
				limitations under the License.</comments>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>dsyer</id>
      <name>Dave Syer</name>
      <email>dsyer at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>lead</role>
      </roles>
    </developer>
    <developer>
      <id>sgibb</id>
      <name>Spencer Gibb</name>
      <email>sgibb at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>lead</role>
      </roles>
    </developer>
    <developer>
      <id>mgrzejszczak</id>
      <name>Marcin Grzejszczak</name>
      <email>mgrzejszczak at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
    <developer>
      <id>rbaxter</id>
      <name>Ryan Baxter</name>
      <email>rbaxter at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
    <developer>
      <id>omaciaszeksharma</id>
      <name>Olga Maciaszek-Sharma</name>
      <email>omaciaszeksharma at pivotal.io</email>
      <organization>Pivotal Software, Inc.</organization>
      <organizationUrl>https://www.spring.io</organizationUrl>
      <roles>
        <role>developer</role>
      </roles>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:git://github.com/spring-cloud/spring-cloud-build.git/spring-cloud-circuitbreaker/spring-cloud-starter-circuitbreaker/spring-cloud-starter-circuitbreaker-reactor-resilience4j</connection>
    <developerConnection>scm:git:ssh://**************/spring-cloud/spring-cloud-build.git/spring-cloud-circuitbreaker/spring-cloud-starter-circuitbreaker/spring-cloud-starter-circuitbreaker-reactor-resilience4j</developerConnection>
    <url>https://github.com/spring-cloud/spring-cloud-build/spring-cloud-circuitbreaker/spring-cloud-starter-circuitbreaker/spring-cloud-starter-circuitbreaker-reactor-resilience4j</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter</artifactId>
      <version>4.1.2</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-circuitbreaker-resilience4j</artifactId>
      <version>3.1.1</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.github.resilience4j</groupId>
      <artifactId>resilience4j-circuitbreaker</artifactId>
      <version>2.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.github.resilience4j</groupId>
      <artifactId>resilience4j-timelimiter</artifactId>
      <version>2.1.0</version>
      <scope>compile</scope>
    </dependency>
    <dependency>
      <groupId>io.github.resilience4j</groupId>
      <artifactId>resilience4j-reactor</artifactId>
      <version>2.1.0</version>
      <scope>compile</scope>
    </dependency>
  </dependencies>
</project>
