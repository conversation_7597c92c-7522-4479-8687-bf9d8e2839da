{"formatVersion": "1.1", "component": {"group": "org.springframework.kafka", "module": "spring-kafka", "version": "3.1.4", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.7"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "org.springframework", "module": "spring-context", "version": {"requires": "6.1.6"}}, {"group": "org.springframework", "module": "spring-messaging", "version": {"requires": "6.1.6"}}, {"group": "org.springframework", "module": "spring-tx", "version": {"requires": "6.1.6"}}, {"group": "org.springframework.retry", "module": "spring-retry", "version": {"requires": "2.0.5"}, "excludes": [{"group": "org.springframework", "module": "*"}]}, {"group": "org.apache.kafka", "module": "kafka-clients", "version": {"requires": "3.6.2"}}, {"group": "io.micrometer", "module": "micrometer-observation", "version": {"requires": "1.12.5"}}], "files": [{"name": "spring-kafka-3.1.4.jar", "url": "spring-kafka-3.1.4.jar", "size": 822821, "sha512": "326ca0843c03a803c4c7a4f8c208c8c6989562375e3a4df24f76b88222567fea6e1d96e839c3215110872de223ab803aea355b8b7adb2b4e56c1d2a7c0e9c408", "sha256": "6d27bdea13507e46946eab35c8ca34ae42ad441deee4087f1014f0bde9c087bd", "sha1": "34002e4f8c3481682e0302badc5a98f91c5ed7e4", "md5": "1b60ff39ca615939c72ad92dccc697fe"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "com.google.code.findbugs", "module": "jsr305", "version": {"requires": "3.0.2"}}, {"group": "org.springframework", "module": "spring-context", "version": {"requires": "6.1.6"}}, {"group": "org.springframework", "module": "spring-messaging", "version": {"requires": "6.1.6"}}, {"group": "org.springframework", "module": "spring-tx", "version": {"requires": "6.1.6"}}, {"group": "org.springframework.retry", "module": "spring-retry", "version": {"requires": "2.0.5"}, "excludes": [{"group": "org.springframework", "module": "*"}]}, {"group": "org.apache.kafka", "module": "kafka-clients", "version": {"requires": "3.6.2"}}, {"group": "io.micrometer", "module": "micrometer-observation", "version": {"requires": "1.12.5"}}], "files": [{"name": "spring-kafka-3.1.4.jar", "url": "spring-kafka-3.1.4.jar", "size": 822821, "sha512": "326ca0843c03a803c4c7a4f8c208c8c6989562375e3a4df24f76b88222567fea6e1d96e839c3215110872de223ab803aea355b8b7adb2b4e56c1d2a7c0e9c408", "sha256": "6d27bdea13507e46946eab35c8ca34ae42ad441deee4087f1014f0bde9c087bd", "sha1": "34002e4f8c3481682e0302badc5a98f91c5ed7e4", "md5": "1b60ff39ca615939c72ad92dccc697fe"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-kafka-3.1.4-javadoc.jar", "url": "spring-kafka-3.1.4-javadoc.jar", "size": 1574059, "sha512": "f3c1946c53386e6572506fd18827ab0996661b6d3f5e57ccf625112e64b78b6393564f150e18fa4e86c473728de9f5a9babaf95668da4d355edb34e07d034de7", "sha256": "e767e386698ec0d4ce6530b82fbad43e62cfd866594858cc52c971dcfc4fd917", "sha1": "764c806fbe21c797b70441ba474d40ea2639fc32", "md5": "0743c08be7d8c74c9ca856e9d9c68074"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-kafka-3.1.4-sources.jar", "url": "spring-kafka-3.1.4-sources.jar", "size": 554833, "sha512": "35fda79dbb58bc7143fcfa7197665ffb934beec904316d9a5b180bf09fa094838fde24aa012da90a392619a56e78418d40c23f4a4e11b1590ad7760d23b91eff", "sha256": "74dabef17366d15cfd6ac417cbe47fcf63b1c662494e3bd5cf5836c85eb752f1", "sha1": "05f19f9a7aa45496b293504970a7f8fc341af6de", "md5": "01dd16d4bcd927a72b1ac5447598a162"}]}, {"name": "optionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.assertj", "module": "assertj-core", "version": {"requires": "3.24.2"}}, {"group": "org.apache.kafka", "module": "kafka-streams", "version": {"requires": "3.6.2"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-core", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.datatype", "module": "jackson-datatype-jdk8", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.datatype", "module": "jackson-datatype-jsr310", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.datatype", "module": "jackson-datatype-joda", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.module", "module": "jackson-module-kotlin", "version": {"requires": "2.15.4"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "*"}]}, {"group": "org.springframework.data", "module": "spring-data-commons", "version": {"requires": "3.2.5"}, "excludes": [{"group": "org.springframework", "module": "*"}, {"group": "io.micrometer", "module": "*"}]}, {"group": "com.jayway.jsonpath", "module": "json-path", "version": {"requires": "2.8.0"}}, {"group": "io.projectreactor", "module": "reactor-core", "version": {"requires": "3.6.5"}}, {"group": "io.projectreactor.kafka", "module": "reactor-kafka", "version": {"requires": "1.3.23"}}, {"group": "io.micrometer", "module": "micrometer-core", "version": {"requires": "1.12.5"}}, {"group": "io.micrometer", "module": "micrometer-tracing", "version": {"requires": "1.2.5"}}], "files": [{"name": "spring-kafka-3.1.4.jar", "url": "spring-kafka-3.1.4.jar", "size": 822821, "sha512": "326ca0843c03a803c4c7a4f8c208c8c6989562375e3a4df24f76b88222567fea6e1d96e839c3215110872de223ab803aea355b8b7adb2b4e56c1d2a7c0e9c408", "sha256": "6d27bdea13507e46946eab35c8ca34ae42ad441deee4087f1014f0bde9c087bd", "sha1": "34002e4f8c3481682e0302badc5a98f91c5ed7e4", "md5": "1b60ff39ca615939c72ad92dccc697fe"}], "capabilities": [{"group": "org.springframework.kafka", "name": "spring-kafka-optional", "version": "3.1.4"}]}, {"name": "optionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.assertj", "module": "assertj-core", "version": {"requires": "3.24.2"}}, {"group": "org.apache.kafka", "module": "kafka-streams", "version": {"requires": "3.6.2"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-core", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.datatype", "module": "jackson-datatype-jdk8", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.datatype", "module": "jackson-datatype-jsr310", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.datatype", "module": "jackson-datatype-joda", "version": {"requires": "2.15.4"}}, {"group": "com.fasterxml.jackson.module", "module": "jackson-module-kotlin", "version": {"requires": "2.15.4"}, "excludes": [{"group": "org.jetbrains.kotlin", "module": "*"}]}, {"group": "org.springframework.data", "module": "spring-data-commons", "version": {"requires": "3.2.5"}, "excludes": [{"group": "org.springframework", "module": "*"}, {"group": "io.micrometer", "module": "*"}]}, {"group": "com.jayway.jsonpath", "module": "json-path", "version": {"requires": "2.8.0"}}, {"group": "io.projectreactor", "module": "reactor-core", "version": {"requires": "3.6.5"}}, {"group": "io.projectreactor.kafka", "module": "reactor-kafka", "version": {"requires": "1.3.23"}}, {"group": "io.micrometer", "module": "micrometer-core", "version": {"requires": "1.12.5"}}, {"group": "io.micrometer", "module": "micrometer-tracing", "version": {"requires": "1.2.5"}}], "files": [{"name": "spring-kafka-3.1.4.jar", "url": "spring-kafka-3.1.4.jar", "size": 822821, "sha512": "326ca0843c03a803c4c7a4f8c208c8c6989562375e3a4df24f76b88222567fea6e1d96e839c3215110872de223ab803aea355b8b7adb2b4e56c1d2a7c0e9c408", "sha256": "6d27bdea13507e46946eab35c8ca34ae42ad441deee4087f1014f0bde9c087bd", "sha1": "34002e4f8c3481682e0302badc5a98f91c5ed7e4", "md5": "1b60ff39ca615939c72ad92dccc697fe"}], "capabilities": [{"group": "org.springframework.kafka", "name": "spring-kafka-optional", "version": "3.1.4"}]}]}