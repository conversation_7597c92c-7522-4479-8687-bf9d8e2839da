{"formatVersion": "1.1", "component": {"group": "org.springframework.kafka", "module": "spring-kafka-test", "version": "3.1.4", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.7"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "org.springframework", "module": "spring-context", "version": {"requires": "6.1.6"}}, {"group": "org.springframework", "module": "spring-test", "version": {"requires": "6.1.6"}}, {"group": "org.springframework.retry", "module": "spring-retry", "version": {"requires": "2.0.5"}, "excludes": [{"group": "org.springframework", "module": "*"}]}, {"group": "org.apache.zookeeper", "module": "zookeeper", "version": {"requires": "3.8.4"}, "excludes": [{"group": "org.slf4j", "module": "slf4j-log4j12"}, {"group": "log4j", "module": "*"}]}, {"group": "org.apache.kafka", "module": "kafka-clients", "version": {"requires": "3.6.2"}, "thirdPartyCompatibility": {"artifactSelector": {"name": "kafka-clients", "type": "jar", "extension": "jar", "classifier": "test"}}}, {"group": "org.apache.kafka", "module": "kafka-metadata", "version": {"requires": "3.6.2"}}, {"group": "org.apache.kafka", "module": "kafka-server-common", "version": {"requires": "3.6.2"}}, {"group": "org.apache.kafka", "module": "kafka-server-common", "version": {"requires": "3.6.2"}, "thirdPartyCompatibility": {"artifactSelector": {"name": "kafka-server-common", "type": "jar", "extension": "jar", "classifier": "test"}}}, {"group": "org.apache.kafka", "module": "kafka-streams-test-utils", "version": {"requires": "3.6.2"}}, {"group": "org.apache.kafka", "module": "kafka_2.13", "version": {"requires": "3.6.2"}, "excludes": [{"group": "commons-logging", "module": "*"}]}, {"group": "org.apache.kafka", "module": "kafka_2.13", "version": {"requires": "3.6.2"}, "excludes": [{"group": "commons-logging", "module": "*"}], "thirdPartyCompatibility": {"artifactSelector": {"name": "kafka_2.13", "type": "jar", "extension": "jar", "classifier": "test"}}}, {"group": "org.junit.jupiter", "module": "junit-jupiter-api", "version": {"requires": "5.10.2"}}, {"group": "org.junit.platform", "module": "junit-platform-launcher", "version": {"requires": "1.10.2"}}], "files": [{"name": "spring-kafka-test-3.1.4.jar", "url": "spring-kafka-test-3.1.4.jar", "size": 80665, "sha512": "951d7cf2f88be9c7759cb29cc325b9fe6f5d7be4256bc7dc67597e0b2e300a5ab59ccc90b869157448fde407263e88f7453e7e4c9067384004b050f6a7f048d0", "sha256": "7263ddf87e21f626bdb6c1a8e08bc07f5ca0ff1607923b677ba1b3c873a22f0e", "sha1": "cc071ea4a1c343a0edeab2e84666147d0e573dee", "md5": "775b78f64da7f8b0d577c8fe7a1bfbf5"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.environment": "standard-jvm", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime", "org.jetbrains.kotlin.platform.type": "jvm"}, "dependencies": [{"group": "com.google.code.findbugs", "module": "jsr305", "version": {"requires": "3.0.2"}}, {"group": "org.springframework", "module": "spring-context", "version": {"requires": "6.1.6"}}, {"group": "org.springframework", "module": "spring-test", "version": {"requires": "6.1.6"}}, {"group": "org.springframework.retry", "module": "spring-retry", "version": {"requires": "2.0.5"}, "excludes": [{"group": "org.springframework", "module": "*"}]}, {"group": "org.apache.zookeeper", "module": "zookeeper", "version": {"requires": "3.8.4"}, "excludes": [{"group": "org.slf4j", "module": "slf4j-log4j12"}, {"group": "log4j", "module": "*"}]}, {"group": "org.apache.kafka", "module": "kafka-clients", "version": {"requires": "3.6.2"}, "thirdPartyCompatibility": {"artifactSelector": {"name": "kafka-clients", "type": "jar", "extension": "jar", "classifier": "test"}}}, {"group": "org.apache.kafka", "module": "kafka-metadata", "version": {"requires": "3.6.2"}}, {"group": "org.apache.kafka", "module": "kafka-server-common", "version": {"requires": "3.6.2"}}, {"group": "org.apache.kafka", "module": "kafka-server-common", "version": {"requires": "3.6.2"}, "thirdPartyCompatibility": {"artifactSelector": {"name": "kafka-server-common", "type": "jar", "extension": "jar", "classifier": "test"}}}, {"group": "org.apache.kafka", "module": "kafka-streams-test-utils", "version": {"requires": "3.6.2"}}, {"group": "org.apache.kafka", "module": "kafka_2.13", "version": {"requires": "3.6.2"}, "excludes": [{"group": "commons-logging", "module": "*"}]}, {"group": "org.apache.kafka", "module": "kafka_2.13", "version": {"requires": "3.6.2"}, "excludes": [{"group": "commons-logging", "module": "*"}], "thirdPartyCompatibility": {"artifactSelector": {"name": "kafka_2.13", "type": "jar", "extension": "jar", "classifier": "test"}}}, {"group": "org.junit.jupiter", "module": "junit-jupiter-api", "version": {"requires": "5.10.2"}}, {"group": "org.junit.platform", "module": "junit-platform-launcher", "version": {"requires": "1.10.2"}}], "files": [{"name": "spring-kafka-test-3.1.4.jar", "url": "spring-kafka-test-3.1.4.jar", "size": 80665, "sha512": "951d7cf2f88be9c7759cb29cc325b9fe6f5d7be4256bc7dc67597e0b2e300a5ab59ccc90b869157448fde407263e88f7453e7e4c9067384004b050f6a7f048d0", "sha256": "7263ddf87e21f626bdb6c1a8e08bc07f5ca0ff1607923b677ba1b3c873a22f0e", "sha1": "cc071ea4a1c343a0edeab2e84666147d0e573dee", "md5": "775b78f64da7f8b0d577c8fe7a1bfbf5"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-kafka-test-3.1.4-javadoc.jar", "url": "spring-kafka-test-3.1.4-javadoc.jar", "size": 212080, "sha512": "ae1e0b27b7e71a6991aebdb0b799d125caac302cb083a41029e356f75da0819fb5ff2b54b7cb183b0e824eb1151fc0fb19db5e5c51a18a68ab1fbd487ad256f1", "sha256": "435735edcdf78828bffde852140fd7db830d7b257d076a0dff091fe367d3ed46", "sha1": "4c78e511b35ec9a14c835da479d1035132df1c26", "md5": "77dbdd5aee3c3f925c338342ca955c76"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "spring-kafka-test-3.1.4-sources.jar", "url": "spring-kafka-test-3.1.4-sources.jar", "size": 44608, "sha512": "735365336e109d9b6d1624e379a145fc9e9f981f83bef4df528eaf2ee7c1ad3bc2088d299b7bf7ebab7e5acdcf28d3772a9ab312f29b54ee0ab8b4d5fc5790da", "sha256": "75436d555c99a4a711010d777e4a7931b362f51859ef7335189898ba109e3712", "sha1": "475457c784ccae560845e060c573f00ea8799ee9", "md5": "4fb9ceadad39321271be81229e61178a"}]}, {"name": "optionalApiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "org.assertj", "module": "assertj-core", "version": {"requires": "3.24.2"}}, {"group": "org.hamcrest", "module": "hamcrest-core", "version": {"requires": "2.2"}}, {"group": "org.mockito", "module": "mockito-core", "version": {"requires": "5.6.0"}}, {"group": "junit", "module": "junit", "version": {"requires": "4.13.2"}, "excludes": [{"group": "org.hamcrest", "module": "hamcrest-core"}]}, {"group": "org.apache.logging.log4j", "module": "log4j-core", "version": {"requires": "2.21.1"}}], "files": [{"name": "spring-kafka-test-3.1.4.jar", "url": "spring-kafka-test-3.1.4.jar", "size": 80665, "sha512": "951d7cf2f88be9c7759cb29cc325b9fe6f5d7be4256bc7dc67597e0b2e300a5ab59ccc90b869157448fde407263e88f7453e7e4c9067384004b050f6a7f048d0", "sha256": "7263ddf87e21f626bdb6c1a8e08bc07f5ca0ff1607923b677ba1b3c873a22f0e", "sha1": "cc071ea4a1c343a0edeab2e84666147d0e573dee", "md5": "775b78f64da7f8b0d577c8fe7a1bfbf5"}], "capabilities": [{"group": "org.springframework.kafka", "name": "spring-kafka-test-optional", "version": "3.1.4"}]}, {"name": "optionalRuntimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 17, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.assertj", "module": "assertj-core", "version": {"requires": "3.24.2"}}, {"group": "org.hamcrest", "module": "hamcrest-core", "version": {"requires": "2.2"}}, {"group": "org.mockito", "module": "mockito-core", "version": {"requires": "5.6.0"}}, {"group": "junit", "module": "junit", "version": {"requires": "4.13.2"}, "excludes": [{"group": "org.hamcrest", "module": "hamcrest-core"}]}, {"group": "org.apache.logging.log4j", "module": "log4j-core", "version": {"requires": "2.21.1"}}], "files": [{"name": "spring-kafka-test-3.1.4.jar", "url": "spring-kafka-test-3.1.4.jar", "size": 80665, "sha512": "951d7cf2f88be9c7759cb29cc325b9fe6f5d7be4256bc7dc67597e0b2e300a5ab59ccc90b869157448fde407263e88f7453e7e4c9067384004b050f6a7f048d0", "sha256": "7263ddf87e21f626bdb6c1a8e08bc07f5ca0ff1607923b677ba1b3c873a22f0e", "sha1": "cc071ea4a1c343a0edeab2e84666147d0e573dee", "md5": "775b78f64da7f8b0d577c8fe7a1bfbf5"}], "capabilities": [{"group": "org.springframework.kafka", "name": "spring-kafka-test-optional", "version": "3.1.4"}]}]}