{"formatVersion": "1.1", "component": {"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-api-incubator", "version": "2.7.0-alpha", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.opentelemetry.semconv", "module": "opentelemetry-semconv", "version": {"requires": "1.25.0-alpha"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}, {"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-api", "version": {"requires": "2.7.0"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}], "files": [{"name": "opentelemetry-instrumentation-api-incubator-2.7.0-alpha.jar", "url": "opentelemetry-instrumentation-api-incubator-2.7.0-alpha.jar", "size": 133492, "sha512": "31a94a211a74430e13bb220415869308833b8c69c016e0b2eff6838805ad7a916c2d1b78c37ff80149875a05b43619a80df3d1ac9e77812065b5acfd6bc094b2", "sha256": "bc40faad24666ec5b46fcf46589f81feb0305a49ef2eb97bef9b1a7d1a09605b", "sha1": "09981ebde3912e3a2dd83a3cda8350be86ba4594", "md5": "91f25d72525eb40fa352443e31eed935"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api-incubator", "version": {"requires": "1.41.0-alpha"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}, {"group": "io.opentelemetry.semconv", "module": "opentelemetry-semconv", "version": {"requires": "1.25.0-alpha"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}, {"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-api", "version": {"requires": "2.7.0"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}], "files": [{"name": "opentelemetry-instrumentation-api-incubator-2.7.0-alpha.jar", "url": "opentelemetry-instrumentation-api-incubator-2.7.0-alpha.jar", "size": 133492, "sha512": "31a94a211a74430e13bb220415869308833b8c69c016e0b2eff6838805ad7a916c2d1b78c37ff80149875a05b43619a80df3d1ac9e77812065b5acfd6bc094b2", "sha256": "bc40faad24666ec5b46fcf46589f81feb0305a49ef2eb97bef9b1a7d1a09605b", "sha1": "09981ebde3912e3a2dd83a3cda8350be86ba4594", "md5": "91f25d72525eb40fa352443e31eed935"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-instrumentation-api-incubator-2.7.0-alpha-javadoc.jar", "url": "opentelemetry-instrumentation-api-incubator-2.7.0-alpha-javadoc.jar", "size": 278109, "sha512": "282f3b97b7ae4ed0f05389ce0807e56d44f5dde27e14636e3beef25832c726fefb5f2edceb652b66dcf4d72b808ca21d76026fb26a22a239f53bb9ecbf606935", "sha256": "f2001856094891af437b8afd4a93bc1580a9118358a25c1b387daeab2abaf09a", "sha1": "eb8304777c972fdcd58e11e6d8ce8d6a6ef378e8", "md5": "ac6906b42d81b10dda38e0b28e99f700"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-instrumentation-api-incubator-2.7.0-alpha-sources.jar", "url": "opentelemetry-instrumentation-api-incubator-2.7.0-alpha-sources.jar", "size": 72503, "sha512": "19f3cd8cc4dd35b176a4abce2b4f355e07fabcee263115e545d7e4f82560b9541eb8d496d4175e3da42950b51694abfeb2946302931748686a4356d1698f3f49", "sha256": "80e8c91325f909f4001359a29a704ae4b0252c7d21a7d59fef7920a0faead4af", "sha1": "7a8c778b2f4287d70ae8700f1656561243a7b17f", "md5": "d658b06941d3e0ae8469fccee47c4b75"}]}]}