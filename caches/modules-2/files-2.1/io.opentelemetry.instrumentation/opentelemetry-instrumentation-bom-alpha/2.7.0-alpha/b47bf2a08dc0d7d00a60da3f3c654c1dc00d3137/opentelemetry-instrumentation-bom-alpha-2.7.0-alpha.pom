<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to Gradle or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.opentelemetry.instrumentation</groupId>
  <artifactId>opentelemetry-instrumentation-bom-alpha</artifactId>
  <version>2.7.0-alpha</version>
  <packaging>pom</packaging>
  <name>OpenTelemetry Instrumentation for Java</name>
  <description>OpenTelemetry Instrumentation Bill of Materials (Alpha)</description>
  <url>https://github.com/open-telemetry/opentelemetry-java-instrumentation</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>opentelemetry</id>
      <name>OpenTelemetry</name>
      <url>https://github.com/open-telemetry/opentelemetry-java-instrumentation/discussions</url>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:**************:open-telemetry/opentelemetry-java-instrumentation.git</connection>
    <developerConnection>scm:git:**************:open-telemetry/opentelemetry-java-instrumentation.git</developerConnection>
    <url>**************:open-telemetry/opentelemetry-java-instrumentation.git</url>
  </scm>
  <dependencyManagement>
    <dependencies>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-instrumentation-annotations-support</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-instrumentation-api-incubator</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent</groupId>
        <artifactId>opentelemetry-javaagent-bootstrap</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent</groupId>
        <artifactId>opentelemetry-javaagent-extension-api</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent</groupId>
        <artifactId>opentelemetry-javaagent-internal-logging-application</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent</groupId>
        <artifactId>opentelemetry-javaagent-internal-logging-simple</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent</groupId>
        <artifactId>opentelemetry-javaagent-tooling</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent</groupId>
        <artifactId>opentelemetry-muzzle</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-sdk-autoconfigure-support</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent</groupId>
        <artifactId>opentelemetry-testing-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent</groupId>
        <artifactId>opentelemetry-javaagent-tooling-java9</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent</groupId>
        <artifactId>opentelemetry-agent-for-testing</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-alibaba-druid-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-alibaba-druid-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-apache-dbcp-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-apache-dbcp-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-apache-dubbo-2.7</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-apache-dubbo-2.7</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-apache-httpasyncclient-4.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-apache-shenyu-2.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-c3p0-0.9</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-c3p0-0.9</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-camel-2.20</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-clickhouse-client-0.5</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-executors-bootstrap</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-executors</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-external-annotations</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-finagle-http-23.11</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-finatra-2.9</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-geode-1.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-google-http-client-1.19</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-grails-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-grizzly-2.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-grpc-1.6</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-grpc-1.6</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-guava-10.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-guava-10.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-gwt-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-hikaricp-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-hikaricp-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-http-url-connection</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-hystrix-1.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-influxdb-2.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-java-http-client</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-java-http-client</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-java-util-logging</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-javalin-5.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-client</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jdbc-bootstrap</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jdbc</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-jdbc</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jmx-metrics</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-jmx-metrics</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jodd-http-4.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jsp-2.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-kubernetes-client-7.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-methods</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-mybatis-3.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opentelemetry-extension-annotations-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opentelemetry-extension-kotlin-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opentelemetry-instrumentation-annotations-1.16</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opentelemetry-instrumentation-api</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-oracle-ucp-11.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-oracle-ucp-11.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-oshi</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-oshi</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-payara</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-quarkus-resteasy-reactive</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-quartz-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-quartz-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-r2dbc-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-r2dbc-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-rabbitmq-2.7</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-rediscala-1.8</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-resources</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-rmi-bootstrap</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-rmi</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-scala-fork-join-2.8</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spark-2.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spymemcached-2.12</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-struts-2.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-tapestry-5.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-tomcat</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-twilio-6.6</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-undertow-1.4-bootstrap</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-undertow-1.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-vaadin-14.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-vibur-dbcp-11.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-vibur-dbcp-11.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-wicket-8.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-akka-actor-2.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-akka-actor-fork-join-2.5</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-akka-http-10.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-apache-httpclient-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-apache-httpclient-4.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-apache-httpclient-4.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-apache-httpclient-5.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-apache-httpclient-5.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-armeria-1.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-armeria-1.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-armeria-grpc-1.14</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-async-http-client-1.9</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-async-http-client-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-aws-lambda-core-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-aws-lambda-core-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-aws-lambda-events-2.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-aws-lambda-events-2.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-aws-sdk-1.11</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-aws-sdk-1.11</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-aws-sdk-1.11-autoconfigure</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-aws-sdk-2.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-aws-sdk-2.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-aws-sdk-2.2-autoconfigure</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-azure-core-1.14</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-azure-core-1.19</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-azure-core-1.36</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-cassandra-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-cassandra-4.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-cassandra-4.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-cassandra-4.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-couchbase-2-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-couchbase-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-couchbase-2.6</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-couchbase-3.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-couchbase-3.1.6</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-couchbase-3.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-dropwizard-metrics-4.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-dropwizard-views-0.7</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-elasticsearch-api-client-7.16</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-elasticsearch-rest-5.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-elasticsearch-rest-6.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-elasticsearch-rest-7.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-elasticsearch-rest-7.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-elasticsearch-rest-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-elasticsearch-rest-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-elasticsearch-transport-5.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-elasticsearch-transport-5.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-elasticsearch-transport-6.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-elasticsearch-transport-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-graphql-java-12.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-graphql-java-12.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-graphql-java-20.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-graphql-java-20.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-graphql-java-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-hibernate-3.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-hibernate-4.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-hibernate-6.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-hibernate-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-hibernate-procedure-call-4.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-hibernate-reactive-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-internal-application-logger-bootstrap</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-internal-application-logger</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-internal-class-loader</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-internal-eclipse-osgi-3.6</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-internal-lambda</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-internal-lambda-java9</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-internal-reflection</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-internal-url-class-loader</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-common-bootstrap</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxws-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxws-2.0-axis2-1.6</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxws-2.0-cxf-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxws-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxws-jws-api-1.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxws-metro-2.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jboss-logmanager-appender-1.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jboss-logmanager-mdc-1.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jedis-1.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jedis-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jedis-4.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jedis-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jetty-11.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jetty-12.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jetty-8.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jetty-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jetty-httpclient-12.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-jetty-httpclient-12.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jetty-httpclient-9.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-jetty-httpclient-9.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jms-1.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jms-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jms-common-bootstrap</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jms-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jsf-jakarta-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jsf-javax-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jsf-mojarra-1.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jsf-mojarra-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jsf-myfaces-1.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jsf-myfaces-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-kafka-streams-0.11</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-kotlinx-coroutines-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-kotlinx-coroutines-flow-1.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-ktor-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-ktor-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-ktor-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-ktor-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-lettuce-4.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-lettuce-5.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-lettuce-5.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-lettuce-5.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-lettuce-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-liberty-20.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-liberty-dispatcher-20.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-log4j-appender-1.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-log4j-appender-2.17</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-log4j-appender-2.17</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-log4j-mdc-1.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-logback-appender-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-logback-appender-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-logback-mdc-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-logback-mdc-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-micrometer-1.5</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-micrometer-1.5</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-mongo-3.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-mongo-3.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-mongo-3.7</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-mongo-4.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-mongo-async-3.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-netty-3.8</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-netty-4-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-netty-4-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-netty-4.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-netty-4.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-netty-4.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-netty-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-okhttp-2.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-okhttp-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-okhttp-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opensearch-rest-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opensearch-rest-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opentelemetry-api-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opentelemetry-api-1.10</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opentelemetry-api-1.15</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opentelemetry-api-1.27</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opentelemetry-api-1.31</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opentelemetry-api-1.32</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opentelemetry-api-1.37</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opentelemetry-api-1.38</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-opentelemetry-api-1.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-pekko-actor-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-pekko-http-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-pulsar-2.8</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-ratpack-1.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-ratpack-1.7</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-reactor-3.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-reactor-3.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-reactor-3.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-reactor-kafka-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-redisson-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-redisson-3.17</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-redisson-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-restlet-1.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-restlet-1.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-restlet-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-restlet-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-runtime-telemetry-java17</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-runtime-telemetry-java17</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-runtime-telemetry-java8</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-runtime-telemetry-java8</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-rxjava-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-rxjava-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-rxjava-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-rxjava-3-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-rxjava-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-rxjava-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-rxjava-3.1.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-rxjava-3.1.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-servlet-2.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-servlet-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-servlet-5.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-servlet-common-bootstrap</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-servlet-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-servlet-javax-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-batch-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-boot-actuator-autoconfigure-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-spring-boot-resources</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-core-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-integration-4.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-spring-integration-4.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-kafka-2.7</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-spring-kafka-2.7</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-rabbit-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-rmi-4.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-scheduling-3.1-bootstrap</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-scheduling-3.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-security-config-6.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-spring-security-config-6.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-ws-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-zipkin-spring-boot-starter</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-tomcat-10.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-tomcat-7.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-tomcat-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-vertx-kafka-client-3.6</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-vertx-redis-client-4.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-vertx-rx-java-3.5</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-vertx-sql-client-4.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-vertx-web-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-xxl-job-1.9.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-xxl-job-2.1.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-xxl-job-2.3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-xxl-job-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-zio-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-2.0-annotations</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-2.0-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-2.0-cxf-3.2</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-2.0-jersey-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-2.0-resteasy-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-2.0-resteasy-3.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-2.0-resteasy-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-3.0-annotations</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-3.0-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-3.0-jersey-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-jaxrs-3.0-resteasy-6.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-kafka-clients-0.11-bootstrap</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-kafka-clients-0.11</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-kafka-clients-2.6</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-kafka-clients-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-log4j-context-data-2.17</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-log4j-context-data-2.17-autoconfigure</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-log4j-context-data-2.7</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-play-mvc-2.4</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-play-mvc-2.6</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-play-ws-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-play-ws-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-play-ws-2.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-play-ws-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-reactor-netty-0.9</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-reactor-netty-1.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-rocketmq-client-4.8</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-rocketmq-client-4.8</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-rocketmq-client-5.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-cloud-gateway-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-data-1.8</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-jms-2.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-jms-6.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-web-3.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-spring-web-3.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-web-6.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-webflux-5.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-spring-webflux-5.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-webmvc-3.1</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-spring-webmvc-5.3</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-webmvc-6.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-spring-webmvc-6.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-spring-webmvc-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-vertx-http-client-3.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-vertx-http-client-4.0</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.javaagent.instrumentation</groupId>
        <artifactId>opentelemetry-javaagent-vertx-http-client-common</artifactId>
        <version>2.7.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.semconv</groupId>
        <artifactId>opentelemetry-semconv</artifactId>
        <version>1.25.0-alpha</version>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-bom</artifactId>
        <version>1.41.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry</groupId>
        <artifactId>opentelemetry-bom-alpha</artifactId>
        <version>1.41.0-alpha</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
      <dependency>
        <groupId>io.opentelemetry.instrumentation</groupId>
        <artifactId>opentelemetry-instrumentation-bom</artifactId>
        <version>2.7.0</version>
        <type>pom</type>
        <scope>import</scope>
      </dependency>
    </dependencies>
  </dependencyManagement>
</project>
