{"formatVersion": "1.1", "component": {"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-api", "version": "2.7.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.41.0"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}], "files": [{"name": "opentelemetry-instrumentation-api-2.7.0.jar", "url": "opentelemetry-instrumentation-api-2.7.0.jar", "size": 221240, "sha512": "b6f06b647f719cf5c0149294b3d6f6228cc8aacea10eb33a556a0bfab41c54abba318866c2460ccd7fc7e4dc4700e35d84d9e88e0832e2f91a3be5877cfc6a03", "sha256": "23de2ab3f8fd0a5b6235e2b81a9ae770efce158bc7727392252dc818aea9b3ca", "sha1": "b597d97c2a0f7b4df4b2afc6eb9f421c1e48b359", "md5": "0a9c30ffba5cfaba862e83f3ce0d11f9"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.opentelemetry", "module": "opentelemetry-api-incubator", "version": {"requires": "1.41.0-alpha"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}, {"group": "io.opentelemetry.semconv", "module": "opentelemetry-semconv", "version": {"requires": "1.25.0-alpha"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}, {"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.41.0"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}], "files": [{"name": "opentelemetry-instrumentation-api-2.7.0.jar", "url": "opentelemetry-instrumentation-api-2.7.0.jar", "size": 221240, "sha512": "b6f06b647f719cf5c0149294b3d6f6228cc8aacea10eb33a556a0bfab41c54abba318866c2460ccd7fc7e4dc4700e35d84d9e88e0832e2f91a3be5877cfc6a03", "sha256": "23de2ab3f8fd0a5b6235e2b81a9ae770efce158bc7727392252dc818aea9b3ca", "sha1": "b597d97c2a0f7b4df4b2afc6eb9f421c1e48b359", "md5": "0a9c30ffba5cfaba862e83f3ce0d11f9"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-instrumentation-api-2.7.0-javadoc.jar", "url": "opentelemetry-instrumentation-api-2.7.0-javadoc.jar", "size": 411710, "sha512": "42f4093661fc30dfba9471011460a95af6e95daa651d1e25a089ab93cd84d107afb5134662696079257eea0333fbbb108922b47fa9d5fc51481ce6377319c5f7", "sha256": "559914c6712f5ba16c3dd75572e55c08aa6f98bdbed6341abe4804699b9dec5e", "sha1": "a405445fcc676cee449f8e2e0975ab7084550d7d", "md5": "ab0bf966fb57992e6c49f0e874c3549c"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-instrumentation-api-2.7.0-sources.jar", "url": "opentelemetry-instrumentation-api-2.7.0-sources.jar", "size": 128651, "sha512": "c567b1ee5dd62e4aab6946da032ad777151d160abf7f89e807d52d3a777c64e3ed7166aaf141cd314430c89cb6b20d788b070996d36d2d32ab687752e498dc32", "sha256": "7b12769d64d6439605303f81e034ff4335db2eab81b1ee06261b0b338e0d2ef4", "sha1": "95a2bd88fbbf5df7db6516a345659a3d379f5613", "md5": "2823b117a6ef9c6dd44bcba310cdda20"}]}]}