<?xml version="1.0" encoding="UTF-8"?>
<project xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 https://maven.apache.org/xsd/maven-4.0.0.xsd" xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <!-- This module was also published with a richer model, Gradle metadata,  -->
  <!-- which should be used instead. Do not delete the following line which  -->
  <!-- is to indicate to <PERSON>rad<PERSON> or any Gradle module metadata file consumer  -->
  <!-- that they should prefer consuming it instead. -->
  <!-- do_not_remove: published-with-gradle-metadata -->
  <modelVersion>4.0.0</modelVersion>
  <groupId>io.opentelemetry.instrumentation</groupId>
  <artifactId>opentelemetry-micrometer-1.5</artifactId>
  <version>2.7.0-alpha</version>
  <name>OpenTelemetry Instrumentation for Java</name>
  <description>Instrumentation of Java libraries using OpenTelemetry.</description>
  <url>https://github.com/open-telemetry/opentelemetry-java-instrumentation</url>
  <licenses>
    <license>
      <name>The Apache License, Version 2.0</name>
      <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
    </license>
  </licenses>
  <developers>
    <developer>
      <id>opentelemetry</id>
      <name>OpenTelemetry</name>
      <url>https://github.com/open-telemetry/opentelemetry-java-instrumentation/discussions</url>
    </developer>
  </developers>
  <scm>
    <connection>scm:git:**************:open-telemetry/opentelemetry-java-instrumentation.git</connection>
    <developerConnection>scm:git:**************:open-telemetry/opentelemetry-java-instrumentation.git</developerConnection>
    <url>**************:open-telemetry/opentelemetry-java-instrumentation.git</url>
  </scm>
  <dependencies>
    <dependency>
      <groupId>io.opentelemetry.instrumentation</groupId>
      <artifactId>opentelemetry-instrumentation-api</artifactId>
      <version>2.7.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.opentelemetry.instrumentation</groupId>
          <artifactId>opentelemetry-instrumentation-bom-alpha</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry.instrumentation</groupId>
      <artifactId>opentelemetry-instrumentation-api-incubator</artifactId>
      <version>2.7.0-alpha</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.opentelemetry.instrumentation</groupId>
          <artifactId>opentelemetry-instrumentation-bom-alpha</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>io.opentelemetry</groupId>
      <artifactId>opentelemetry-api</artifactId>
      <version>1.41.0</version>
      <scope>compile</scope>
      <exclusions>
        <exclusion>
          <groupId>io.opentelemetry.instrumentation</groupId>
          <artifactId>opentelemetry-instrumentation-bom-alpha</artifactId>
        </exclusion>
      </exclusions>
    </dependency>
  </dependencies>
</project>
