{"formatVersion": "1.1", "component": {"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-micrometer-1.5", "version": "2.7.0-alpha", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.10"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "dependencies": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-api", "version": {"requires": "2.7.0"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}, {"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-api-incubator", "version": {"requires": "2.7.0-alpha"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}, {"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.41.0"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}], "files": [{"name": "opentelemetry-micrometer-1.5-2.7.0-alpha.jar", "url": "opentelemetry-micrometer-1.5-2.7.0-alpha.jar", "size": 44959, "sha512": "b4d9566662924cbfa38ec14bbf38a29c60270dd51f9171af278c6b7a5221f6f03fb21573b92740c3613ccf18e602e3980224ab2077a4824d0a5523463e3d2425", "sha256": "ba923d81942973c2a51770fb8c50f41a81e07ddaeb38a592afba8cd16ce9b241", "sha1": "d43ff74d1dad866ce280042a6c2b3e726643a20d", "md5": "6a14757260625676858d09e8318b183a"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-api", "version": {"requires": "2.7.0"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}, {"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-api-incubator", "version": {"requires": "2.7.0-alpha"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}, {"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.41.0"}, "excludes": [{"group": "io.opentelemetry.instrumentation", "module": "opentelemetry-instrumentation-bom-alpha"}]}], "files": [{"name": "opentelemetry-micrometer-1.5-2.7.0-alpha.jar", "url": "opentelemetry-micrometer-1.5-2.7.0-alpha.jar", "size": 44959, "sha512": "b4d9566662924cbfa38ec14bbf38a29c60270dd51f9171af278c6b7a5221f6f03fb21573b92740c3613ccf18e602e3980224ab2077a4824d0a5523463e3d2425", "sha256": "ba923d81942973c2a51770fb8c50f41a81e07ddaeb38a592afba8cd16ce9b241", "sha1": "d43ff74d1dad866ce280042a6c2b3e726643a20d", "md5": "6a14757260625676858d09e8318b183a"}]}, {"name": "javadocElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "javadoc", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-micrometer-1.5-2.7.0-alpha-javadoc.jar", "url": "opentelemetry-micrometer-1.5-2.7.0-alpha-javadoc.jar", "size": 99318, "sha512": "3dbd067a31d5a71885f9ba69735375346f2a461ebc9e759bdaf961d091ac1869b830332a0dfed584dda0098aacbc5ca808cd5e92f6a08e49ce17c761434b0b9d", "sha256": "fbae2e61f86aac6f2c6e116ca5f91f9ee009cfc7df42434ea9cda36b57236793", "sha1": "7cd3ee0d2fc9575e97d6ca5c6f4c387655eaab8e", "md5": "40dd2bfab791c3727ff7ca14b7ae0992"}]}, {"name": "sourcesElements", "attributes": {"org.gradle.category": "documentation", "org.gradle.dependency.bundling": "external", "org.gradle.docstype": "sources", "org.gradle.usage": "java-runtime"}, "files": [{"name": "opentelemetry-micrometer-1.5-2.7.0-alpha-sources.jar", "url": "opentelemetry-micrometer-1.5-2.7.0-alpha-sources.jar", "size": 20675, "sha512": "e17599a1d0bc2da992da9382893b6071d5aad4e677370359c8d2904d2a1597d87b570e719d5161494717c93b244bc47cb04b99f18e8bb8129ae283d36f025c19", "sha256": "48a1c72c92d40e6e7fe5aee657475b9a6463dfcd2a7379e454c66c9d17596051", "sha1": "6e90f5384dfc2cfb7e5e72707f836d2b5c071ea7", "md5": "d70ae9963937de012bdff7c09654824d"}]}]}