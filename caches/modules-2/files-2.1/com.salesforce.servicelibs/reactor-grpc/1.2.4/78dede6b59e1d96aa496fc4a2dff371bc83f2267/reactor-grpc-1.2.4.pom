<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
  <parent>
    <artifactId>reactive-grpc</artifactId>
    <groupId>com.salesforce.servicelibs</groupId>
    <version>1.2.4</version>
    <relativePath>../../pom.xml</relativePath>
  </parent>
  <modelVersion>4.0.0</modelVersion>
  <artifactId>reactor-grpc</artifactId>
  <build>
    <plugins>
      <plugin>
        <artifactId>maven-checkstyle-plugin</artifactId>
        <configuration>
          <configLocation>../../checkstyle.xml</configLocation>
          <suppressionsLocation>../../checkstyle_ignore.xml</suppressionsLocation>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-shade-plugin</artifactId>
        <version>3.2.1</version>
        <executions>
          <execution>
            <phase>package</phase>
            <goals>
              <goal>shade</goal>
            </goals>
          </execution>
        </executions>
        <configuration>
          <relocations>
            <relocation>
              <pattern>android.annotation</pattern>
              <shadedPattern>com.salesforce.servicelibs.android.annotation</shadedPattern>
            </relocation>
            <relocation>
              <pattern>com.github.mustachejava</pattern>
              <shadedPattern>com.salesforce.servicelibs.com.github.mustachejava</shadedPattern>
            </relocation>
            <relocation>
              <pattern>com.google</pattern>
              <shadedPattern>com.salesforce.servicelibs.com.google</shadedPattern>
            </relocation>
            <relocation>
              <pattern>google.protobuf</pattern>
              <shadedPattern>com.salesforce.servicelibs.google.protobuf</shadedPattern>
            </relocation>
            <relocation>
              <pattern>io.grpc</pattern>
              <shadedPattern>com.salesforce.servicelibs.io.grpc</shadedPattern>
            </relocation>
            <relocation>
              <pattern>io.perfmark</pattern>
              <shadedPattern>com.salesforce.servicelibs.io.perfmark</shadedPattern>
            </relocation>
            <relocation>
              <pattern>javax.annotation</pattern>
              <shadedPattern>com.salesforce.servicelibs.javax.annotation</shadedPattern>
            </relocation>
            <relocation>
              <pattern>org.checkerframework</pattern>
              <shadedPattern>com.salesforce.servicelibs.org.checkerframework</shadedPattern>
            </relocation>
            <relocation>
              <pattern>org.codehaus</pattern>
              <shadedPattern>com.salesforce.servicelibs.org.codehaus</shadedPattern>
            </relocation>
          </relocations>
        </configuration>
      </plugin>
      <plugin>
        <artifactId>maven-jar-plugin</artifactId>
        <version>2.4</version>
        <configuration>
          <archive>
            <manifest>
              <addClasspath>true</addClasspath>
              <mainClass>com.salesforce.reactorgrpc.ReactorGrpcGenerator</mainClass>
            </manifest>
          </archive>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.salesforce.servicelibs</groupId>
        <artifactId>canteen-maven-plugin</artifactId>
        <version>${canteen.plugin.version}</version>
        <executions>
          <execution>
            <goals>
              <goal>bootstrap</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>
