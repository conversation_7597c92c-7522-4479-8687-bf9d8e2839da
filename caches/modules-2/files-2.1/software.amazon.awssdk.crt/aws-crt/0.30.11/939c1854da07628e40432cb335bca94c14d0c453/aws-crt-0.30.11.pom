<project xmlns="http://maven.apache.org/POM/4.0.0"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>software.amazon.awssdk.crt</groupId>
    <artifactId>aws-crt</artifactId>
    <packaging>jar</packaging>
    <version>0.30.11</version>
    <name>${project.groupId}:${project.artifactId}</name>
    <description>Java bindings for the AWS SDK Common Runtime</description>
    <url>https://github.com/awslabs/aws-crt-java</url>

    <licenses>
        <license>
            <name>The Apache Software License, Version 2.0</name>
            <url>http://www.apache.org/licenses/LICENSE-2.0.txt</url>
        </license>
    </licenses>

    <developers>
        <developer>
            <name>AWS SDK Common Runtime Team</name>
            <email><EMAIL></email>
            <organization>Amazon Web Services</organization>
            <organizationUrl>https://aws.amazon.com</organizationUrl>
        </developer>
    </developers>

    <scm>
        <connection>scm:git:git://github.com/awslabs/aws-crt-java.git</connection>
        <developerConnection>scm:git:ssh://github.com:awslabs/aws-crt-java.git</developerConnection>
        <url>https://github.com/awslabs/aws-crt-java/tree/main</url>
    </scm>

    <properties>
        <cmake.command>cmake</cmake.command>
        <cmake.buildtype>RelWithDebInfo</cmake.buildtype>
        <cmake.buildconfig>${cmake.buildtype}</cmake.buildconfig>
        <cmake.binaries>target/cmake-build</cmake.binaries>
        <maven.compiler.source>1.8</maven.compiler.source>
        <maven.compiler.target>1.8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <cmake.warningsareerrors>OFF</cmake.warningsareerrors>
        <cmake.s2nNoPqAsm>OFF</cmake.s2nNoPqAsm>
        <cmake.osx_arch>-DOSX_ARCH_DUMMY=1</cmake.osx_arch>
        <cmake.min_osx_version>-DOSX_DEPLOYMENT_TARGET_DUMMY=1</cmake.min_osx_version>
        <cmake.crt_fips>OFF</cmake.crt_fips>
        <maven-surefire-plugin.version>2.21.0</maven-surefire-plugin.version>
    </properties>

    <profiles>
        <profile>
            <id>compile-for-java-8</id>
            <activation>
                <jdk>[9,)</jdk>
            </activation>
            <properties>
                <maven.compiler.release>8</maven.compiler.release>
            </properties>
        </profile>
        <profile>
            <id>continuous-integration</id>
            <properties>
                <cmake.warningsareerrors>ON</cmake.warningsareerrors>
            </properties>
        </profile>
        <profile>
            <!-- Windows profile: find cmake generators and use ALL_BUILD target
                NOTE: CMake generator will be cached to target/cmake-build/cmake.properties, delete or edit this
                file to change VS version
                -->
            <id>windows-vs</id>
            <activation>
                <os>
                    <family>windows</family>
                </os>
            </activation>
            <properties>
                <cmake.buildtype></cmake.buildtype>
                <cmake.buildconfig>RelWithDebInfo</cmake.buildconfig>
                <cmake.cflags/>
            </properties>
        </profile>
        <!-- Unix/Linux/OSX profiles: use makefiles and the "all" compile target -->
        <profile>
            <id>unix-make</id>
            <activation>
                <os>
                    <family>unix</family>
                </os>
            </activation>
            <properties>
                <cmake.cflags/>
            </properties>
        </profile>
        <profile>
            <id>mac-x64</id>
            <activation>
                <os>
                    <family>mac</family>
                    <arch>x86_64</arch>
                </os>
            </activation>
            <properties>
                <cmake.cflags/>
                <cmake.min_osx_version>-DCMAKE_OSX_DEPLOYMENT_TARGET=10.9</cmake.min_osx_version>
                <cmake.osx_arch>-DCMAKE_OSX_ARCHITECTURES=x86_64</cmake.osx_arch>
            </properties>
        </profile>
        <profile>
            <id>mac-arm64</id>
                <activation>
                    <os>
                        <family>mac</family>
                        <arch>aarch64</arch>
                    </os>
                </activation>
            <properties>
                <cmake.min_osx_version>-DCMAKE_OSX_DEPLOYMENT_TARGET=10.9</cmake.min_osx_version>
                <cmake.osx_arch>-DCMAKE_OSX_ARCHITECTURES=arm64</cmake.osx_arch>
            </properties>
        </profile>
        <!-- 32-bit Unix -->
        <profile>
            <id>unix-x86</id>
            <activation>
                <os>
                    <family>unix</family>
                    <arch>i386</arch>
                </os>
            </activation>
            <properties>
                <cmake.cflags>-m32</cmake.cflags>
            </properties>
        </profile>
        <profile>
            <id>debug-native</id>
            <activation>
                <property>
                    <name>debug.native</name>
                </property>
            </activation>
            <properties>
                <cmake.buildtype>Debug</cmake.buildtype>
            </properties>
        </profile>
        <!-- native compilation -->
        <profile>
            <id>build-shared-lib</id>
            <activation>
                <property>
                    <name>!shared-lib.skip</name>
                </property>
            </activation>
            <build>
                <plugins>
                    <!-- cmake configure and build -->
                    <plugin>
                        <groupId>org.codehaus.mojo</groupId>
                        <artifactId>exec-maven-plugin</artifactId>
                        <version>1.3.2</version>
                        <executions>
                            <!-- cmake configure -->
                            <execution>
                                <id>cmake-configure</id>
                                <phase>generate-sources</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>${cmake.command}</executable>
                                    <workingDirectory>${project.basedir}</workingDirectory>
                                    <arguments>
                                        <argument>-B${cmake.binaries}</argument>
                                        <argument>-H${project.basedir}</argument>
                                        <argument>-DCMAKE_BUILD_TYPE=${cmake.buildtype}</argument>
                                        <argument>-DCMAKE_EXPORT_COMPILE_COMMANDS=ON</argument>
                                        <argument>-DBUILD_DEPS=ON</argument>
                                        <argument>-DAWS_WARNINGS_ARE_ERRORS=${cmake.warningsareerrors}</argument>
                                        <argument>-DCMAKE_PREFIX_PATH=${cmake.binaries}/install</argument>
                                        <argument>-DCMAKE_INSTALL_PREFIX=${cmake.binaries}/install</argument>
                                        <argument>-DCMAKE_C_FLAGS=${cmake.cflags}</argument>
                                        <argument>${cmake.min_osx_version}</argument>
                                        <argument>${cmake.osx_arch}</argument>
                                        <argument>-DS2N_NO_PQ_ASM=${cmake.s2nNoPqAsm}</argument>
                                        <argument>-DBUILD_TESTING=OFF</argument>
                                        <argument>-DCRT_FIPS=${cmake.crt_fips}</argument>
                                        <argument>--no-warn-unused-cli</argument>
                                        <argument>${cmake.generator}</argument>
                                        <argument>${cmake.toolset}</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                            <!-- cmake build -->
                            <execution>
                                <id>make</id>
                                <phase>generate-resources</phase>
                                <goals>
                                    <goal>exec</goal>
                                </goals>
                                <configuration>
                                    <executable>${cmake.command}</executable>
                                    <workingDirectory>${cmake.binaries}</workingDirectory>
                                    <arguments>
                                        <argument>--build</argument>
                                        <argument>.</argument>
                                        <argument>--config</argument>
                                        <argument>${cmake.buildconfig}</argument>
                                        <argument>--target</argument>
                                        <argument>aws-crt-jni</argument>
                                    </arguments>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
        <profile>
            <id>graalvm-native</id>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.graalvm.buildtools</groupId>
                        <artifactId>native-maven-plugin</artifactId>
                        <version>0.10.2</version> <!-- or newer version -->
                        <extensions>true</extensions>
                        <executions>
                            <execution>
                                <id>test-native</id>
                                <goals>
                                    <goal>test</goal>
                                </goals>
                                <phase>test</phase>
                            </execution>
                        </executions>
                        <configuration>
                            <agent>
                                <enabled>true</enabled>
                            </agent>
                            <imageName>aws-crt-java</imageName>
                            <buildArgs>
                                <buildArg>--no-fallback</buildArg>
                                <buildArg>--initialize-at-build-time=org.junit.Ignore,java.lang.annotation.Annotation,org.junit.runners.model.FrameworkField</buildArg>
                                <buildArg>--verbose</buildArg>
                            </buildArgs>
                        </configuration>
                    </plugin>
                </plugins>
            </build>
            <properties>
                <maven-surefire-plugin.version>3.2.3</maven-surefire-plugin.version>
            </properties>
        </profile>
        <profile>
            <id>release</id>
            <distributionManagement>
                <snapshotRepository>
                    <id>ossrh</id>
                    <url>https://aws.oss.sonatype.org/content/repositories/snapshots</url>
                </snapshotRepository>
            </distributionManagement>
            <build>
                <plugins>
                    <!-- staging/release to Sonatype -->
                    <plugin>
                        <groupId>org.sonatype.plugins</groupId>
                        <artifactId>nexus-staging-maven-plugin</artifactId>
                        <version>1.6.8</version>
                        <extensions>true</extensions>
                        <configuration>
                            <serverId>ossrh</serverId>
                            <nexusUrl>https://aws.oss.sonatype.org/</nexusUrl>
                            <autoReleaseAfterClose>false</autoReleaseAfterClose>
                            <skipStagingRepositoryClose>true</skipStagingRepositoryClose>
                        </configuration>
                    </plugin>
                    <!-- source jar -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-source-plugin</artifactId>
                        <version>2.2.1</version>
                        <executions>
                            <execution>
                                <id>attach-sources</id>
                                <goals>
                                    <goal>jar-no-fork</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- javadoc jar -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-javadoc-plugin</artifactId>
                        <version>2.9.1</version>
                        <executions>
                            <execution>
                                <id>attach-javadocs</id>
                                <goals>
                                    <goal>jar</goal>
                                </goals>
                            </execution>
                        </executions>
                    </plugin>
                    <!-- GPG signing -->
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-gpg-plugin</artifactId>
                        <version>1.5</version>
                        <executions>
                            <execution>
                                <id>sign-artifacts</id>
                                <phase>verify</phase>
                                <goals>
                                    <goal>sign</goal>
                                </goals>
                                <configuration>
                                    <keyname>${gpg.keyname}</keyname>
                                </configuration>
                            </execution>
                        </executions>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>

    <dependencies>
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>4.13.2</version>
            <scope>test</scope>
        </dependency>
        <!-- https://graalvm.github.io/native-build-tools/latest/maven-plugin.html#testing-support -->
        <dependency>
            <groupId>org.junit.vintage</groupId>
            <artifactId>junit-vintage-engine</artifactId>
            <version>5.10.2</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>org.mockito</groupId>
            <artifactId>mockito-core</artifactId>
            <version>4.7.0</version>
            <scope>test</scope>
        </dependency>
        <dependency>
            <groupId>commons-cli</groupId>
            <artifactId>commons-cli</artifactId>
            <version>1.4</version>
            <scope>test</scope>
        </dependency>
    </dependencies>
    <build>
        <resources>
            <!-- shared libs built via cmake -->
            <resource>
                <directory>${cmake.binaries}/lib</directory>
                <excludes>
                    <exclude>**/*.ilk</exclude>                    <!-- no one needs incremental linker files in their jar -->
                    <exclude>**/*.pdb</exclude>                    <!-- do not package pdbs, comment this out to debug C code -->
                    <exclude>**/*.dbg</exclude>                    <!-- UNIX debug info, same as PDBs -->
                    <exclude>**/*.a</exclude>                      <!-- ignore any libraries from building -->
                </excludes>
            </resource>
            <!-- GraalVM Native Image Configuration Files -->
            <resource>
                <directory>src/main/resources</directory>
            </resource>
        </resources>
        <testResources>
            <testResource>
                <directory>src/test/resources</directory>
            </testResource>
        </testResources>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>3.8.1</version>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>test-jar</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <classifier>${crt.classifier}</classifier>
                    <archive>
                        <manifest>
                            <addDefaultSpecificationEntries>true</addDefaultSpecificationEntries>
                            <addDefaultImplementationEntries>true</addDefaultImplementationEntries>
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <!-- make build dir for cmake -->
            <plugin>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>1.8</version>
                <executions>
                    <execution>
                        <id>create-build-dir</id>
                        <phase>initialize</phase>
                        <configuration>
                            <target>
                                <mkdir dir="${cmake.binaries}"/>
                            </target>
                        </configuration>
                        <goals>
                            <goal>run</goal>
                        </goals>
                    </execution>
                    <execution>
                        <id>cmake-generator</id>
                        <phase>initialize</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <exportAntProperties>true</exportAntProperties>
                            <target>
                                <property environment="env"/>
                                <condition property="cmake.generator" value="-G${env.AWS_CMAKE_GENERATOR}" else="-DGENERATOR_DUMMY=1">
                                    <isset property="env.AWS_CMAKE_GENERATOR" />
                                </condition>
                                <echo message="Generator = ${cmake.generator}"/>
                            </target>
                        </configuration>
                    </execution>
                    <execution>
                        <id>cmake-toolset</id>
                        <phase>initialize</phase>
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <exportAntProperties>true</exportAntProperties>
                            <target>
                                <property environment="env"/>
                                <condition property="cmake.toolset" value="-T${env.AWS_CMAKE_TOOLSET}" else="-DTOOLSET_DUMMY=1">
                                    <isset property="env.AWS_CMAKE_TOOLSET" />
                                </condition>
                                <echo message="Toolset = ${cmake.toolset}"/>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <!-- unit test configuration -->
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>${maven-surefire-plugin.version}</version>
                <configuration>
                    <systemPropertyVariables>
                        <crt.classifier>${crt.classifier}</crt.classifier>
                        <certificate>${crt.test.certificate}</certificate>
                        <privatekey>${crt.test.privatekey}</privatekey>
                        <ecc_certificate>${crt.test.ecc_certificate}</ecc_certificate>
                        <ecc_privatekey>${crt.test.ecc_privatekey}</ecc_privatekey>
                        <endpoint>${crt.test.endpoint}</endpoint>
                        <rootca>${crt.test.rootca}</rootca>
                        <privatekey_p8>${crt.test.privatekey_p8}</privatekey_p8>
                    </systemPropertyVariables>
                    <properties>
                        <property>
                            <name>listener</name>
                            <value>software.amazon.awssdk.crt.test.FailFastListener</value>
                        </property>
                    </properties>
                    <shutdown>kill</shutdown>
                    <argLine>-Daws.crt.memory.tracing=2 -Xcheck:jni</argLine>
                    <runOrder>alphabetical</runOrder>
                    <useFile>false</useFile>
                    <reuseForks>false</reuseForks>
                    <forkCount>0</forkCount>
                </configuration>
            </plugin>
        </plugins>
    </build>
</project>
