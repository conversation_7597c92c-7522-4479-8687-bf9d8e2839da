{"formatVersion": "1.1", "component": {"group": "org.apache.kafka", "module": "kafka-storage-api", "version": "3.6.2", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.2.1"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "kafka-storage-api-3.6.2.jar", "url": "kafka-storage-api-3.6.2.jar", "size": 26135, "sha512": "5c75ae5316743a5116ff4eac5fd401d421a19b42dc8ba0f82ce8c1bf7680e2ae1e730d0ce5cb306e712d2d586dd4e1a15beabcaf5bd8d1a9c68a166567ed3177", "sha256": "5679e9a1f8e776adef9f3011994bb1d9d3ed9925e31eb0ab075456ea643d9ef7", "sha1": "8276139e796c95f65cd88fa90849ae9d83fca728", "md5": "f05d598608b50dd31c5598d1f4b11726"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.apache.kafka", "module": "kafka-clients", "version": {"requires": "3.6.2"}}, {"group": "org.apache.kafka", "module": "kafka-server-common", "version": {"requires": "3.6.2"}}, {"group": "com.yammer.metrics", "module": "metrics-core", "version": {"requires": "2.2.0"}}, {"group": "org.slf4j", "module": "slf4j-api", "version": {"requires": "1.7.36"}}], "files": [{"name": "kafka-storage-api-3.6.2.jar", "url": "kafka-storage-api-3.6.2.jar", "size": 26135, "sha512": "5c75ae5316743a5116ff4eac5fd401d421a19b42dc8ba0f82ce8c1bf7680e2ae1e730d0ce5cb306e712d2d586dd4e1a15beabcaf5bd8d1a9c68a166567ed3177", "sha256": "5679e9a1f8e776adef9f3011994bb1d9d3ed9925e31eb0ab075456ea643d9ef7", "sha1": "8276139e796c95f65cd88fa90849ae9d83fca728", "md5": "f05d598608b50dd31c5598d1f4b11726"}]}]}