{"formatVersion": "1.1", "component": {"group": "org.apache.kafka", "module": "kafka-storage", "version": "3.6.2", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.2.1"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-api"}, "files": [{"name": "kafka-storage-3.6.2.jar", "url": "kafka-storage-3.6.2.jar", "size": 327681, "sha512": "fe1e5f6fd96abb775b3530a40a12f294d381516297613c176d777c6aed699169fa1dc29a5a2ef42cbe7e4fdc06295986865c8cec878bbfb1c7303f64d3fe2d20", "sha256": "fee107617e3d587ad81a3c8e75b1c06eb2852f9a28d4dd5c9bd53ea9116cd6de", "sha1": "3fbacd1fd725cfce6c81001e2fdec76a8bc499c7", "md5": "6b916d9e0d04f521cc24b3520f619249"}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "library", "org.gradle.dependency.bundling": "external", "org.gradle.jvm.version": 8, "org.gradle.libraryelements": "jar", "org.gradle.usage": "java-runtime"}, "dependencies": [{"group": "org.apache.kafka", "module": "kafka-storage-api", "version": {"requires": "3.6.2"}}, {"group": "org.apache.kafka", "module": "kafka-server-common", "version": {"requires": "3.6.2"}}, {"group": "org.apache.kafka", "module": "kafka-clients", "version": {"requires": "3.6.2"}}, {"group": "com.github.ben-manes.caffeine", "module": "caffeine", "version": {"requires": "2.9.3"}}, {"group": "org.slf4j", "module": "slf4j-api", "version": {"requires": "1.7.36"}}, {"group": "com.fasterxml.jackson.core", "module": "jackson-databind", "version": {"requires": "2.13.5"}}, {"group": "com.yammer.metrics", "module": "metrics-core", "version": {"requires": "2.2.0"}}], "files": [{"name": "kafka-storage-3.6.2.jar", "url": "kafka-storage-3.6.2.jar", "size": 327681, "sha512": "fe1e5f6fd96abb775b3530a40a12f294d381516297613c176d777c6aed699169fa1dc29a5a2ef42cbe7e4fdc06295986865c8cec878bbfb1c7303f64d3fe2d20", "sha256": "fee107617e3d587ad81a3c8e75b1c06eb2852f9a28d4dd5c9bd53ea9116cd6de", "sha1": "3fbacd1fd725cfce6c81001e2fdec76a8bc499c7", "md5": "6b916d9e0d04f521cc24b3520f619249"}]}]}