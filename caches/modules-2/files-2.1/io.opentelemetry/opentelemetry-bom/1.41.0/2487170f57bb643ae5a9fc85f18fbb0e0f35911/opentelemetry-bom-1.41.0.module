{"formatVersion": "1.1", "component": {"group": "io.opentelemetry", "module": "opentelemetry-bom", "version": "1.41.0", "attributes": {"org.gradle.status": "release"}}, "createdBy": {"gradle": {"version": "8.9"}}, "variants": [{"name": "apiElements", "attributes": {"org.gradle.category": "platform", "org.gradle.usage": "java-api"}, "dependencyConstraints": [{"group": "io.opentelemetry", "module": "opentelemetry-context", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-opentracing-shim", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-common", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-logging", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-logging-otlp", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-zipkin", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-extension-kotlin", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-extension-trace-propagators", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-logs", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-metrics", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-testing", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-trace", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-extension-autoconfigure", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-extension-autoconfigure-spi", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-extension-jaeger-remote-sampler", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-otlp", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-otlp-common", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-sender-grpc-managed-channel", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-sender-jdk", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-sender-okhttp", "version": {"requires": "1.41.0"}}]}, {"name": "runtimeElements", "attributes": {"org.gradle.category": "platform", "org.gradle.usage": "java-runtime"}, "dependencyConstraints": [{"group": "io.opentelemetry", "module": "opentelemetry-context", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-opentracing-shim", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-api", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-common", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-logging", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-logging-otlp", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-zipkin", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-extension-kotlin", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-extension-trace-propagators", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-common", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-logs", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-metrics", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-testing", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-trace", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-extension-autoconfigure", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-extension-autoconfigure-spi", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-sdk-extension-jaeger-remote-sampler", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-otlp", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-otlp-common", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-sender-grpc-managed-channel", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-sender-jdk", "version": {"requires": "1.41.0"}}, {"group": "io.opentelemetry", "module": "opentelemetry-exporter-sender-okhttp", "version": {"requires": "1.41.0"}}]}]}